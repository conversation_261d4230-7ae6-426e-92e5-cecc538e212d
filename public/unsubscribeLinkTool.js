const React = window.unlayer.React;

const Viewer = (values) => {
  return React.createElement(
    'div',
    { style: { textAlign: 'center', padding: '8px' } },
    React.createElement(
      'a',
      {
        href: values.data.url,
        style: { color: 'gray', fontSize: '14px', textDecoration: 'underline' },
      },
      'Cancelar Inscrição',
    ),
  );
};

unlayer.registerTool({
  name: 'unsubscribe_link',
  label: 'Cancelar Incrição',
  icon: 'fa-ban',
  supportedDisplayModes: ['email'],
  options: {},
  values: {},
  renderer: {
    Viewer: unlayer.createViewer({
      render(values) {
        return `<div style="text-align: center; padding: 8px;">
          <a href="${values.data.url}" style="color: gray; font-size: 14px; text-decoration: underline;">Cancelar Inscrição</a>
        </div>`;
      },
    }),
    exporters: {
      email: function (values) {
        return `<div style="text-align: center; padding: 8px;">
          <a href="${values.data.url}" style="color: gray; font-size: 14px; text-decoration: underline;">Cancelar Inscrição</a>
        </div>`;
      },
      web: function (values) {
        return `<div style="text-align: center; padding: 8px;">
          <a href="${values.data.url}" style="color: gray; font-size: 14px; text-decoration: underline;">Cancelar Inscrição</a>
        </div>`;
      },
    },
  },
});
