this.addEventListener('install', (event) => {
  this.skipWaiting();
});

this.addEventListener('activate', (event) => {
  event.waitUntil(this.clients.claim());
});

this.addEventListener('fetch', (event) => {
  event.respondWith(fetch(event.request));
});

this.addEventListener('push', (event) => {
  let payload = null;
  try {
    payload = event.data.json();
  } catch (error) {
    console.error('Error parsing push event data:', error);
  }

  if (!payload) {
    return;
  }

  if (payload.type === 'notification') {
    event.waitUntil(
      self.registration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon,
        badge: payload.badge,
        tag: payload.tag || payload.body,
        renotify: true,
      }),
    );
  } else {
    console.error('Unknown push type:', payload.type);
  }
});
