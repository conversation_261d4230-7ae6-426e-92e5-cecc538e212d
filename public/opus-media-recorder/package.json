{"name": "opus-media-recorder", "version": "0.8.0", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/kbumsik/opus-media-recorder", "description": "MedialRecorer polyfill for Opus recording using WebAssembly", "main": "OpusMediaRecorder.js", "files": ["OpusMediaRecorder.js", "OpusMediaRecorder.umd.js", "encoderWorker.js", "encoderWorker.umd.js", "OggOpusEncoder.js", "OggOpusEncoder.wasm", "OggOpusEncoder.bin", "WebMOpusEncoder.js", "WebMOpusEncoder.wasm", "WebMOpusEncoder.bin", "WaveEncoder.js", "commonFunctions.js"], "repository": {"type": "git", "url": "https+git://github.com/kbumsik/opus-media-recorder"}, "keywords": ["MediaRecorder", "MediaStream", "Opus", "<PERSON><PERSON>", "WebM", "WebAssembly", "WASM", "PCM", "Decoder", "Stream", "Audio", "Microphone", "Mic", "Recorder", "Recording", "Wav", "Wave"], "scripts": {"build": "make", "build:production": "make PRODUCTION=1", "webpack": "webpack-cli", "serve": "webpack-dev-server", "clean": "make clean"}, "dependencies": {"detect-browser": "^4.1.0", "event-target-shim": "^3.0.2"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/preset-env": "^7.2.3", "babel-loader": "^8.0.5", "eslint": "^5.12.0", "eslint-config-standard": "^11.0.0", "eslint-loader": "^2.1.1", "eslint-plugin-html": "^4.0.6", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^3.1.0", "jsdoc": "^3.5.5", "magic-string": "^0.25.2", "webpack": "^4.28.4", "webpack-cli": "^3.2.1", "webpack-dev-server": "^3.1.14", "wrapper-webpack-plugin": "^2.1.0"}}