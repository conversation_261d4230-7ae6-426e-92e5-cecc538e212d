# Etapa 1: Build (com cache e variáveis de build)
FROM node:20-alpine AS builder

ARG REACT_APP_BACKEND_URL=localhost:3001
ARG REACT_APP_REACT_DISABLE_SSL=true
ARG REACT_APP_ENABLE_DEBUG_TOOLS=false
ARG REACT_APP_FRONTEND_DOMAIN=localhost:3000

ENV REACT_APP_BACKEND_URL=$REACT_APP_BACKEND_URL \
    REACT_APP_REACT_DISABLE_SSL=$REACT_APP_REACT_DISABLE_SSL \
    REACT_APP_ENABLE_DEBUG_TOOLS=$REACT_APP_ENABLE_DEBUG_TOOLS \
    REACT_APP_FRONTEND_DOMAIN=$REACT_APP_FRONTEND_DOMAIN \
    GENERATE_SOURCEMAP=false \
    NODE_OPTIONS=--max_old_space_size=4096

WORKDIR /app

COPY package*.json yarn.lock ./
RUN yarn install --frozen-lockfile

COPY . .

RUN yarn build

# Etapa 2: Runtime com serve (imagem mais enxuta)
FROM node:20-alpine

WORKDIR /app

RUN yarn global add serve

COPY --from=builder /app/build ./build

EXPOSE 3000
CMD ["serve", "-s", "build", "-l", "3000"]
