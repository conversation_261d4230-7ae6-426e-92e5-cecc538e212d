{"name": "whatsapp-tool-frontend", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^2.0.4", "@chakra-ui/react": "^2.8.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11", "@emotion/styled": "^11", "@hookform/resolvers": "^2.9.1", "@reduxjs/toolkit": "^1.8.2", "@rive-app/react-canvas": "^4.18.6", "@sentry/cli": "^2.41.1", "@sentry/react": "^8.54.0", "@tanstack/match-sorter-utils": "^8.8.4", "@tanstack/react-table": "^8.3.2", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/faker": "^6.6.9", "@types/jest": "^27.0.1", "@types/mixpanel-browser": "^2.38.2", "@types/node": "^16.7.13", "@types/react": "^18.0.0", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-datepicker": "^4.4.2", "@types/react-dom": "^18.0.0", "@types/react-redux": "^7.1.24", "@types/socket.io-client": "1.4.36", "axios": "^0.27.2", "chart.js": "^3.8.0", "date-fns": "^2.28.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "emoji-mart": "^5.6.0", "emojibase": "^15.3.1", "emojibase-data": "^15.3.2", "faker": "5.5.3", "framer-motion": "^6", "i18next": "^23.10.1", "mixpanel-browser": "^2.47.0", "opus-media-recorder": "^0.8.0", "query-string": "^8.1.0", "react": "^18.1.0", "react-beautiful-dnd": "^13.1.0", "react-chartjs-2": "^4.2.0", "react-csv": "^2.2.2", "react-datepicker": "^4.16.0", "react-dom": "^18.1.0", "react-dropzone": "^14.2.3", "react-email-editor": "^1.7.11", "react-hook-form": "^7.32.0", "react-i18next": "^14.1.0", "react-icons": "^4.4.0", "react-input-mask": "^2.0.4", "react-query": "^3.39.1", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-select": "^5.7.5", "react-string-replace": "^1.1.0", "react-table": "^7.8.0", "react-virtual": "^2.10.4", "reactflow": "^11.10.2", "socket.io-client": "4.1.2", "typescript": "^4.4.2", "uuid": "^8.3.2", "web-vitals": "^2.1.0", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "scripts": {"start:dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint \"src/**/*.{ts,tsx}\"", "lint:fix": "eslint \"src/**/*.{ts,tsx}\" --fix --debug", "prepare": "husky"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"]}, "eslintConfig": {"extends": ["react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@types/react-csv": "^1.1.3", "@types/react-input-mask": "^3.0.2", "@types/react-table": "^7.7.12", "@types/uuid": "^8.3.4", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "babel-eslint": "^10.1.0", "eslint": "^9.17.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.4.2"}}