# Revi Front

This is the front-end application for Revi, built using [Create React App](https://github.com/facebook/create-react-app).

## 🚀 Getting Started

### Prerequisites

Ensure you have the following installed before running the project:

- [Node.js](https://nodejs.org/) - v20 LTS
- [Yarn](https://yarnpkg.com/)

### Installation

1. Clone the repository:
   ```sh
   git clone https://github.com/marcelo1811/revi-front
   cd revi-front

2. Install packages:
   ```sh
   yarn

### Running

1. dev server
   ```sh
   yarn start:dev