export const dataTestIds = {
  loginPage: {
    loginButton: 'login-button',
    emailInput: 'email',
    passwordInput: 'password',
  },
  inboxPage: {
    newConversationPopover: {
      triggerButton: 'new-conversation-trigger-button',
      phoneInput: 'phone-input',
      popoverNextButton: 'popover-next-button',
      errorMessage: 'popover-error-message',
    },
    chat: {
      customerDetailsButton: 'customer-details-button',
      agentSelect: 'agent-select',
      finishConversationMenu: 'finish-conversation-menu',
      finishConversationWithCsatMenuItem:
        'finish-conversation-with-csat-menu-item',
      finishConversationMenuItem: 'finish-conversation-menu-item',
      finishConversationButton: 'finish-conversation-button',
      inputArea: 'input-area',
      fileInput: 'file-input',
      sendMessageButton: 'send-message-button',
    },
    drawerConversationSettings: {
      triggerButton: 'customer-details-trigger-button',
      editCustomerTab: 'edit-customer-tab',
      categorySelect: 'category-select',
      saveButton: 'save-button',
    },
  },
};
