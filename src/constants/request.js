import axios from 'axios';
import { AuthService } from '../services/auth.service';
import { showToast } from '../state/globalSlice';
import { store } from '../state/store';
import { baseURL } from './base-url';
import { baseIntegratorURL } from './integrator-base-url';
import { protocol } from './protocol';
import { ErrorHandleUtils } from '../utils/errorHandler.utils';

// TODO: ADD ABSOLUTEURL TO ENV VAR
const shownErrors = new Set();

function handleError(message) {
  const sanitizedErrorMessage = ErrorHandleUtils.sanitizeErrorMessage(message);
  if (ErrorHandleUtils.isDuplicateError(sanitizedErrorMessage, shownErrors))
    return;

  shownErrors.add(sanitizedErrorMessage);
  store.dispatch(
    showToast({
      title: sanitizedErrorMessage,
      status: 'error',
      duration: 3000,
      isClosable: true,
    }),
  );
  setTimeout(() => shownErrors.delete(sanitizedErrorMessage), 3000);
}

const getRequest = ({
  useIntegratorBackendUrl = false,
  informedBaseURL = baseURL,
} = {}) => {
  const axiosInstance = axios.create({
    baseURL: `${protocol}://${useIntegratorBackendUrl ? baseIntegratorURL : informedBaseURL}`,
    timeout: 60000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  axiosInstance.interceptors.request.use(function (config) {
    const accessToken = AuthService.getAccessToken();
    config.headers.Authorization = accessToken ? `Bearer ${accessToken}` : '';
    return config;
  });

  axiosInstance.interceptors.response.use(
    (res) => {
      return res;
    },
    (err) => {
      if (
        [401].includes(err?.response?.status) &&
        !err?.response?.data?.message?.includes('Usuário')
      ) {
        AuthService.logout();
        if (!window.location.pathname.includes('cancelar-inscricao')) {
          handleError('Sessão expirada');
        }
      } else if (err?.response?.status === 400) {
        handleError(err.response.data.message);
      } else if ([504, 0].includes(err?.response?.status)) {
        return;
      } else {
        handleError(err.response.data.message);
      }
      return Promise.reject(err);
    },
  );
  return axiosInstance;
};

export const request = getRequest();
export const integratorRequest = getRequest({
  useIntegratorBackendUrl: true,
});
export const agentRequest = getRequest({
  informedBaseURL: process.env.REACT_APP_AGENT_BASE_URL,
});
