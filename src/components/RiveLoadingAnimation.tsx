import React, { useState, useEffect } from 'react';
import { Flex } from '@chakra-ui/react';
import Rive from '@rive-app/react-canvas';

interface RiveLoadingAnimationProps {
  isLoading: boolean;
  hasResults: boolean;
  height?: string | number;
  width?: string | number;
  animationFile?: string;
  onLoadingComplete?: () => void;
  successAnimationDuration?: number;
}

const RiveLoadingAnimation = ({
  isLoading,
  hasResults,
  height = '100px',
  width = '100%',
  animationFile = '/animations/rive/ai-generate-loading.riv',
  onLoadingComplete,
  successAnimationDuration = 1450,
}: RiveLoadingAnimationProps) => {
  const [showLoading, setShowLoading] = useState(true);
  const [currentArtBoard, setCurrentArtBoard] = useState('Magic');

  useEffect(() => {
    if (hasResults && !isLoading) {
      setCurrentArtBoard('Tick blue');

      const hideTimeout = setTimeout(() => {
        setShowLoading(false);
        onLoadingComplete?.();
      }, successAnimationDuration);

      return () => clearTimeout(hideTimeout);
    } else if (isLoading) {
      setShowLoading(true);
      setCurrentArtBoard('Magic');
    }
  }, [isLoading, hasResults, onLoadingComplete, successAnimationDuration]);

  if (!isLoading && !hasResults) {
    return null;
  }

  if (!showLoading) {
    return null;
  }

  return (
    <Flex height={height} width={width} align="center" justify="center">
      <Rive
        key={currentArtBoard}
        src={animationFile}
        artboard={currentArtBoard}
        stateMachines="State appear"
      />
    </Flex>
  );
};

export default RiveLoadingAnimation;
