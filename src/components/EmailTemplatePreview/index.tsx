import React from 'react';
import {
  Box,
  Card,
  CardBody,
  Text,
  VStack,
  useColorModeValue,
  Flex,
} from '@chakra-ui/react';
import { EmailTemplate } from '../../types/Prisma';

interface EmailTemplatePreviewProps {
  emailTemplate: EmailTemplate;
  onSelect?: (template: EmailTemplate) => void;
  selectedEmailTemplate?: EmailTemplate | null;
}

const EmailTemplatePreview = ({
  emailTemplate,
  onSelect,
  selectedEmailTemplate,
}: EmailTemplatePreviewProps) => {
  const bgHover = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Card
      key={emailTemplate.id}
      variant="outline"
      cursor={onSelect ? 'pointer' : 'default'}
      _hover={{ bg: bgHover }}
      onClick={() => onSelect?.(emailTemplate)}
      borderColor={borderColor}
      maxW="400px"
      minWidth={'-webkit-fit-content'}
    >
      <CardBody p={3}>
        <VStack align="stretch" spacing={2}>
          <Flex justify="space-between">
            <Text>{emailTemplate.name}</Text>
            <Flex gap={1} align="center">
              <Text fontSize="xs" color="gray.500">
                Assunto do Email:
              </Text>
              <Text fontSize="xs" fontWeight="medium">
                {emailTemplate.subject}
              </Text>
            </Flex>
          </Flex>
          <Box>
            <Text fontSize="xs" color="gray.500" mb={1}>
              Preview
            </Text>
            <Box
              width="375px"
              height="600px"
              overflow="auto"
              border="1px solid #ccc"
              borderRadius="10px"
            >
              <iframe
                srcDoc={emailTemplate.html}
                width="100%"
                height="100%"
                style={
                  selectedEmailTemplate
                    ? { border: 'none' }
                    : { border: 'none', pointerEvents: 'none' }
                }
              />
            </Box>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default EmailTemplatePreview;
