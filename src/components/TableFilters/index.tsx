import {
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { ParseFilterCriteria } from '../../pages/CampaignsPage/WhatsappCampaignsPage/WhatsappCampaignDetailsPage';
import { CustomerFilterUtils } from '../../utils/customer-filter.utils';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../constants/api-routes';

interface TableFiltersProps {
  filterCriteria: string | null;
}

const TableFilters = ({ filterCriteria }: TableFiltersProps) => {
  const { data: transformedFilterCriteria } = useQuery(
    ['parseFilterCriteria', filterCriteria],
    async () => {
      const parsedCriteria = await CustomerFilterUtils.parseFilterCriteriaAsync(
        filterCriteria!,
      );

      return parsedCriteria;
    },
    {
      enabled: !!filterCriteria,
    },
  );

  return (
    <TableContainer mt={2}>
      <Table variant="striped" size={'sm'}>
        <Thead>
          <Tr>
            <Th>Filtro</Th>
            <Th>Valor</Th>
          </Tr>
        </Thead>
        <Tbody>
          {transformedFilterCriteria?.map((filter, index) => (
            <Tr key={index}>
              <Td>{filter.title}</Td>
              <Td>{filter.value}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default TableFilters;
