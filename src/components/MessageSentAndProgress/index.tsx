import { Box, Flex, Progress, Text } from '@chakra-ui/react';
import { CompanyMessageBalanceByCategoryResponse } from '../../types/CompanyMessageBalanceByCategoryResponse';

interface MessageSentAndProgressProps {
  text: string;
  data: CompanyMessageBalanceByCategoryResponse;
  field: 'daily' | 'marketing' | 'utility' | 'service';
}

const MessageSentAndProgress = ({
  text,
  data,
  field,
}: MessageSentAndProgressProps) => {
  return (
    <Box>
      <Flex justify={'space-between'}>
        <Text>{text}</Text>
        <Text
          color={data[field].sent > data[field].total ? 'red.500' : 'black'}
        >
          {data[field].sent}/{data[field].total}
        </Text>
      </Flex>
      <Progress
        value={(data[field].sent / data[field].total) * 100}
        size="sm"
        colorScheme="blue"
        borderRadius={'10px'}
      />
    </Box>
  );
};

export default MessageSentAndProgress;
