import React from 'react';
import {
  Box,
  Flex,
  Text,
  Avatar,
  Tag,
  TagLabel,
  TagLeftIcon,
  Button,
  IconButton,
  useDisclosure,
  Collapse,
} from '@chakra-ui/react';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  EditIcon,
  DeleteIcon,
} from '@chakra-ui/icons';
import { colors } from '../../constants/colors';
import { IconType } from 'react-icons/lib';

interface TagItem {
  label: string;
  icon?: IconType;
  bgColor: string;
  color: string;
}

interface EntityCardProps {
  title: string;
  subtitle: string;
  iconLetter: string;
  iconBgColor: string;
  iconColor: string;
  tags?: TagItem[];
  detailsTitle?: string;
  detailsContent?: React.ReactNode;
  onEdit?: () => void;
  onDelete?: () => void;
  onToggleDetails?: () => void;
  isCustomHeader?: boolean;
  customHeader?: React.ReactNode;
  hideDetailsButton?: boolean;
  disableDelete?: boolean;
  hideDetails?: boolean;
}

const EntityCard: React.FC<EntityCardProps> = ({
  title,
  subtitle,
  iconLetter,
  iconBgColor,
  iconColor,
  tags = [],
  detailsTitle,
  detailsContent,
  onEdit,
  onDelete,
  onToggleDetails,
  isCustomHeader = false,
  customHeader,
  hideDetailsButton = false,
  disableDelete = false,
  hideDetails = false,
}) => {
  const { isOpen, onToggle } = useDisclosure();

  const handleToggle = () => {
    onToggle();
    if (onToggleDetails) {
      onToggleDetails();
    }
  };

  const shouldShowDetailsButton =
    !hideDetailsButton && detailsContent && !hideDetails;

  return (
    <Box
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      mb={4}
      boxShadow="sm"
      bg="white"
    >
      {isCustomHeader ? (
        customHeader
      ) : (
        <Flex p={4} justifyContent="space-between" alignItems="center">
          <Flex alignItems="center">
            <Avatar
              name={iconLetter}
              bg={iconBgColor}
              color={iconColor}
              size="md"
              mr={4}
            />
            <Box>
              <Text fontWeight="bold" fontSize="lg">
                {title}
              </Text>
              <Text fontSize="sm" color="gray.600">
                {subtitle}
              </Text>
              {tags.length > 0 && (
                <Flex mt={2} flexWrap="wrap" gap={2}>
                  {tags.map((tag, index) => (
                    <Tag
                      key={index}
                      size="sm"
                      bgColor={tag.bgColor}
                      color={tag.color}
                      borderRadius="full"
                    >
                      {tag.icon && <TagLeftIcon as={tag.icon} boxSize="12px" />}
                      <TagLabel>{tag.label}</TagLabel>
                    </Tag>
                  ))}
                </Flex>
              )}
            </Box>
          </Flex>
          <Flex>
            {shouldShowDetailsButton && (
              <Button
                rightIcon={isOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
                variant="ghost"
                size="sm"
                onClick={handleToggle}
                mr={2}
                id={isOpen ? 'fechar-detalhes' : 'ver-detalhes'}
              >
                {isOpen ? 'Ocultar detalhes' : 'Ver detalhes'}
              </Button>
            )}
            <IconButton
              icon={<EditIcon color={colors.darkGrey} />}
              aria-label="Edit"
              variant="ghost"
              size="sm"
              onClick={onEdit}
              mr={2}
            />
            <IconButton
              icon={
                <DeleteIcon
                  color={disableDelete ? 'gray.300' : colors.darkGrey}
                />
              }
              aria-label="Delete"
              variant="ghost"
              size="sm"
              onClick={disableDelete ? undefined : onDelete}
              isDisabled={disableDelete}
              cursor={disableDelete ? 'not-allowed' : 'pointer'}
            />
          </Flex>
        </Flex>
      )}

      {detailsContent && !hideDetails && (
        <Collapse in={isOpen} animateOpacity>
          <Box p={4} bg="gray.50" borderTop="1px" borderColor="gray.100">
            {detailsTitle && (
              <Text fontWeight="medium" mb={3}>
                {detailsTitle}
              </Text>
            )}
            {detailsContent}
          </Box>
        </Collapse>
      )}
    </Box>
  );
};

export default EntityCard;
