import {
  <PERSON>,
  <PERSON>lex,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Text,
  useDisclosure,
  Collapse,
  Icon,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r,
  IconButton,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { MdKeyboardArrowRight, MdKeyboardArrowDown } from 'react-icons/md';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { selectSidebarState, toggleSidebar } from '../../state/inboxSlice';
import { useDispatch, useSelector } from 'react-redux';
import { scrollbarStyles } from '../../styles/scrollbar.styles';

interface SiderItemProps {
  title?: string;
  icon?: React.ReactNode;
  rightElement?: React.ReactNode;
  leftElement?: React.ReactNode;
  onClick: () => void;
  contextMenuItems?: {
    title: string;
    onClick: () => void;
  }[];
  isHidden?: boolean;
  children?: SiderItemProps[];
}

interface SiderProps {
  topElement?: React.ReactNode;
  width?: string;
  activeTab: string;
  items: SiderItemProps[];
  headerActions?: React.ReactNode;
  showToggleButton?: boolean;
}

export const SectionTitle = ({
  children,
  isCollapsed,
}: {
  children: React.ReactNode;
  isCollapsed: boolean;
}) => {
  if (isCollapsed) return null;

  return (
    <Text
      fontSize="xs"
      fontWeight="medium"
      color="gray.500"
      textTransform="uppercase"
      px={2}
      mb={2}
    >
      {children}
    </Text>
  );
};

const SiderItem = ({
  activeTab,
  title,
  icon,
  onClick,
  children,
  rightElement,
  leftElement,
  contextMenuItems,
  isCollapsed,
  isHidden,
}: SiderItemProps & {
  activeTab: string;
  isCollapsed: boolean;
}) => {
  const hasChildren = children && children.length > 0;
  const isActive = activeTab === title;
  const contextMenu = useDisclosure();
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const textColor = 'gray.800';

  const handleClick = () => {
    if (hasChildren) {
      setIsOpen(!isOpen);
    }
    onClick();
  };

  const handleContextMenu = (event: React.MouseEvent) => {
    if (contextMenuItems?.length) {
      event.preventDefault();
      setPosition({ x: event.pageX, y: event.pageY });
      contextMenu.onOpen();
    }
  };

  if (isHidden) return null;

  return (
    <Box width="100%">
      {contextMenuItems && contextMenu.isOpen && (
        <Menu isOpen={true} onClose={contextMenu.onClose}>
          <MenuButton
            as={Box}
            position="absolute"
            left={`${position.x}px`}
            top={`${position.y}px`}
          />
          <MenuList zIndex="popover">
            {contextMenuItems.map(({ title, onClick }) => (
              <MenuItem key={title} onClick={onClick}>
                {title}
              </MenuItem>
            ))}
          </MenuList>
        </Menu>
      )}

      <Tooltip label={isCollapsed ? title : ''} placement="right" hasArrow>
        <Stack
          spacing={0}
          onClick={handleClick}
          cursor="pointer"
          borderRadius="md"
          onContextMenu={handleContextMenu}
          bg={isActive ? 'blue.50' : 'transparent'}
          _hover={{ bg: 'gray.100' }}
          transition="all 0.3s"
        >
          <Flex
            align="center"
            p={2}
            borderRadius="md"
            role="group"
            justify={isCollapsed ? 'center' : 'space-between'}
            minH="40px"
          >
            <Flex align="center" gap={3} flex={isCollapsed ? '0 0 auto' : 1}>
              {leftElement && (
                <Box
                  mr={isCollapsed ? 0 : 2}
                  color={isActive ? 'blue.500' : textColor}
                  _groupHover={{ color: 'blue.500' }}
                  fontSize={isCollapsed ? '20px' : '16px'}
                >
                  {leftElement}
                </Box>
              )}
              {icon && (
                <Box
                  color={isActive ? 'blue.500' : textColor}
                  _groupHover={{ color: 'blue.500' }}
                  fontSize={isCollapsed ? '20px' : '16px'}
                >
                  {icon}
                </Box>
              )}
              {!isCollapsed && (
                <Text
                  color={isActive ? 'blue.500' : textColor}
                  fontWeight={hasChildren ? 'semibold' : 'medium'}
                  fontSize="sm"
                  _groupHover={{ color: 'blue.500' }}
                  noOfLines={1}
                >
                  {title}
                </Text>
              )}
            </Flex>

            {!isCollapsed && (
              <Flex align="center" gap={2}>
                {rightElement}
                {hasChildren && (
                  <Icon
                    as={isOpen ? MdKeyboardArrowDown : MdKeyboardArrowRight}
                    w={4}
                    h={4}
                    transition="all 0.3s"
                  />
                )}
              </Flex>
            )}
          </Flex>
        </Stack>
      </Tooltip>

      {hasChildren && !isCollapsed && (
        <Collapse in={isOpen} animateOpacity>
          <Stack pl={4} mt={1} spacing={1}>
            {children.map((child) => (
              <SiderItem
                key={child.title}
                activeTab={activeTab}
                isCollapsed={isCollapsed}
                {...child}
              />
            ))}
          </Stack>
        </Collapse>
      )}
    </Box>
  );
};

const Sider = ({
  items,
  activeTab,
  width = '280px',
  topElement,
  showToggleButton = true,
  headerActions = null,
}: SiderProps) => {
  const dispatch = useDispatch();
  const isCollapsed = useSelector(selectSidebarState);
  const bgColor = 'gray.50';
  const mainItems = items.slice(0, 4);
  const otherItems = items.slice(4);
  const collapsedWidth = '64px';
  const expandedWidth = width;

  return (
    <Box position="relative">
      <Stack
        minW={isCollapsed ? collapsedWidth : expandedWidth}
        maxW={isCollapsed ? collapsedWidth : expandedWidth}
        w={isCollapsed ? collapsedWidth : expandedWidth}
        h="100vh"
        pt={topElement ? 0 : 5}
        px={isCollapsed ? 2 : 5}
        pb={5}
        bg={bgColor}
        overflowY="auto"
        overflowX="hidden"
        spacing={4}
        borderRight="1px"
        borderColor="gray.200"
        transition="all 0.3s"
        css={scrollbarStyles({ width: '4px' })}
      >
        {topElement}

        <Stack spacing={4}>
          <Stack spacing={2}>
            {headerActions}

            {mainItems.map((item) => (
              <SiderItem
                key={item.title}
                activeTab={activeTab}
                isCollapsed={isCollapsed}
                {...item}
              />
            ))}
          </Stack>

          <Divider />

          <Stack spacing={1}>
            {otherItems.map((item) => (
              <SiderItem
                key={item.title}
                activeTab={activeTab}
                isCollapsed={isCollapsed}
                {...item}
              />
            ))}
          </Stack>
        </Stack>
      </Stack>
      {showToggleButton && (
        <Box
          position="absolute"
          top="35px"
          transform="translateY(-50%)"
          right={isCollapsed ? '0' : '15px'}
          zIndex={2}
          boxSize={12}
          display="flex"
          justifyContent="center"
          alignItems="center"
        >
          <Tooltip
            label={isCollapsed ? 'Expandir' : 'Ocultar'}
            aria-label="Botão para expandir ou ocultar sidebar"
          >
            <IconButton
              aria-label={isCollapsed ? 'Expandir sidebar' : 'Ocultar sidebar'}
              icon={isCollapsed ? <FiChevronRight /> : <FiChevronLeft />}
              size="sm"
              width="36px"
              height="36px"
              borderRadius="full"
              boxShadow="lg"
              onClick={() => dispatch(toggleSidebar())}
              bg={bgColor}
              _hover={{
                bg: 'gray.100',
                transform: 'scale(1.1)',
                transition: 'all 0.3s',
              }}
              _focus={{ boxShadow: '0 0 0 2px rgba(0, 0, 0, 0.2)' }}
            />
          </Tooltip>
        </Box>
      )}
    </Box>
  );
};

export default Sider;
