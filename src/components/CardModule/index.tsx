import { Box, Text, Checkbox, Flex } from '@chakra-ui/react';

interface CardModuleProps {
  icon: React.ElementType;
  label: string;
  isChecked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const CardModule = ({
  icon: IconComponent,
  label,
  isChecked,
  onChange,
}: CardModuleProps) => {
  const variant = 'outline';

  return (
    <Box
      as="label"
      borderWidth="1px"
      borderRadius="md"
      p={4}
      position="relative"
      borderColor={isChecked ? 'blue.500' : 'gray.200'}
      bg="white"
      cursor="pointer"
      _hover={{
        bg: 'gray.50',
        borderColor: 'blue.300',
      }}
    >
      <Flex align="center">
        <Box color="gray.500" mr={3}>
          <IconComponent size={20} variant={variant} />
        </Box>
        <Text fontWeight="medium" flex="1">
          {label}
        </Text>
        <Checkbox
          isChecked={isChecked}
          onChange={onChange}
          colorScheme="blue"
          pointerEvents="none"
        />
      </Flex>
    </Box>
  );
};

export default CardModule;
