import {
  Flex,
  <PERSON>ing,
  Text,
  VStack,
  Icon,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Divider,
  Toolt<PERSON>,
} from '@chakra-ui/react';
import { useLocation, useNavigate } from 'react-router-dom';
import { colors } from '../../constants/colors';
import { useAppModuleAccessGuard } from '../../hooks/useAppModuleAccessGuard';
import { scrollbarStyles } from '../../styles/scrollbar.styles';
import { IconType } from 'react-icons/lib';
import { useState } from 'react';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

interface SidebarSecondaryProps {
  title: string;
  options: {
    title: string;
    path: string;
    icon: IconType;
  }[];
}

const SidebarSecondary = ({ title, options }: SidebarSecondaryProps) => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  function isActivePath(path: string) {
    return location.pathname.includes(path);
  }

  const handleToggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <Flex
      flexDir="column"
      bgColor="white"
      height="100vh"
      maxHeight="100vh"
      borderRight="1px solid #E0E0E0"
      position="relative"
      width={isCollapsed ? '80px' : '250px'}
      transition="width 0.3s ease"
      overflowY="auto"
      css={scrollbarStyles({ background: 'white' })}
    >
      <Flex
        paddingY={6}
        paddingX={3}
        position="sticky"
        top={0}
        bgColor="white"
        zIndex={2}
        justifyContent="space-between"
        alignItems="center"
        pb={0}
      >
        {!isCollapsed && (
          <Heading
            size="md"
            alignSelf="flex-start"
            bgColor={colors.black}
            bgClip="text"
            noOfLines={2}
            p={1}
          >
            {title}
          </Heading>
        )}
        <IconButton
          icon={isCollapsed ? <FiChevronRight /> : <FiChevronLeft />}
          aria-label={isCollapsed ? 'Expandir' : 'Ocultar'}
          variant="ghost"
          size="sm"
          onClick={handleToggleSidebar}
          ml={isCollapsed ? 'auto' : 0}
          mr={isCollapsed ? 'auto' : 0}
          color={colors.primary}
          _hover={{ bg: 'gray.100' }}
        />
      </Flex>

      <Divider my={4} borderColor="#E0E0E0" />

      <VStack
        spacing={2}
        width="100%"
        paddingY={2}
        overflowY="auto"
        css={scrollbarStyles({ width: '6px', background: colors.lightGrey })}
        height="calc(100vh - 85px)"
      >
        {options.map((option, index) => {
          if (!checkUserHasPathAccess(option.path)) return null;

          const isActive = isActivePath(option.path);

          return (
            <Flex
              key={`sidebar-option-${index}`}
              paddingY={2}
              paddingX={4}
              bgColor={isActive ? '#E3EFFF' : 'transparent'}
              borderRadius="md"
              _hover={{
                bgColor: isActive ? 'none' : colors.lightGrey,
              }}
              cursor="pointer"
              width="90%"
              onClick={() => navigate(option.path)}
              alignItems="center"
              justifyContent={isCollapsed ? 'center' : 'flex-start'}
              role="group"
              gap={isCollapsed ? 0 : 4}
              transition="background-color 0.2s ease, border 0.2s ease"
            >
              <Icon
                as={option.icon}
                boxSize={5}
                color={isActive ? colors.primary : colors.black}
              />
              {!isCollapsed && (
                <Tooltip
                  label={option.title}
                  hasArrow
                  isDisabled={option.title.length < 25}
                >
                  <Text
                    fontSize="sm"
                    fontWeight="normal"
                    color={isActive ? colors.primary : colors.black}
                    noOfLines={2}
                  >
                    {option.title}
                  </Text>
                </Tooltip>
              )}
            </Flex>
          );
        })}
      </VStack>
    </Flex>
  );
};

export default SidebarSecondary;
