import {
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from '@chakra-ui/react';
import React from 'react';
import { colors } from '../../constants/colors';

interface CrudDrawerProps {
  isLoading: boolean;
  isOpen: boolean;
  onClose: () => void;
  title: string;
  actionButtonText: string;
  children: React.ReactNode;
  formId?: string;
  onClickDelete?: () => void;
  canDelete?: boolean;
  buttonDeleteText?: string;
  onClickSubmit?: () => void;
  canSubmit?: boolean;
}

const CrudDrawer = ({
  isLoading,
  isOpen,
  onClose,
  actionButtonText,
  title,
  children,
  formId,
  onClickDelete,
  canDelete,
  buttonDeleteText = 'Deletar',
  onClickSubmit,
  canSubmit = true,
}: CrudDrawerProps) => {
  return (
    <Drawer isOpen={isOpen} placement="right" onClose={onClose} size={'md'}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader>{title}</DrawerHeader>

        <DrawerBody>{children}</DrawerBody>

        <DrawerFooter>
          {onClickDelete && (
            <Button
              variant="outline"
              mr={3}
              onClick={onClickDelete}
              disabled={!canDelete}>
              {buttonDeleteText}
            </Button>
          )}
          {onClickSubmit ? (
            <Button
              isLoading={isLoading}
              variant='primary'
              onClick={onClickSubmit}
              isDisabled={!canSubmit}>
              {actionButtonText}
            </Button>
          ) : (
            <Button
              isLoading={isLoading}
              variant='primary'
              form={formId}
              type="submit"
              isDisabled={!canSubmit}>
              {actionButtonText}
            </Button>
          )}
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default CrudDrawer;
