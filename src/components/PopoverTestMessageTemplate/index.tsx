import React, { <PERSON>actNode, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>overTrigger,
  PopoverArrow,
  PopoverCloseButton,
  PopoverContent,
  Text,
} from '@chakra-ui/react';
import {
  Stack,
  FormControl,
  FormLabel,
  Input,
  InputLeftAddon,
  InputGroup,
  ButtonGroup,
  Button,
} from '@chakra-ui/react';
import { FocusLock } from '@chakra-ui/react';
import { ReactInputMask } from 'react-input-mask';
import {
  MessagesService,
  SendMessageTemplateByPhoneDto,
} from '../../services/messages.service';
import { useMutation } from 'react-query';
import { MessageTemplateUtils } from '../../utils/message-templates.utils';
import { MessageTemplate } from '../../types/MessageTemplate';
import { StringUtils } from '../../utils/string.utils';
import { colors } from '../../constants/colors';

const MESSAGE_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  NONE: null,
} as const;

type MessageStatusType = (typeof MESSAGE_STATUS)[keyof typeof MESSAGE_STATUS];

export interface PopoverTestMessageTemplateProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  template: MessageTemplate;
  children?: ReactNode;
}

const PopoverTestMessageTemplate = ({
  isOpen,
  onOpen,
  onClose,
  template,
  children,
}: PopoverTestMessageTemplateProps) => {
  const [recipientName, setRecipientName] = useState<string>('');
  const [recipientPhoneNumberId, setRecipientPhoneNumberId] =
    useState<string>('');
  const [messageStatus, setMessageStatus] = useState<MessageStatusType>(
    MESSAGE_STATUS.NONE,
  );
  const sendMessageTemplateByPhone = useMutation(
    (sendMessageTemplateByPhoneDto: SendMessageTemplateByPhoneDto) =>
      MessagesService.sendMessageTemplateByPhone(sendMessageTemplateByPhoneDto),
    {
      onSuccess: (res: any) => {
        setMessageStatus(MESSAGE_STATUS.SUCCESS);
      },
      onError: (error: any) => {
        setMessageStatus(MESSAGE_STATUS.ERROR);
      },
    },
  );

  useEffect(() => {
    if (isOpen) {
      setRecipientName('');
      setRecipientPhoneNumberId('');
      setMessageStatus(MESSAGE_STATUS.NONE);
    }
  }, [isOpen]);

  useEffect(() => {
    if (messageStatus === MESSAGE_STATUS.SUCCESS) {
      const oneSecondInMilliseconds = 1000;
      const timeoutId = setTimeout(() => onClose(), oneSecondInMilliseconds);

      return () => clearTimeout(timeoutId);
    }
  }, [messageStatus, onClose]);

  async function handleSubmit() {
    const parameters = MessageTemplateUtils.getAllParametersInText(
      template.templateText,
    );

    const templateArgs: Record<string, string> = {};
    parameters.forEach((parameter) => {
      let formatted = parameter.replace(/[\[|\]]/g, '');
      formatted = StringUtils.toCamelCase(formatted);
      templateArgs[formatted] = parameter;
    });

    await sendMessageTemplateByPhone.mutateAsync({
      recipientName,
      recipientPhoneNumberId,
      templateName: template.name,
      templateArgs,
    });
  }

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      placement="right"
      closeOnBlur={true}
    >
      <PopoverTrigger>{children}</PopoverTrigger>
      <PopoverContent p={5}>
        <FocusLock persistentFocus={false}>
          <PopoverArrow />
          <PopoverCloseButton />
          <Stack>
            <FormControl>
              <FormLabel>Nome</FormLabel>
              <Input
                value={recipientName}
                onChange={(e) => setRecipientName(e.target.value)}
              />
            </FormControl>
            <FormControl>
              <FormLabel>Telefone</FormLabel>
              <InputGroup>
                <InputLeftAddon children="+55" />
                <Input
                  type="tel"
                  placeholder="(11) 00000-0000"
                  as={ReactInputMask}
                  mask="(99)99999-9999"
                  value={recipientPhoneNumberId}
                  onChange={(e) => setRecipientPhoneNumberId(e.target.value)}
                />
              </InputGroup>
            </FormControl>
            {messageStatus === MESSAGE_STATUS.SUCCESS ? (
              <Text color="green.500">Mensagem enviada com sucesso</Text>
            ) : messageStatus === MESSAGE_STATUS.ERROR ? (
              <Text color="red.500">Erro ao enviar mensagem</Text>
            ) : null}
            <ButtonGroup display="flex" justifyContent="flex-end">
              <Button variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button
                color={colors.white}
                bgColor={colors.primary}
                onClick={handleSubmit}
                isLoading={sendMessageTemplateByPhone.isLoading}
                isDisabled={!recipientName || !recipientPhoneNumberId}
              >
                Enviar
              </Button>
            </ButtonGroup>
          </Stack>
        </FocusLock>
      </PopoverContent>
    </Popover>
  );
};

export default PopoverTestMessageTemplate;
