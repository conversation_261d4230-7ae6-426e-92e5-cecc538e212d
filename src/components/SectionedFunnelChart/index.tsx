import { Box, Flex } from '@chakra-ui/react';
import ChartSection from './ChartSection';

export interface SectionedFunnelChartData {
  barValue: number;
  sectionTitle: string;
  funnelValue: number;
  barTooltip: string;
  funnelTooltip: string;
  onClick?: () => void;
}
interface SectionedFunnelChartProps {
  data: SectionedFunnelChartData[];
  maxSections: number;
  barColor: string;
  funnelColor: string;
  funnelValueFormatter: (value: number) => string;
  barValueFormatter: (value: number) => string;
  width: number;
  height: number;
  lastSectionTitle?: string;
}

export default function SectionedFunnelChart({
  data,
  barColor,
  funnelColor,
  funnelValueFormatter,
  barValueFormatter,
  width,
  height,
  maxSections,
  lastSectionTitle,
}: SectionedFunnelChartProps) {
  const sectionMaxHeight = height - 150;
  const numberOfSections = data.length;
  const sectionWidth = width / numberOfSections;
  const maxValue = data.reduce((max, item) => Math.max(max, item.barValue), 0);

  const formattedData = data.reduce(
    (prev, curr, index) => {
      let showFunnel = true;
      const nextItem = data[index + 1];
      if (index + 1 > maxSections) {
        const lastItem = prev[prev.length - 1];
        if (lastSectionTitle) {
          lastItem.sectionTitle = lastSectionTitle;
        }
        lastItem.showFunnel = false;
        return prev;
      }
      return [
        ...prev,
        {
          ...curr,
          startValue: curr.barValue,
          endValue: nextItem?.barValue,
          showFunnel,
        },
      ];
    },
    [] as (SectionedFunnelChartData & {
      startValue: number;
      endValue: number;
      showFunnel: boolean;
    })[],
  );

  return (
    <Box padding="20px" overflowX="scroll">
      <Flex width={`${width}px`} height={`${height}px`}>
        {formattedData.map(
          (
            {
              sectionTitle,
              funnelValue,
              onClick,
              startValue,
              endValue,
              showFunnel,
              barTooltip,
              funnelTooltip,
            },
            index,
          ) => {
            return (
              <ChartSection
                barTooltip={barTooltip}
                funnelTooltip={funnelTooltip}
                maxValue={maxValue}
                maxHeight={sectionMaxHeight}
                barColor={barColor}
                funnelColor={funnelColor}
                sectionTitle={sectionTitle}
                barValue={startValue}
                funnelValue={funnelValue}
                barValueFormatter={barValueFormatter}
                funnelValueFormatter={funnelValueFormatter}
                funnelWidth={(funnelValue / 100) * sectionWidth}
                startValue={startValue}
                endValue={endValue}
                onClick={onClick}
                showFunnel={showFunnel}
              />
            );
          },
        )}
      </Flex>
    </Box>
  );
}
