import { Box, Flex, Text, Tooltip } from '@chakra-ui/react';

function minMaxScaler(value: number, min: number, max: number): number {
  if (min === max) {
    throw new Error('Min and max values cannot be the same.');
  }
  return (value - min) / (max - min);
}

function getScaledValue(value: number, maxValue: number, multiplier: number) {
  return minMaxScaler(value, 0, maxValue) * multiplier;
}

interface ChartSectionProps {
  barValueFormatter: (value: number) => string;
  funnelValueFormatter: (value: number) => string;
  sectionTitle: string;
  barColor: string;
  funnelColor: string;
  barValue: number;
  maxValue: number;
  startValue: number;
  endValue: number;
  funnelWidth: number;
  funnelValue: number;
  showFunnel?: boolean;
  maxHeight: number;
  onClick?: () => void;
  barTooltip: string;
  funnelTooltip: string;
}

export default function ChartSection({
  sectionTitle,
  barValue,
  barColor,
  funnelValueFormatter,
  barValueFormatter,
  funnelColor,
  maxValue,
  startValue,
  endValue,
  funnelWidth,
  funnelValue,
  showFunnel,
  maxHeight,
  onClick,
  barTooltip,
  funnelTooltip,
}: ChartSectionProps) {
  const scaledStartValue = getScaledValue(startValue, maxValue, maxHeight);
  const scaledEndValue = getScaledValue(endValue, maxValue, maxHeight);
  const rightHeightDiscount = scaledStartValue - scaledEndValue;
  const limitedFunnelWidth = Math.min(Math.max(funnelWidth * 50, 100), 250);

  function percentageFormatter(value: number) {
    return Number((value * 100).toFixed(2)) + '%';
  }

  const percentage = percentageFormatter(barValue / maxValue);
  return (
    <Flex
      border="1px dotted"
      borderColor="gray.200"
      flexDirection="column"
      justifyContent="space-between"
      height="100%"
    >
      <Box padding="20px">
        <Text fontSize="14px" color="gray.500">
          {sectionTitle}
        </Text>
        <Text fontSize="18px" fontWeight="bold" color="gray.500">
          {barValueFormatter(barValue)}
        </Text>
      </Box>
      <Flex onClick={onClick}>
        <Tooltip label={barTooltip} aria-label="A tooltip" placement="top">
          <Box
            _hover={{
              cursor: 'pointer',
              transition: 'transform 0.2s',
              opacity: 0.5,
            }}
            width="60px"
            height={`${scaledStartValue}px`}
            bg={barColor}
            borderTopRadius="md"
          />
        </Tooltip>

        {showFunnel && (
          <Tooltip label={funnelTooltip} placement="bottom">
            <Box position="relative">
              <Box
                width={`${limitedFunnelWidth}px`}
                height={`${scaledStartValue}px`}
                bg={funnelColor}
                clipPath={`polygon(0px 0px, ${limitedFunnelWidth}px ${rightHeightDiscount}px, ${limitedFunnelWidth}px ${scaledStartValue}px, 0px ${scaledStartValue}px)`}
                borderTopRadius="md"
                _hover={{
                  cursor: 'pointer',
                  transition: 'transform 0.2s',
                  transform: 'scale(1.05)',
                }}
                overflow="visible"
              />
              <Tooltip placement="top" label={`${percentage} do total`}>
                <Box
                  bgColor="gray.500"
                  paddingX={3}
                  paddingY={1}
                  borderRadius="md"
                  position="absolute"
                  bottom={'30px'}
                  left="50%"
                  transform="translateX(-50%)"
                  zIndex={10}
                >
                  <Text
                    fontWeight="bold"
                    fontSize="12px"
                    color="white"
                    whiteSpace="nowrap"
                  >
                    {percentage}
                  </Text>
                </Box>
              </Tooltip>
              <Box
                position="absolute"
                bottom={'-20px'}
                left="50%"
                transform="translateX(-50%)"
                zIndex={10}
              >
                <Text
                  fontWeight="bold"
                  fontSize="12px"
                  color="gray.500"
                  whiteSpace="nowrap"
                >
                  {funnelValueFormatter(funnelValue)}
                </Text>
              </Box>
            </Box>
          </Tooltip>
        )}
      </Flex>
    </Flex>
  );
}
