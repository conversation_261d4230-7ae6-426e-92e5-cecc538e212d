import {
  Box,
  Flex,
  Icon,
  Image,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  Stack,
  Text,
  Tooltip,
  VStack,
  IconButton,
} from '@chakra-ui/react';
import { ReactNode } from 'react';
import { BiMessageRoundedError } from 'react-icons/bi';
import { BsChatLeft } from 'react-icons/bs';
import { CgTemplate } from 'react-icons/cg';
import { FaCogs, FaUsers, FaWrench, FaShoppingBag } from 'react-icons/fa';
import { FiLogOut, FiSettings } from 'react-icons/fi';
import { MdOutlineCampaign } from 'react-icons/md';
import { RiNodeTree } from 'react-icons/ri';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { useQuery } from 'react-query';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { apiRoutes } from '../../constants/api-routes';
import { appPaths } from '../../constants/app-paths';
import { colors } from '../../constants/colors';
import { CompaniesService } from '../../services/companies.service';
import { logout } from '../../state/authSlice';
import { hoverButtonStyles } from '../../styles/hover.styles';
import { FaHome } from 'react-icons/fa';
import MessageSentAndProgress from '../MessageSentAndProgress';
import { InfoOutlineIcon } from '@chakra-ui/icons';
import { CompanyMessageBalanceByCategoryResponse } from '../../types/CompanyMessageBalanceByCategoryResponse';
import { useAppModuleAccessGuard } from '../../hooks/useAppModuleAccessGuard';
import { useSidebar } from '../../hooks/useSidebar';

interface SidebarOption {
  title: string;
  icon: ReactNode;
  path: string;
}

export const NAVBAR_HEIGHT = '40px';
export const SIDEBAR_WIDTH_COLLAPSED = '80px';
export const SIDEBAR_WIDTH_EXPANDED = '280px';

export const SIDEBAR_OPTIONS: SidebarOption[] = [
  {
    title: 'Home',
    icon: <FaHome size="20px" />,
    path: appPaths.home(),
  },
  {
    title: 'Conversas',
    icon: <BsChatLeft size="20px" />,
    path: appPaths.conversations(),
  },
  {
    title: 'Clientes',
    icon: <FaUsers size="20px" />,
    path: appPaths.customers.index(),
  },
  {
    title: 'Produtos',
    icon: <FaShoppingBag size="20px" />,
    path: appPaths.products.index(),
  },
  {
    title: 'Campanhas',
    icon: <MdOutlineCampaign size="20px" />,
    path: appPaths.campaigns.whatsapp.index(),
  },
  {
    title: 'Templates',
    icon: <CgTemplate size="20px" />,
    path: appPaths.messageTemplates.index(),
  },
  {
    title: 'Automações',
    icon: <RiNodeTree size="20px" />,
    path: appPaths.automations.messageFlows.index(),
  },
];

const Sidebar = () => {
  const { isExpanded, setIsExpanded } = useSidebar();
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { data, refetch } = useQuery(
    apiRoutes.getCompanyMessageBalanceByCategory(),
    async () => {
      const { data } =
        await CompaniesService.getCompanyMessageBalanceByCategory();
      return data;
    },
  );

  function handleClickOption(path: string) {
    navigate(path, { replace: true });
  }

  function isActivePath(path: string) {
    return location.pathname.split('/')[1] === path.split('/')[1];
  }

  function handleClickLogo() {
    navigate(appPaths.home(), { replace: true });
  }

  function toggleSidebar() {
    setIsExpanded(!isExpanded);
  }

  const limitReached = data?.daily.sent === data?.daily.total;

  const showTooltip = data
    ? (
        [
          'marketing',
          'utility',
          'service',
        ] as (keyof CompanyMessageBalanceByCategoryResponse)[]
      ).some((category) => data[category].sent >= data[category].total * 0.9)
    : false;

  return (
    <Flex
      width={isExpanded ? SIDEBAR_WIDTH_EXPANDED : SIDEBAR_WIDTH_COLLAPSED}
      bgColor={colors.primary}
      height="100%"
      flexDir={'column'}
      justify="space-between"
      transition="width 0.3s ease"
      position="relative"
    >
      <IconButton
        aria-label="Toggle sidebar"
        icon={isExpanded ? <ChevronLeftIcon /> : <ChevronRightIcon />}
        onClick={toggleSidebar}
        position="absolute"
        right="-12px"
        top="80px"
        size="sm"
        borderRadius="full"
        bg="white"
        color={colors.primary}
        border="2px solid"
        borderColor={colors.primary}
        _hover={{
          bg: colors.primary,
          color: 'white',
        }}
        zIndex={10}
      />

      <Box>
        <Flex
          data-prevent-unsaved-exit-FlowEditorPage="true"
          justify="center"
          align="center"
          height={NAVBAR_HEIGHT}
          mb={3}
          onClick={handleClickLogo}
          px={2}
          cursor="pointer"
        >
          <Image src="/logo192.png" height="80%" />
          {isExpanded && (
            <Text
              ml={3}
              color="white"
              fontWeight="bold"
              fontSize="lg"
              whiteSpace="nowrap"
            >
              Revi
            </Text>
          )}
        </Flex>

        <Stack width="100%" spacing={1} mt={4}>
          {SIDEBAR_OPTIONS.map(({ title, icon, path }, index) => {
            const hasAccess = checkUserHasPathAccess(path);

            if (!hasAccess) {
              return null;
            }

            const isActive = isActivePath(path);

            return (
              <Tooltip
                label={isExpanded ? '' : title}
                aria-label="A tooltip"
                key={index}
                isDisabled={isExpanded}
                placement="right"
              >
                <Flex
                  data-prevent-unsaved-exit-FlowEditorPage="true"
                  onClick={() => handleClickOption(path)}
                  padding={isExpanded ? 3 : 2}
                  mx={2}
                  borderRadius="md"
                  justify={isExpanded ? 'flex-start' : 'center'}
                  align="center"
                  cursor={'pointer'}
                  _hover={hoverButtonStyles}
                  bgColor={isActive ? colors.white : 'transparent'}
                  color={isActive ? colors.primary : colors.white}
                  gap={3}
                  transition="all 0.2s"
                >
                  {icon}
                  {isExpanded && (
                    <Text
                      fontSize="sm"
                      fontWeight={isActive ? 'bold' : 'medium'}
                      whiteSpace="nowrap"
                    >
                      {title}
                    </Text>
                  )}
                </Flex>
              </Tooltip>
            );
          })}
        </Stack>
      </Box>

      <VStack spacing={1} pb={2}>
        {data && (
          <Popover isLazy>
            <Tooltip
              label={isExpanded ? '' : 'Limites de mensagens'}
              aria-label="A tooltip"
              isDisabled={isExpanded}
              placement="right"
            >
              <Box
                width={isExpanded ? 'calc(100% - 16px)' : 'auto'}
                onClick={() => refetch()}
              >
                <PopoverTrigger>
                  <Flex
                    padding={isExpanded ? 3 : 2}
                    mx={2}
                    borderRadius="md"
                    justify={isExpanded ? 'flex-start' : 'center'}
                    align="center"
                    cursor="pointer"
                    _hover={hoverButtonStyles}
                    bgColor="transparent"
                    color={colors.white}
                    gap={3}
                    transition="all 0.2s"
                  >
                    <BiMessageRoundedError size="20px" />
                    {isExpanded && (
                      <Text
                        fontSize="sm"
                        fontWeight="medium"
                        whiteSpace="nowrap"
                      >
                        Limites
                      </Text>
                    )}
                  </Flex>
                </PopoverTrigger>
              </Box>
            </Tooltip>

            <PopoverContent width="420px" maxW="95vw" boxShadow="2xl">
              <PopoverArrow />
              <PopoverCloseButton padding={4} />

              <PopoverHeader
                fontWeight="bold"
                fontSize="lg"
                color="gray.800"
                padding={4}
              >
                Seu limite de mensagens
              </PopoverHeader>

              <PopoverBody>
                <VStack gap={5} fontSize="sm" align="stretch">
                  <Box
                    p={4}
                    borderRadius="md"
                    bg={limitReached ? 'red.50' : 'gray.50'}
                    border="1px solid"
                    borderColor={limitReached ? 'red.200' : 'gray.200'}
                  >
                    <Text fontWeight="medium" fontSize="sm" color="gray.600">
                      Mensagens restantes hoje
                    </Text>
                    <Flex align="baseline" gap={2}>
                      <Text
                        fontSize="2xl"
                        fontWeight="bold"
                        color={limitReached ? 'red.500' : 'gray.900'}
                      >
                        {data?.daily.total - data?.daily.sent}
                      </Text>
                      <Text fontSize="sm" color="gray.500">
                        de {data?.daily.total}
                      </Text>
                    </Flex>
                    {limitReached && (
                      <Text fontSize="sm" color="red.600" mt={1}>
                        Limite atingido
                      </Text>
                    )}
                  </Box>

                  <Flex align="center" gap={2}>
                    <Text fontWeight="bold" fontSize="lg" color="gray.800">
                      Mensagens por categoria
                    </Text>
                    {showTooltip && (
                      <Tooltip
                        label="Você está chegando ao limite, mas não se preocupe! Ainda é possível continuar enviando mensagens"
                        fontSize="xs"
                        bg="blue.600"
                        color="white"
                        borderRadius="md"
                        p={2}
                        placement="top"
                      >
                        <Icon
                          as={InfoOutlineIcon}
                          color="blue.500"
                          boxSize={4}
                          cursor="pointer"
                        />
                      </Tooltip>
                    )}
                  </Flex>

                  <VStack align="stretch" gap={3}>
                    <MessageSentAndProgress
                      text="Marketing"
                      data={data}
                      field="marketing"
                    />
                    <MessageSentAndProgress
                      text="Utilidade"
                      data={data}
                      field="utility"
                    />
                    <MessageSentAndProgress
                      text="Atendimento"
                      data={data}
                      field="service"
                    />
                  </VStack>

                  <Text
                    color="gray.500"
                    fontStyle="italic"
                    fontSize="xs"
                    textAlign="center"
                    borderTop="1px solid"
                    pt={2}
                  >
                    Atualizado a cada 36h
                  </Text>
                </VStack>
              </PopoverBody>
            </PopoverContent>
          </Popover>
        )}

        <Menu>
          <Tooltip
            label={isExpanded ? '' : 'Configurações'}
            aria-label="A tooltip"
            isDisabled={isExpanded}
            placement="right"
          >
            <MenuButton
              width={isExpanded ? 'calc(100% - 16px)' : 'auto'}
              cursor="pointer"
              _hover={hoverButtonStyles}
            >
              <Flex
                padding={isExpanded ? 3 : 2}
                mx={2}
                borderRadius="md"
                justify={isExpanded ? 'flex-start' : 'center'}
                align="center"
                cursor="pointer"
                _hover={hoverButtonStyles}
                bgColor="transparent"
                color={colors.white}
                gap={3}
                transition="all 0.2s"
              >
                <FiSettings size="20px" />
                {isExpanded && (
                  <Text fontSize="sm" fontWeight="medium" whiteSpace="nowrap">
                    Configurações
                  </Text>
                )}
              </Flex>
            </MenuButton>
          </Tooltip>
          <MenuList>
            {checkUserHasPathAccess(appPaths.settings.general()) && (
              <MenuItem
                onClick={() => handleClickOption(appPaths.settings.general())}
                display="flex"
                justifyContent={'space-between'}
              >
                Configurações <FaCogs />
              </MenuItem>
            )}
            {checkUserHasPathAccess(
              appPaths.debug.simulateMessageReceiveDebugPage(),
            ) &&
              process.env.REACT_APP_ENABLE_DEBUG_TOOLS === 'true' && (
                <MenuItem
                  onClick={() =>
                    handleClickOption(
                      appPaths.debug.simulateMessageReceiveDebugPage(),
                    )
                  }
                  display="flex"
                  justifyContent={'space-between'}
                >
                  Ferramentas de Debug <FaWrench />
                </MenuItem>
              )}
            <MenuItem
              onClick={() => dispatch(logout())}
              display="flex"
              justifyContent={'space-between'}
            >
              Sair <FiLogOut />
            </MenuItem>
          </MenuList>
        </Menu>
      </VStack>
    </Flex>
  );
};

export default Sidebar;
