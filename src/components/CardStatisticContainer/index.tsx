import { useQuery } from 'react-query';
import CardStatistic, { CardStatisticProp } from '../CardStatistic';
import { request } from '../../constants/request';

interface CardStatisticContainerProps
  extends Omit<CardStatisticProp, 'value' | 'valueColor' | 'bgIconColor'> {
  value?: any;
  requestRoute?: string;
  dataKey?: string;
  valueColor?: ((value: any) => string) | string;
  valueFormatter?: (value: any) => string;
  bgIconColor?: ((value: any) => string) | string;
}

const CardStatisticContainer = ({
  icon,
  value: providedValue,
  requestRoute,
  dataKey,
  title,
  valueFormatter,
  bgIconColor,
  tooltip,
  valueColor,
}: CardStatisticContainerProps) => {
  const { data, isFetching } = useQuery(
    requestRoute!,
    async () => {
      if (!requestRoute) return null;
      const res = await request.get(requestRoute);
      return res.data;
    },
    {
      enabled: !!requestRoute && providedValue === undefined,
    },
  );

  const finalValue =
    providedValue !== undefined
      ? providedValue
      : dataKey
        ? data?.[dataKey]
        : data;

  return (
    <CardStatistic
      icon={icon}
      title={title}
      value={valueFormatter ? valueFormatter(finalValue) : finalValue}
      isLoading={providedValue !== undefined ? false : isFetching}
      bgIconColor={
        bgIconColor && typeof bgIconColor === 'function'
          ? bgIconColor(finalValue)
          : bgIconColor
      }
      tooltip={tooltip}
      valueColor={
        valueColor && typeof valueColor === 'function'
          ? valueColor(finalValue)
          : valueColor
      }
    />
  );
};

export default CardStatisticContainer;
