import {
  <PERSON>ton,
  <PERSON>dal,
  ModalBody,
  ModalClose<PERSON>utton,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON>dal<PERSON><PERSON>er,
  Modal<PERSON>eader,
  ModalOverlay,
  Box,
} from '@chakra-ui/react';
import React from 'react';
import { scrollbarStyles } from '../../styles/scrollbar.styles';

interface CrudModalProps {
  isLoading: boolean;
  isOpen: boolean;
  onClose: () => void;
  title: string;
  actionButtonText: string;
  children: React.ReactNode;
  formId: string;
  onClickDelete?: () => void;
  canDelete?: boolean;
  maxBodyHeight?: string;
  size?: string;
}

const CrudModal = ({
  isLoading,
  isOpen,
  onClose,
  actionButtonText,
  title,
  children,
  formId,
  onClickDelete,
  canDelete,
  maxBodyHeight = '60vh',
  size,
}: CrudModalProps) => {
  return (
    <Modal
      blockScrollOnMount={false}
      isOpen={isOpen}
      onClose={onClose}
      isCentered
      size={size}
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{title}</ModalHeader>
        <ModalCloseButton />
        <Box maxHeight={maxBodyHeight} overflowY="auto" css={scrollbarStyles()}>
          <ModalBody>{children}</ModalBody>
        </Box>

        <ModalFooter display={'flex'} justifyContent="space-between" gap={3}>
          {onClickDelete ? (
            <Button
              variant="ghost"
              onClick={onClickDelete}
              isDisabled={!canDelete}
            >
              Excluir
            </Button>
          ) : (
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
          )}

          <Button
            isLoading={isLoading}
            variant="primary"
            type="submit"
            form={formId}
          >
            {actionButtonText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CrudModal;
