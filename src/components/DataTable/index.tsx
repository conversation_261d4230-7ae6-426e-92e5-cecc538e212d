import { TriangleDownIcon, TriangleUpIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  chakra,
  Flex,
  Input,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import {
  FilterFn,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import React, { HTMLProps, useEffect, useState } from 'react';
import { CSVLink } from 'react-csv';
import {
  FaAngleDoubleLeft,
  FaAngleDoubleRight,
  FaAngleLeft,
  FaAngleRight,
  FaFileCsv,
} from 'react-icons/fa';
import { useSearchParams } from 'react-router-dom';
import ButtonIcon from '../ButtonIcon';
import DebouncedInput from '../DebounceInput';
import IndeterminateCheckbox from '../IndeterminateCheckbox';
import Loading from '../Loading';
import { pageSizeOptions } from '../../constants/page-size-options';


interface DataTableProps {
  data: any[];
  columns: any[];
  rightContent?: React.ReactNode;
  leftContent?: React.ReactNode;
  showExportCSV?: boolean;
  rowSelection?: any;
  setRowSelection?: (rowSelection: any) => void;
  showRowSelection?: boolean;
}

const getRowId = (row: any, relativeIndex: any, parent: any) => {
  // In row object you have access to data.
  return parent ? [parent.id, row.uniqueId].join('.') : row.id;
};

const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  return String(row?.getValue(columnId) || '')
    .toLowerCase()
    .includes(value?.toLowerCase());

  // // Rank the item
  // const itemRank = rankItem(row.getValue(columnId), value)

  // // Store the itemRank info
  // addMeta({
  //   itemRank,
  // })

  // Return if the item should be filtered in/out
  // return itemRank.passed
};

const DataTable = ({
  data: tableData,
  columns: tableColumns,
  rightContent,
  leftContent,
  showExportCSV = false,
  rowSelection,
  setRowSelection,
  showRowSelection = false,
}: DataTableProps) => {
  const data = React.useMemo(() => tableData, [tableData]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [globalFilter, setGlobalFilter] = React.useState(searchParams.get('search'));

  const columns = React.useMemo(() => {
    if (!showRowSelection) {
      return tableColumns;
    } else {
      return [
        {
          id: 'select',
          header: (info: any) => {
            if (!info) return <div />;
            const table = info.table;
            return (
              <IndeterminateCheckbox
                {...{
                  checked: table.getIsAllPageRowsSelected(),
                  indeterminate: table.getIsSomeRowsSelected(),
                  onChange: table.getToggleAllPageRowsSelectedHandler(),
                }}
              />
            );
          },
          cell: ({ row }: any) => {
            return (
              <IndeterminateCheckbox
                {...{
                  checked: row.getIsSelected(),
                  disabled: !row.getCanSelect(),
                  indeterminate: row.getIsSomeSelected(),
                  onChange: row.getToggleSelectedHandler(),
                }}
              />
            );
          },
        },
        ...tableColumns,
      ];
    }
  }, [showRowSelection, tableColumns]);
  const [CSVColumns, setCSVColumns] = useState<any[]>([]);

  const {
    getHeaderGroups,
    getRowModel,
    previousPage,
    getCanPreviousPage,
    nextPage,
    setPageIndex,
    getPageCount,
    getCanNextPage,
    getState,
    setPageSize,
    getExpandedRowModel,
  } = useReactTable({
    getRowId,
    columns,
    data,
    state: {
      sorting,
      rowSelection,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    enableRowSelection: true, //enable row selection for all rows
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    // Pipeline
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    debugTable: true,
  });

  useEffect(() => {
    // TODO: fix logic for accessorFn
    const newCSVColumns = columns?.map((column) => {
      return {
        label: column?.header(),
        key: column?.accessorKey || '',
      };
    });

    setCSVColumns(newCSVColumns);
  }, [columns]);

  function handleChangeGlobalFilter(value: string) {
    setGlobalFilter(value);
    searchParams.set('search', value);
    setSearchParams(searchParams);
  }

  return (
    <TableContainer>
      <Flex gap={2} my={3}>
        {leftContent}
        <Box flex={1}>
          <DebouncedInput
            value={globalFilter ?? ''}
            onChange={(value) => handleChangeGlobalFilter(String(value))}
            placeholder="Pesquise por nome, telefone ou tag..."
          />
        </Box>
        {showExportCSV && (
          <CSVLink
            data={data}
            separator={';'}
            headers={CSVColumns}
            filename={'clientes.csv'}>
            <Button leftIcon={<FaFileCsv />}>Baixar CSV</Button>
          </CSVLink>
        )}
        {rightContent}
      </Flex>
      <Text mt={3} mb={-3}>
        {Object.keys(rowSelection).length > 0 && (
          <span>{Object.keys(rowSelection).length} itens selecionados</span>
        )}
      </Text>

      <Table mt={5}>
        <Thead>
          {getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Th key={header.id}>
                  {header.isPlaceholder ? null : (
                    <Box
                      onClick={header.column.getToggleSortingHandler()}
                      cursor="pointer">
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      <chakra.span pl="4">
                        {header.column.getIsSorted() ? (
                          header.column.getIsSorted() === 'asc' ? (
                            <TriangleDownIcon aria-label="sorted descending" />
                          ) : (
                            <TriangleUpIcon aria-label="sorted ascending" />
                          )
                        ) : null}
                      </chakra.span>
                    </Box>
                  )}
                </Th>
              ))}
            </Tr>
          ))}
        </Thead>
        <Tbody>
          {getRowModel().rows.map((row) => {
            return (
              <Tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Td key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Td>
                ))}
              </Tr>
            );
          })}
        </Tbody>
      </Table>
      <Text>
        {Object.keys(rowSelection).length > 0 && (
          <span>{Object.keys(rowSelection).length} itens selecionados</span>
        )}
      </Text>
      <Flex align={'center'} gap={5}>
        <Flex align={'center'}>
          <ButtonIcon
            icon={<FaAngleDoubleLeft />}
            onClick={() => setPageIndex(0)}
            disabled={!getCanPreviousPage()}
          />
          <ButtonIcon
            icon={<FaAngleLeft />}
            onClick={() => previousPage()}
            disabled={!getCanPreviousPage()}
          />
          <ButtonIcon
            icon={<FaAngleRight />}
            onClick={() => nextPage()}
            disabled={!getCanNextPage()}
          />
          <ButtonIcon
            icon={<FaAngleDoubleRight />}
            onClick={() => setPageIndex(getPageCount() - 1)}
            disabled={!getCanNextPage()}
          />
        </Flex>
        <Text>
          {getState().pagination.pageIndex * getState().pagination.pageSize}-
          {(getState().pagination.pageIndex + 1) *
            getState().pagination.pageSize}{' '}
          de <strong>{getExpandedRowModel().rows.length}</strong> itens
        </Text>
        <select
          value={getState().pagination.pageSize}
          onChange={(e) => {
            setPageSize(Number(e.target.value));
          }}>
          {pageSizeOptions.map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Mostrar {pageSize}
            </option>
          ))}
        </select>
      </Flex>
    </TableContainer>
  );
};

export default DataTable;
