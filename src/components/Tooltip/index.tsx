import {
  Tooltip as ChakraTooltip,
  TooltipProps,
  Icon,
  Box,
  Text,
} from '@chakra-ui/react';
import { FiHelpCircle } from 'react-icons/fi';

interface CustomTooltipProps extends TooltipProps {
  label: string;
}

const Tooltip = ({ label, ...rest }: CustomTooltipProps) => {
  return (
    <ChakraTooltip
      hasArrow
      placement="top"
      bg="gray.700"
      color="white"
      boxShadow="md"
      borderRadius="md"
      p={3}
      fontSize="sm"
      maxW="250px"
      textAlign="left"
      {...rest}
      label={<Text>{label}</Text>}
    >
      <Box w="28px" h="28px" borderRadius="full" cursor="pointer" bg="gray.50">
        <Icon as={FiHelpCircle} color="gray.500" w={4} h={4} />
      </Box>
    </ChakraTooltip>
  );
};

export default Tooltip;
