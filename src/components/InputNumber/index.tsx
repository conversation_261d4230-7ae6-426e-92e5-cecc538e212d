import {
  InputGroup,
  InputLeftAddon,
  InputRightAddon,
  Input,
  ThemingProps,
} from '@chakra-ui/react';

export interface InputNumberProps {
  size?: ThemingProps<'Input'>['size'];
  value?: number | string;
  onChange?: (value: number) => void;
  leftAddon?: string;
  rightAddon?: string;
  maxWidth?: any;
  name?: string;
  defaultValue?: number;
  isDisabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

const InputNumber = ({
  size,
  value,
  onChange,
  leftAddon,
  rightAddon,
  maxWidth,
  name,
  defaultValue,
  isDisabled = false,
  placeholder,
  maxLength,
}: InputNumberProps) => {
  function getValue() {
    if (!onChange) return;

    return value;
  }

  function handleChange(e: any) {
    const inputValue = e.target.value;
    if (onChange) {
      if (/^\d*$/.test(inputValue)) {
        onChange(inputValue);
      }
    }
  }

  return (
    <InputGroup
      size={size}
      display="flex"
      justifyContent={'center'}
      maxWidth={maxWidth}
    >
      {leftAddon && <InputLeftAddon children={leftAddon} />}
      <Input
        isDisabled={isDisabled}
        name={name}
        size={size}
        width="100%"
        bgColor="white"
        value={getValue()}
        defaultValue={defaultValue}
        onChange={handleChange}
        placeholder={placeholder}
        maxLength={maxLength}
      />
      {rightAddon && <InputRightAddon children={rightAddon} />}
    </InputGroup>
  );
};

export default InputNumber;
