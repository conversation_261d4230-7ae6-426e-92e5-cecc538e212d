import Rive from '@rive-app/react-canvas';

interface AIButtonProps {
  onClick: () => void;
}

const AIButton = ({ onClick }: AIButtonProps) => {
  const riveAnimationFile = '/animations/rive/ai-button.riv';
  const handleClick = () => {
    onClick();
  };

  return (
    <div style={{ backgroundColor: '', width: 64, height: 64 }}>
      <Rive
        src={riveAnimationFile}
        stateMachines="State Machine 1"
        onClick={handleClick}
        onTouchStart={handleClick}
        style={{
          cursor: 'pointer',
        }}
      />
    </div>
  );
};

export default AIButton;
