import { Box, Button, Divider, Switch } from '@chakra-ui/react';
import { FaEdit } from 'react-icons/fa';
import { MdOutlineRemoveRedEye } from 'react-icons/md';
import ThreeDImgCard from '../../pages/AutomationsPage/BackgroundAutomationsPage/components/3DImgCard';
import ButtonIcon from '../ButtonIcon';
import { colors } from '../../constants/colors';
import { ReactNode } from 'react';

interface CustomAction {
  label: string;
  icon: ReactNode;
  onClick: () => void;
}

interface AutomationCardProps {
  title: string;
  subtitle: string;
  imgSource: string;
  primaryActionName?: string;
  onPrimaryAction: () => void;
  hasSecondaryActions?: boolean;
  isActive?: boolean;
  onToggleActive?: () => void;
  onView?: () => void;
  onEdit?: () => void;
  customActions?: CustomAction[];
}

const CardAction = ({
  title,
  subtitle,
  imgSource,
  primaryActionName = '+ criar',
  onPrimaryAction,
  hasSecondaryActions = false,
  isActive = false,
  onToggleActive,
  onView,
  onEdit,
  customActions = [],
}: AutomationCardProps) => {
  return (
    <Box p={5} shadow="md" borderWidth="1px" borderRadius="md">
      <Box
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        alignItems="flex-start"
      >
        <Box flex="1">
          <Box fontWeight="bold">{title}</Box>
          <Box>{subtitle}</Box>
        </Box>
        <ThreeDImgCard
          onClick={hasSecondaryActions ? onEdit : onPrimaryAction}
          imgSource={imgSource}
        />
      </Box>

      <Divider marginY={5} />

      {!hasSecondaryActions ? (
        <Box textAlign="center">
          <Button variant="primary" width="70%" onClick={onPrimaryAction}>
            {primaryActionName}
          </Button>
        </Box>
      ) : (
        <Box
          display="flex"
          paddingTop={2}
          justifyContent="center"
          alignItems="center"
          height="40px"
          gap={4}
        >
          {onView && (
            <Box textAlign="center">
              <ButtonIcon
                tooltipLabel="Ver resultados"
                icon={
                  <MdOutlineRemoveRedEye
                    color={colors.primaryLight}
                    size={20}
                  />
                }
                onClick={onView}
              />
            </Box>
          )}

          {onEdit && (
            <Box textAlign="center">
              <ButtonIcon
                tooltipLabel="Editar"
                icon={<FaEdit color={colors.primaryLight} size={18} />}
                onClick={onEdit}
              />
            </Box>
          )}

          {customActions.map((action, index) => (
            <Box key={index} textAlign="center">
              <ButtonIcon
                tooltipLabel={action.label}
                icon={action.icon}
                onClick={action.onClick}
              />
            </Box>
          ))}

          {onToggleActive && (
            <Box textAlign="center">
              <Switch
                isChecked={isActive}
                colorScheme="green"
                onChange={onToggleActive}
              />
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default CardAction;
