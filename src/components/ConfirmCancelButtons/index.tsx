import { Stack, Button } from '@chakra-ui/react';
import { colors } from '../../constants/colors';

type ConfirmCancelButtonProps = {
  onConfirmClick: () => void;
  onCancelClick: () => void;
  isLoading?: boolean;
};

const ConfirmCancelButtons = ({
  onConfirmClick,
  onCancelClick,
  isLoading,
}: ConfirmCancelButtonProps) => {
  return (
    <Stack direction="row" mt={4} justify="flex-end" spacing={3}>
      <Button variant="ghost" onClick={onCancelClick}>
        Cancelar
      </Button>
      <Button
        bgColor={colors.primary}
        color="white"
        onClick={onConfirmClick}
        isLoading={isLoading}
        px={6}
      >
        Exportar
      </Button>
    </Stack>
  );
};

export default ConfirmCancelButtons;
