import { Box, Center, Image, Portal, Text } from '@chakra-ui/react';
import React, { ReactNode } from 'react';
import { pulsing } from '../../animations/pulsing';

interface LoadingScreenProps {
  isLoading: boolean;
  children: ReactNode;
  message?: string;
}

const LoadingScreen = ({
  isLoading,
  children,
  message,
}: LoadingScreenProps) => {
  return (
    <div>
      {isLoading ? (
        <Portal>
          <Center
            height="100vh"
            width="100vw"
            boxSizing="border-box"
            position="absolute"
            top={0}
            left={0}
            bgColor="rgba(255,255,255,0.8)"
            flexDir={'column'}
            zIndex={10000}
          >
            <Image
              src="/favicon.ico"
              alt="loading"
              animation={`${pulsing} infinite 1.5s linear`}
            />
            <Text size="md" fontWeight="bold" mt={4}>
              {message ?? 'Carregando'}
            </Text>
          </Center>
        </Portal>
      ) : (
        children
      )}
    </div>
  );
};

export default LoadingScreen;
