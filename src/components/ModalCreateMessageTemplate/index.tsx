import { Modal, ModalContent, ModalOverlay } from '@chakra-ui/react';
import CreateMessageTemplatePage from '../../pages/TemplatePage/components/CreateMessageTemplatePage';

interface ModalCreateMessageTemplateProps {
  onCreateMessageTemplate: (messageTemplateId: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const ModalCreateMessageTemplate = ({
  onCreateMessageTemplate,
  isOpen,
  onClose,
}: ModalCreateMessageTemplateProps) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent maxW="80vw" maxH="80vh" overflowY="scroll">
        <CreateMessageTemplatePage
          onCreateMessageTemplate={onCreateMessageTemplate}
        />
      </ModalContent>
    </Modal>
  );
};

export default ModalCreateMessageTemplate;
