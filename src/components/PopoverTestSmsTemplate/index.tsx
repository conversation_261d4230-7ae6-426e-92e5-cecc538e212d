import { ReactNode, useEffect, useState } from 'react';
import {
  <PERSON>over,
  <PERSON>over<PERSON><PERSON>ger,
  PopoverArrow,
  PopoverClose<PERSON>utton,
  PopoverContent,
  Text,
} from '@chakra-ui/react';
import {
  Stack,
  FormControl,
  FormLabel,
  Input,
  InputLeftAddon,
  InputGroup,
  ButtonGroup,
  Button,
} from '@chakra-ui/react';
import { FocusLock } from '@chakra-ui/react';
import { ReactInputMask } from 'react-input-mask';
import { useMutation } from 'react-query';
import { MessageTemplateUtils } from '../../utils/message-templates.utils';
import { MessageTemplate } from '../../types/MessageTemplate';
import { StringUtils } from '../../utils/string.utils';
import { colors } from '../../constants/colors';
import {
  SendSmsTemplateTestDto,
  SmsMessagesService,
} from '../../services/sms-messages.service';

enum MESSAGE_STATUS {
  SUCCESS = 'success',
  ERROR = 'error',
  NONE = 0,
}

type MessageStatusType = (typeof MESSAGE_STATUS)[keyof typeof MESSAGE_STATUS];

export interface PopoverTestSmsTemplateProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  template: MessageTemplate;
  children?: ReactNode;
}

const PopoverTestSmsTemplate = ({
  isOpen,
  onOpen,
  onClose,
  template,
  children,
}: PopoverTestSmsTemplateProps) => {
  const [recipientName, setRecipientName] = useState<string>('');
  const [recipientPhoneNumberId, setRecipientPhoneNumberId] =
    useState<string>('');
  const [messageStatus, setMessageStatus] = useState<MessageStatusType>(
    MESSAGE_STATUS.NONE,
  );
  const sendSmsTemplateTest = useMutation(
    (sendSmsTemplateTestDto: SendSmsTemplateTestDto) =>
      SmsMessagesService.sendSmsTemplateTest(sendSmsTemplateTestDto),
    {
      onSuccess: () => {
        setMessageStatus(MESSAGE_STATUS.SUCCESS);
      },
      onError: () => {
        setMessageStatus(MESSAGE_STATUS.ERROR);
      },
    },
  );

  useEffect(() => {
    if (isOpen) {
      setRecipientName('');
      setRecipientPhoneNumberId('');
      setMessageStatus(MESSAGE_STATUS.NONE);
    }
  }, [isOpen]);

  useEffect(() => {
    if (messageStatus === MESSAGE_STATUS.SUCCESS) {
      const oneSecondInMilliseconds = 1000;
      const timeoutId = setTimeout(() => onClose(), oneSecondInMilliseconds);

      return () => clearTimeout(timeoutId);
    }
  }, [messageStatus, onClose]);

  async function handleSubmit() {
    const parameters = MessageTemplateUtils.getAllParametersInText(
      template.templateText,
    );

    const templateArgs: Record<string, string> = {};
    parameters.forEach((parameter) => {
      let formatted = parameter.replace(/[\[|\]]/g, '');
      formatted = StringUtils.toCamelCase(formatted);
      templateArgs[formatted] = parameter;
    });

    await sendSmsTemplateTest.mutateAsync({
      recipientName,
      phoneNumber: recipientPhoneNumberId,
      templateId: template.id,
      templateArgs,
    });
  }

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      placement="right"
      closeOnBlur={true}
    >
      <PopoverTrigger>{children}</PopoverTrigger>
      <PopoverContent p={5}>
        <FocusLock persistentFocus={false}>
          <PopoverArrow />
          <PopoverCloseButton />
          <Stack>
            <FormControl>
              <FormLabel>Nome</FormLabel>
              <Input
                value={recipientName}
                onChange={(e) => setRecipientName(e.target.value)}
              />
            </FormControl>
            <FormControl>
              <FormLabel>Telefone</FormLabel>
              <InputGroup>
                <InputLeftAddon children="+55" />
                <Input
                  type="tel"
                  placeholder="(11) 00000-0000"
                  as={ReactInputMask}
                  mask="(99)99999-9999"
                  value={recipientPhoneNumberId}
                  onChange={(e) => setRecipientPhoneNumberId(e.target.value)}
                />
              </InputGroup>
            </FormControl>
            {messageStatus === MESSAGE_STATUS.SUCCESS ? (
              <Text color="green.500">Mensagem enviada com sucesso</Text>
            ) : messageStatus === MESSAGE_STATUS.ERROR ? (
              <Text color="red.500">Erro ao enviar mensagem</Text>
            ) : null}
            <ButtonGroup display="flex" justifyContent="flex-end">
              <Button variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button
                color={colors.white}
                bgColor={colors.primary}
                onClick={handleSubmit}
                isLoading={sendSmsTemplateTest.isLoading}
                isDisabled={!recipientName || !recipientPhoneNumberId}
              >
                Enviar
              </Button>
            </ButtonGroup>
          </Stack>
        </FocusLock>
      </PopoverContent>
    </Popover>
  );
};

export default PopoverTestSmsTemplate;
