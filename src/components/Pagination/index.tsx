import { Flex, Text, Select, Box, useColorModeValue } from '@chakra-ui/react';
import React, { useEffect, useMemo, useState } from 'react';
import {
  FaAngleDoubleLeft,
  FaAngleDoubleRight,
  FaAngleLeft,
  FaAngleRight,
} from 'react-icons/fa';
import { IconType } from 'react-icons';
import { pageSizeOptions } from '../../constants/page-size-options';

interface PaginationProps {
  onChangePage: (page: number) => void;
  onChangeRowsPerPage: (rowsPerPage: number) => void;
  totalRows: number;
  rowsPerPage: number;
  initialPage?: number;
  itemsLabel?: string;
}

const Pagination = ({
  onChangePage,
  onChangeRowsPerPage,
  totalRows,
  rowsPerPage,
  initialPage = 1,
  itemsLabel,
}: PaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);

  const totalPages = useMemo(
    () => Math.ceil(totalRows / rowsPerPage),
    [totalRows, rowsPerPage],
  );

  useEffect(() => {
    onChangePage(currentPage);
  }, [currentPage, onChangePage]);

  const from = (currentPage - 1) * rowsPerPage + 1;
  const to = Math.min(currentPage * rowsPerPage, totalRows);

  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages;

  const iconButtonStyle = {
    border: '1px solid',
    borderColor: useColorModeValue('gray.200', 'gray.600'),
    borderRadius: 'md',
    bg: useColorModeValue('white', 'gray.700'),
    w: 8,
    h: 8,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    cursor: 'pointer',
    _disabled: {
      opacity: 0.5,
      cursor: 'not-allowed',
    },
    _hover: {
      bg: useColorModeValue('gray.100', 'gray.600'),
    },
  };

  const IconButton = ({
    icon: Icon,
    onClick,
    disabled,
  }: {
    icon: IconType;
    onClick: () => void;
    disabled: boolean;
  }) => (
    <Box as="button" onClick={onClick} disabled={disabled} {...iconButtonStyle}>
      <Icon size={12} />
    </Box>
  );

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newRowsPerPage = Number(e.target.value);
    onChangeRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to the first page when changing the page size
  };

  return (
    <Flex
      justify="flex-start"
      align="center"
      wrap="wrap"
      w="100%"
      gap={4}
      px={2}
    >
      {/* Mostrar select */}
      <Flex align="center" gap={2}>
        <Text fontSize="sm" color="gray.600">
          Mostrar
        </Text>
        <Select
          size="sm"
          w="auto"
          value={rowsPerPage}
          onChange={handleRowsPerPageChange} // Use the updated function here
        >
          {pageSizeOptions.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </Select>
      </Flex>

      {/* Botões de navegação */}
      <Flex align="center" gap={2}>
        <IconButton
          icon={FaAngleDoubleLeft}
          onClick={() => setCurrentPage(1)}
          disabled={isFirstPage}
        />
        <IconButton
          icon={FaAngleLeft}
          onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
          disabled={isFirstPage}
        />
        <IconButton
          icon={FaAngleRight}
          onClick={() =>
            setCurrentPage((prev) => Math.min(totalPages, prev + 1))
          }
          disabled={isLastPage}
        />
        <IconButton
          icon={FaAngleDoubleRight}
          onClick={() => setCurrentPage(totalPages)}
          disabled={isLastPage}
        />
      </Flex>

      {/* Informações de range */}
      <Text fontSize="sm" color="gray.600">
        {from}–{to} de <strong>{totalRows}</strong> {itemsLabel}
      </Text>
    </Flex>
  );
};

export default Pagination;
