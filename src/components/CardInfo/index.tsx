import { ReactNode, useState } from 'react';
import {
  Box,
  Text,
  Flex,
  Icon,
  CloseButton,
  Divider,
  Avatar,
  useColorModeValue,
} from '@chakra-ui/react';
import { colors } from '../../constants/colors';

interface CardInfoProps {
  title: string;
  description: string;
  info: string;
  icon: ReactNode;
  iconBgColor?: string;
  onClick?: () => void;
  bgTitle?: ReactNode;
}

const CardInfo = ({
  title,
  description,
  info,
  icon,
  iconBgColor = colors.primary,
  onClick,
  bgTitle,
}: CardInfoProps) => {
  const textColor = 'gray.900';
  const descColor = 'gray.600';
  const statColor = 'gray.500';

  return (
    <Box
      bg="white"
      borderRadius="lg"
      boxShadow="md"
      _hover={{ boxShadow: 'lg', cursor: 'pointer', transform: 'scale(1.05)' }}
      transition="all 0.3s"
      overflow="hidden"
      minWidth="280px"
      maxWidth="280px"
      borderTopWidth={4}
      borderColor={iconBgColor}
      onClick={onClick}
      display="flex"
      flexDirection="column"
      p={6}
      justifyContent="space-between"
    >
      <Box>
        <Flex align="center" mb={4}>
          <Box p={2} borderRadius="lg" bg={iconBgColor}>
            {icon}
          </Box>
          <Text ml={3} fontSize="md" fontWeight="semibold" color={textColor}>
            {title}
          </Text>
        </Flex>
        <Text color={descColor} mb={4}>
          {description}
        </Text>
      </Box>
      <Box pt={4} borderTopWidth={1} borderColor="gray.100" height='100px'>
        <Text fontSize="sm" color={statColor}>
          {info}
        </Text>
      </Box>
    </Box>
  );
};

export default CardInfo;
