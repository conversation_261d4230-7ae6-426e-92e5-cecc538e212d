import React from 'react';
import {
  Tooltip as ChakraTooltip,
  Box,
  Text,
  TooltipProps,
} from '@chakra-ui/react';

interface TooltipTruncatedTextProps {
  text: string;
  maxWidth?: string;
  tooltipProps?: Partial<TooltipProps>;
  showTooltip?: boolean;
  fontSize?: string;
  padding?: string | number;
}

const TooltipTruncatedText = ({
  text,
  maxWidth = '100px',
  tooltipProps = {},
  showTooltip = true,
  fontSize = 'inherit',
  padding = 1,
}: TooltipTruncatedTextProps) => {
  const defaultTooltipProps: Partial<TooltipProps> = {
    hasArrow: true,
    placement: 'top',
    bg: 'gray.700',
    color: 'white',
    boxShadow: 'md',
    borderRadius: 'md',
    p: 2,
    fontSize: 'xs',
    ...tooltipProps,
  };

  const textElement = (
    <Box
      maxW={maxWidth}
      overflow="hidden"
      textOverflow="ellipsis"
      whiteSpace="nowrap"
      p={padding}
    >
      <Text isTruncated fontSize={fontSize}>
        {text}
      </Text>
    </Box>
  );

  if (!showTooltip) {
    return textElement;
  }

  return (
    <ChakraTooltip {...defaultTooltipProps} label={<Text>{text}</Text>}>
      {textElement}
    </ChakraTooltip>
  );
};

export default TooltipTruncatedText;
