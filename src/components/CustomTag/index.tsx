import { Flex, Text } from '@chakra-ui/react';
import React from 'react';

interface CustomTagProps {
  bgColor: string;
  icon?: React.ReactNode;
  label: string;
  textColor: string;
}

const CustomTag = ({
  bgColor,
  icon,
  label,
  textColor,
}: CustomTagProps) => {
  return (
    <Flex
      bgColor={bgColor}
      borderRadius="15px"
      paddingX="10px"
      paddingY="5px"
      alignItems={'center'}
      gap={2}
      fontWeight="bold"
    >
      {icon}
      <Text color={textColor}>{label}</Text>
    </Flex>
  );
};

export default CustomTag;
