import React, {useMemo, useRef, useState} from 'react';
import {Box, IconButton} from '@chakra-ui/react';
import Picker from '@emoji-mart/react';
import {BsFillEmojiSmileFill} from 'react-icons/bs';
import ButtonIcon from '../ButtonIcon';
import {useEmojiDataSet} from '../../hooks/useEmoji';

type CustomEmojiPickerRenderAs = 'ButtonIcon' | 'IconButton';
type CustomEmojiPickerAnchorPosition = 'top' | 'bottom';

const DEFAULT_CUSTOM_EMOJI_PICKER_ICON: React.ReactElement = (
  <BsFillEmojiSmileFill />
);
const DEFAULT_CUSTOM_EMOJI_PICKER_BUTTON_COMPONENT: CustomEmojiPickerRenderAs =
  'ButtonIcon';
const DEFAULT_EMOJI_PICKER_ANCHOR_POSITION: CustomEmojiPickerAnchorPosition =
  'bottom';
const DEFAULT_EMOJI_PICKER_HEIGHT = 400;
const DEFAULT_EMOJI_PICKER_GAP = 20;

interface CustomEmojiPickerProps {
  Icon?: React.ReactElement;
  renderAs?: CustomEmojiPickerRenderAs;
  emojiPickerHeight?: number;
  emojiPickerGap?: number;
  emojiPickerAnchorPosition?: CustomEmojiPickerAnchorPosition;
  onEmojiSelection: (emoji: string) => void;
  closeAfterPicking?: boolean;
}

const CustomEmojiPicker: React.FC<CustomEmojiPickerProps> = ({
  Icon = DEFAULT_CUSTOM_EMOJI_PICKER_ICON,
  renderAs = DEFAULT_CUSTOM_EMOJI_PICKER_BUTTON_COMPONENT,
  emojiPickerHeight = DEFAULT_EMOJI_PICKER_HEIGHT,
  emojiPickerGap = DEFAULT_EMOJI_PICKER_GAP,
  emojiPickerAnchorPosition = DEFAULT_EMOJI_PICKER_ANCHOR_POSITION,
  onEmojiSelection,
  closeAfterPicking = true,
}) => {
  const {data, locale} = useEmojiDataSet();

  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const emojiIconRef = useRef<HTMLButtonElement>(null);

  const emojiPickerAnchorProps: Pick<
    React.ComponentProps<typeof Box>,
    CustomEmojiPickerAnchorPosition
  > = useMemo(() => {
    if (emojiPickerAnchorPosition === 'top') {
      return {top: `-${emojiPickerHeight + emojiPickerGap}px`};
    }

    return {top: `${DEFAULT_EMOJI_PICKER_GAP}px`};
  }, [emojiPickerAnchorPosition, emojiPickerGap, emojiPickerHeight]);

  const handleOnEmojiClick = (emojiData?: {native: string}) => {
    if (!emojiData) return;
    onEmojiSelection(emojiData.native);
    if (closeAfterPicking) setShowEmojiPicker(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      !emojiPickerRef.current?.contains(event.target as Node) &&
      !emojiIconRef.current?.contains(event.target as Node)
    ) {
      setShowEmojiPicker(false);
    }
  };

  return (
    <>
      {renderAs === 'ButtonIcon' ? (
        <ButtonIcon
          ref={emojiIconRef}
          aria-label="Adicionar emoji"
          icon={Icon}
          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
        />
      ) : (
        <IconButton
          ref={emojiIconRef}
          aria-label="Adicionar emoji"
          variant="ghost"
          icon={Icon}
          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
        />
      )}
      {showEmojiPicker && (
        <Box
          {...emojiPickerAnchorProps}
          position="absolute"
          zIndex={1}
          ref={emojiPickerRef}
        >
          <Picker
            data={data.current}
            height={DEFAULT_EMOJI_PICKER_HEIGHT}
            onEmojiSelect={handleOnEmojiClick}
            onClickOutside={handleClickOutside}
            locale={locale}
          />
        </Box>
      )}
    </>
  );
};

export default CustomEmojiPicker;
