import React from 'react';
import Select from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { ColorUtils } from '../../utils/color.utils';
import TooltipTruncatedText from '../TooltipTruncatedText';

export interface SelectOption {
  value: string;
  label: string;
}

export interface InputSelectProps {
  options: SelectOption[];
  isMulti?: boolean;
  defaultValue?: SelectOption[];
  onChange?: (value: any) => void;
  value?: SelectOption[];
  disabled?: boolean;
  placeholder?: string;
  onCreateOption?: (value: any) => void;
  onMenuClose?: () => void;
  createLabel?: (value: string) => string;
  colorizeSelectedOptions?: boolean;
  styles?: any;
}

const MultiValueLabelWithTooltip = (props: any) => {
  return <TooltipTruncatedText text={props.data.label} maxWidth="150px" />;
};

const InputSelect = ({
  options,
  isMulti,
  defaultValue,
  onChange,
  value,
  disabled,
  placeholder,
  onCreateOption,
  onMenuClose,
  createLabel,
  colorizeSelectedOptions = false,
  styles = {},
}: InputSelectProps) => {
  const SelectComponent = onCreateOption ? CreatableSelect : Select;

  const mergeStyleFn = (
    defaultFn: (base: any, props?: any) => any,
    customStyle?: ((base: any, props?: any) => any) | object,
  ) => {
    return (base: any, props?: any) => {
      const defaultStyle = defaultFn(base, props);
      if (typeof customStyle === 'function') {
        return { ...defaultStyle, ...customStyle(base, props) };
      } else if (typeof customStyle === 'object') {
        return { ...defaultStyle, ...customStyle };
      }
      return defaultStyle;
    };
  };

  const defaultStyles = {
    control: mergeStyleFn(
      (base) => ({
        ...base,
        minHeight: '36px',
        fontSize: '0.875rem',
      }),
      styles.control,
    ),
    menuPortal: mergeStyleFn(
      (base) => ({ ...base, zIndex: 9999 }),
      styles.menuPortal,
    ),
    multiValue: mergeStyleFn((base, { data }) => {
      if (!colorizeSelectedOptions) return base;
      const { label, value } = data as SelectOption;
      const color = ColorUtils.stringToHexColor(label || value);
      return {
        ...base,
        backgroundColor: ColorUtils.lightenHexColor(color, 0.9),
        border: `1px solid ${color}`,
      };
    }, styles.multiValue),
    multiValueLabel: mergeStyleFn((base, { data }) => {
      if (!colorizeSelectedOptions) return base;
      const { label, value } = data as SelectOption;
      const color = ColorUtils.stringToHexColor(label || value);
      return {
        ...base,
        color: color,
      };
    }, styles.multiValueLabel),
    multiValueRemove: mergeStyleFn((base, { data }) => {
      if (!colorizeSelectedOptions) return base;
      const { label, value } = data as SelectOption;
      const color = ColorUtils.stringToHexColor(label || value);
      return {
        ...base,
        color: color,
        ':hover': {
          backgroundColor: color,
          color: 'white',
        },
      };
    }, styles.multiValueRemove),
  };

  return React.createElement(SelectComponent, {
    styles: defaultStyles,
    menuPortalTarget: document.body,
    placeholder,
    isDisabled: disabled,
    options,
    isMulti,
    defaultValue,
    onChange,
    onCreateOption,
    value,
    menuPosition: 'fixed',
    isClearable: true,
    closeMenuOnSelect: !isMulti,
    onMenuClose: onCreateOption ? onMenuClose : undefined,
    formatCreateLabel: createLabel
      ? createLabel
      : (inputValue: string) => `Criar opção: "${inputValue}"`,
    components: {
      MultiValueLabel: MultiValueLabelWithTooltip,
    },
  });
};

export default InputSelect;
