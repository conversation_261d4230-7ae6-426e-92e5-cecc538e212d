import { Input } from '@chakra-ui/react';
import { useEffect, useState } from 'react';

const DebouncedInput =({
  value: initialValue,
  onChange,
  debounce = 500,
  placeholder,
}: {
  value: string;
  onChange: (value: string | number) => void;
  debounce?: number;
  placeholder?: string;
}) => {
  const [value, setValue] = useState<string>(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
  }, [value]);

  return (
    <Input
      value={value}
      placeholder={placeholder}
      onChange={(e) => setValue(e.target.value)}
    />
  );
}

export default DebouncedInput;