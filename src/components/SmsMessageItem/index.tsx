import { Box, Flex, Text } from '@chakra-ui/react';
import reactStringReplace from 'react-string-replace';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useState } from 'react';

export interface SmsMessageItemProps {
  message?: string | null;
  isPreview?: boolean;
}

const SmsMessageItem = ({
  message,
  isPreview = false,
}: SmsMessageItemProps) => {
  const [currentTime] = useState(() =>
    format(new Date(), 'HH:mm', { locale: ptBR }),
  );

  return (
    <Box width={290} maxWidth={'80%'} fontSize={'15px'}>
      <Box
        bgColor={'#FFFFFF'}
        padding={1}
        paddingBottom={0.5}
        border="1px solid #eee"
        borderRadius="5px"
      >
        <Box padding={1}>
          <Text whiteSpace={'pre-line'}>
            {reactStringReplace(
              message || undefined,
              /(?<=^|[\s.,!?])\*([^\s*]+(?:\s+[^\s*]+)*)\*(?=[\s.,!?]|$)/g,
              (match, i) => (
                <Text as="span" fontWeight={'bold'} key={i}>
                  {match}
                </Text>
              ),
            )}
          </Text>
        </Box>

        {isPreview && (
          <Flex mb={2} justifyContent="flex-end" mr={2}>
            <Text fontSize="xs" color="rgba(0,0,0,0.45)" fontWeight="medium">
              {currentTime}
            </Text>
          </Flex>
        )}
      </Box>
    </Box>
  );
};

export default SmsMessageItem;
