import { Box, Image, Text } from '@chakra-ui/react';
import { FaFileAudio } from 'react-icons/fa';

interface FilePreviewProps {
  file: File;
}

const formatFileSize = (size: number) => {
  if (size >= 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  } else {
    return `${(size / 1024).toFixed(2)} KB`;
  }
};

const FilePreview = ({ file }: FilePreviewProps) => {
  const isImage = file?.type.startsWith('image/');
  const isPdf = file?.type === 'application/pdf';
  const isAudio = file?.type.startsWith('audio/');

  const fileSize = formatFileSize(file.size);

  return isImage ? (
    <Image
      src={URL.createObjectURL(file)}
      alt={file.name}
      maxH="150px"
      mb={2}
    />
  ) : isPdf ? (
    <Box style={{ width: '100%', overflow: 'hidden' }}>
      <iframe
        style={{ width: '100%', height: '100%' }}
        src={URL.createObjectURL(file)}
      />
    </Box>
  ) : isAudio ? (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      bg="gray.100"
      borderRadius="md"
      p={4}
      w="100%"
      h="150px"
      mb={2}
    >
      <FaFileAudio size={48} color="#718096" />
      <Text align="center" mt={3}>
        {`${file.name.length > 20 ? file.name.slice(0, 20) + '...' : file.name} • ${fileSize}`}
      </Text>
    </Box>
  ) : (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="center"
      bg="gray.100"
      borderRadius="md"
      p={2}
      w="100%"
      h="150px"
      mb={2}
    >
      <Text fontSize="lg" fontWeight="bold" color="gray.500">
        {file.name.split('?')[0].split('.').pop()?.toUpperCase()}
      </Text>
    </Box>
  );
};

export default FilePreview;
