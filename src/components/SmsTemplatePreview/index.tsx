import { Box, Flex, Text, Icon, HStack, VStack } from '@chakra-ui/react';
import { FaChevronLeft, FaEllipsisH, FaPhone, FaSignal } from 'react-icons/fa';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { smsPhonePreview } from '../../constants/colors';
import { useSelector } from 'react-redux';
import { RootState } from '../../state/store';
import SmsMessageItem, { SmsMessageItemProps } from '../SmsMessageItem';
import { IoSend } from 'react-icons/io5';
import { useState } from 'react';

interface SmsTemplatePreviewProps extends Pick<SmsMessageItemProps, 'message'> {
  height?: string;
}

const SmsTemplatePreview = ({
  message,
  height = '100vh',
}: SmsTemplatePreviewProps) => {
  const companyName = useSelector(
    (state: RootState) => state.auth.currentUser?.company?.name,
  );

  const [currentDate] = useState(new Date());

  return (
    <Box
      height={height}
      width="100%"
      maxWidth="405px"
      maxH="750px"
      minWidth="320px"
      borderRadius="2xl"
      overflow="hidden"
      boxShadow="0 8px 25px rgba(0, 0, 0, 0.2)"
      display="flex"
      flexDirection="column"
      position="relative"
      bg={smsPhonePreview.chatBg}
      fontFamily="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif"
    >
      <Box
        position="absolute"
        top="0"
        left="50%"
        transform="translateX(-50%)"
        width="35%"
        height="22px"
        bg="rgba(0,0,0,0.8)"
        borderBottomLeftRadius="12px"
        borderBottomRightRadius="12px"
        zIndex="10"
        boxShadow="0 1px 2px rgba(0,0,0,0.1)"
      />
      <Flex
        bg={smsPhonePreview.statusBar}
        pt={6}
        pb={1}
        px={4}
        color="white"
        justifyContent="space-between"
        alignItems="center"
      >
        <Text fontSize="xs" fontWeight="medium">
          {format(currentDate, 'HH:mm', { locale: ptBR })}
        </Text>
        <HStack spacing={2}>
          <Icon as={FaSignal} fontSize="xs" />
          <Text fontSize="xs">100%</Text>
        </HStack>
      </Flex>
      <Flex
        bg={smsPhonePreview.headerBg}
        p={3}
        color="white"
        alignItems="center"
        justifyContent="space-between"
        boxShadow="0 1px 3px rgba(0,0,0,0.12)"
      >
        <Flex alignItems="center">
          <Icon as={FaChevronLeft} mr={3} />
          <VStack alignItems="flex-start" spacing={0}>
            <Text fontWeight="bold" fontSize="md">
              {companyName?.slice(0, 8)}
            </Text>
          </VStack>
        </Flex>
        <HStack spacing={5}>
          <Icon as={FaPhone} boxSize="18px" />
          <Icon as={FaEllipsisH} boxSize="18px" />
        </HStack>
      </Flex>
      <Box
        flex="1"
        backgroundImage="url('/iphone-chat-bg.jpg')"
        backgroundSize="cover"
        padding={3}
        overflowY="auto"
        css={{
          '&::-webkit-scrollbar': {
            width: '3px',
          },
          '&::-webkit-scrollbar-track': {
            width: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(0,0,0,0.2)',
            borderRadius: '3px',
          },
        }}
      >
        <Flex justify="center" mb={4}>
          <Text fontSize="xs" color="#AAA" px={3} py={1} fontWeight="medium">
            {format(currentDate, "EEEE, d 'de' MMMM", { locale: ptBR })}
          </Text>
        </Flex>

        <Flex justifyContent="flex-start" mb={2} ml={0}>
          <SmsMessageItem message={message} isPreview={true} />
        </Flex>
      </Box>

      <Flex
        p={2}
        pb={4}
        alignItems="center"
        justifyContent="space-between"
        borderTopWidth="1px"
        borderTopColor="rgba(0,0,0,0.1)"
      >
        <Flex
          flex="1"
          mx={1}
          borderRadius="full"
          bg={smsPhonePreview.inputBg}
          p={1}
          alignItems="center"
        >
          <Box flex="1" py={1} px={2}>
            <Text fontSize="sm" color="gray.500">
              Mensagem
            </Text>
          </Box>
        </Flex>

        <Flex
          bg={smsPhonePreview.buttonColor}
          borderRadius="full"
          p={2}
          color="white"
          ml={1}
          transition="all 0.2s"
          alignItems="center"
          justifyContent="center"
        >
          <Icon as={IoSend} boxSize="18px" />
        </Flex>
      </Flex>

      <Box
        height="4px"
        width="25%"
        bg="rgba(0,0,0,0.7)"
        borderRadius="full"
        position="absolute"
        bottom="2px"
        left="50%"
        transform="translateX(-50%)"
      />
    </Box>
  );
};

export default SmsTemplatePreview;
