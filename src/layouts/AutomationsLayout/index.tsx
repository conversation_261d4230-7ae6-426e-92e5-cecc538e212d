import { Grid, GridItem } from '@chakra-ui/react';
import React, { ReactNode } from 'react';
import SidebarSecondary from '../../components/SidebarSecondary';
import { appPaths } from '../../constants/app-paths';
import {
  FiMessageSquare,
  FiRepeat,
  FiSend,
  FiDollarSign,
} from 'react-icons/fi';

interface AutomationsLayoutProps {
  children: ReactNode;
}

const SIDEBAR_OPTIONS = [
  {
    title: 'Fluxos de mensagem',
    path: appPaths.automations.messageFlows.index(),
    icon: FiMessageSquare,
  },
  {
    title: 'Respostas automáticas',
    path: appPaths.automations.autoReplies(),
    icon: FiRepeat,
  },
  {
    title: 'Envios automatizados',
    path: appPaths.automations.backgroundAutomations.index(),
    icon: FiSend,
  },
  {
    title: 'Cashback',
    path: appPaths.automations.cashback(),
    icon: FiDollarSign,
  },
];

const AutomationsLayout = ({ children }: AutomationsLayoutProps) => {
  return (
    <Grid
      height="100vh"
      templateColumns="auto 1fr"
      templateAreas={'"sidebar page"'}
    >
      <GridItem area="sidebar">
        <SidebarSecondary title="Automações" options={SIDEBAR_OPTIONS} />
      </GridItem>
      <GridItem area="page" maxH={'100vh'} height={'100vh'} overflow="auto">
        {children}
      </GridItem>
    </Grid>
  );
};

export default AutomationsLayout;
