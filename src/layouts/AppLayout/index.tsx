import { Grid, GridItem, Show } from '@chakra-ui/react';
import React, { ReactNode } from 'react';
import Sidebar from '../../components/Sidebar';
import { useSidebar } from '../../hooks/useSidebar';

interface AppLayoutProps {
  children: ReactNode;
  background?: string;
}

const AppLayout = ({ children, background }: AppLayoutProps) => {
  const { sidebarWidth, isMobile } = useSidebar();

  return (
    <Grid
      height={'100vh'}
      templateColumns={isMobile ? 'auto' : `${sidebarWidth} auto`}
      templateAreas={isMobile ? "'page'" : "'sidebar page'"}
      bg={background || 'white'}
      transition="grid-template-columns 0.3s ease"
    >
      <Show above="lg">
        <GridItem area="sidebar">
          <Sidebar />
        </GridItem>
      </Show>
      <GridItem area="page" maxH={'100vh'} height={'100vh'} overflow="auto">
        {children}
      </GridItem>
    </Grid>
  );
};

export default AppLayout;
