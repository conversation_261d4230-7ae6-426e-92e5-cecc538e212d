import { Grid, GridItem, Box } from '@chakra-ui/react';
import { ReactNode } from 'react';
import SidebarSecondary from '../../components/SidebarSecondary';
import { appPaths } from '../../constants/app-paths';
import {
  FiColumns,
  FiTag,
  FiFileText,
  FiHeadphones,
  FiHome,
  FiShield,
  FiZap,
} from 'react-icons/fi';
import { IconType } from 'react-icons/lib';
import { MdOutlineMail } from 'react-icons/md';
import { colors } from '../../constants/colors';

interface SettingsLayoutProps {
  children: ReactNode;
}

const SIDEBAR_OPTIONS: { title: string; path: string; icon: IconType }[] = [
  {
    title: 'Geral',
    path: appPaths.settings.general(),
    icon: FiHome,
  },
  {
    title: 'Atendimento',
    path: appPaths.settings.support(),
    icon: FiHeadphones,
  },
  {
    title: 'Criar & Ocultar Colunas',
    path: appPaths.settings.customColumns(),
    icon: FiColumns,
  },
  {
    title: 'Tags',
    path: appPaths.settings.tags(),
    icon: FiTag,
  },
  {
    title: 'Integrações',
    path: appPaths.settings.integrationSettings.index(),
    icon: FiZap,
  },
  {
    title: 'Acessos',
    path: appPaths.settings.access(),
    icon: FiShield,
  },
  {
    title: 'Email',
    path: appPaths.settings.email(),
    icon: MdOutlineMail,
  },
  {
    title: 'Faturas',
    path: appPaths.settings.invoices(),
    icon: FiFileText,
  },
];

const SettingsLayout = ({ children }: SettingsLayoutProps) => {
  return (
    <Grid
      height="100vh"
      templateColumns="auto 1fr"
      templateAreas={'"sidebar page"'}
    >
      <GridItem area="sidebar">
        <SidebarSecondary title="Configurações" options={SIDEBAR_OPTIONS} />
      </GridItem>
      <GridItem
        area="page"
        maxH="100vh"
        height="100vh"
        overflow="auto"
        bgColor={colors.backgroundSecondary}
      >
        <Box maxW="1000px" mx="auto" mt={4} px={3} p="20px">
          {children}
        </Box>
      </GridItem>
    </Grid>
  );
};

export default SettingsLayout;
