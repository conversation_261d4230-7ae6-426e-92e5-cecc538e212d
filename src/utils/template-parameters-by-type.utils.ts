import { MessageTemplateTypeEnum } from '../types/MessageTemplateType';
import { TemplateParametersEnum } from '../types/TemplateParametersEnum';

const templateParameters: Record<string, string[]> = {
  [MessageTemplateTypeEnum.ABANDONED_CART]: [
    TemplateParametersEnum.CUSTOMER_NAME,
    TemplateParametersEnum.ABANDONED_CART_PRODUCTS,
  ],
  [MessageTemplateTypeEnum.NEW_ORDER]: [TemplateParametersEnum.CUSTOMER_NAME],
  [MessageTemplateTypeEnum.TRACKING_CODE]: [
    TemplateParametersEnum.CUSTOMER_NAME,
    TemplateParametersEnum.TRACKING_CODE,
  ],
  [MessageTemplateTypeEnum.ORDER_PAYMENT_CONFIRMATION]: [
    TemplateParametersEnum.CUSTOMER_NAME,
  ],
  [MessageTemplateTypeEnum.WELCOME_REGISTRATION]: [
    TemplateParametersEnum.CUSTOMER_NAME,
  ],
  [MessageTemplateTypeEnum.ORDER_STATUS_UPDATE]: [
    TemplateParametersEnum.CUSTOMER_NAME,
    TemplateParametersEnum.CUSTOM_TEXT,
    TemplateParametersEnum.ORDER_ID,
  ],
  [MessageTemplateTypeEnum.CASHBACK]: [
    TemplateParametersEnum.CUSTOMER_NAME,
    TemplateParametersEnum.COUPON_EXPIRATION_DATE,
    TemplateParametersEnum.DAYS_UNTIL_COUPON_EXPIRATION,
    TemplateParametersEnum.COUPON_CODE,
    TemplateParametersEnum.COUPON_VALUE,
  ],
};

const excludedParameters: TemplateParametersEnum[] = [
  TemplateParametersEnum.ORDER_ID,
  TemplateParametersEnum.COUPON_EXPIRATION_DATE,
  TemplateParametersEnum.DAYS_UNTIL_COUPON_EXPIRATION,
  TemplateParametersEnum.COUPON_CODE,
  TemplateParametersEnum.COUPON_VALUE,
  TemplateParametersEnum.TRACKING_CODE,
];

export const TemplateParametersUtils = {
  templateParameters,
  excludedParameters,
};
