export function sanitizeErrorMessage(message: string): string {
  const lower = message.toLowerCase();

  const patternsToSuppress = ['prisma', 'raw query failed', 'gupshup'];

  const shouldNormalize = patternsToSuppress.some((pattern) =>
    lower.includes(pattern),
  );

  if (shouldNormalize) {
    return 'Houve um erro na aplicação, tente novamente mais tarde ou contate o suporte';
  }

  return message;
}

export function isDuplicateError(
  message: string,
  shownErrors: Set<string>,
): boolean {
  return shownErrors.has(message);
}

export const ErrorHandleUtils = {
  sanitizeErrorMessage,
  isDuplicateError,
};
