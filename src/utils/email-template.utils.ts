import {
  EmailTemplateParametersEnum,
  EmailTemplateParametersList,
} from '../types/EmailTemplateParametersEnum';
import { MessageTemplate } from '../types/MessageTemplate';

const TemplateParametersRegexpString =
  '(' + EmailTemplateParametersList.join('|') + ')';

const TemplateParametersRegexp = new RegExp(
  TemplateParametersRegexpString,
  'g',
);

function getAllParametersInText(emailTemplateText: string): string[] {
  const parametersRegex = /\{\{.*?\}\}/g;
  const parametersArray = emailTemplateText.match(parametersRegex) as string[];

  return [...new Set(parametersArray)];
}

function getDefaultParametersInText(
  emailTemplateText: string,
): EmailTemplateParametersEnum[] {
  const parametersArray = emailTemplateText.match(
    TemplateParametersRegexp,
  ) as string[];

  return [...new Set(parametersArray)] as EmailTemplateParametersEnum[];
}

function getCustomParametersInText(emailTemplateText: string) {
  const parametersArray = getAllParametersInText(emailTemplateText);
  return [...new Set(parametersArray)].filter((el: any) => {
    return !EmailTemplateParametersList.filter(
      (param) => param !== EmailTemplateParametersEnum.CUSTOM_TEXT,
    ).includes(el as EmailTemplateParametersEnum);
  });
}

function isValidTemplateArgs(
  templateArgs: Record<string, string>,
  emailTemplateText?: string,
): boolean {
  if (!emailTemplateText) {
    return false;
  }
  const customParameters = getCustomParametersInText(emailTemplateText);
  const customParametersCount = customParameters.filter(Boolean).length;
  const templateArgsCount = Object.values(templateArgs).filter(Boolean).length;

  return customParametersCount === templateArgsCount;
}

export const EmailTemplateUtils = {
  getDefaultParametersInText,
  getCustomParametersInText,
  getAllParametersInText,
  isValidTemplateArgs,
};
