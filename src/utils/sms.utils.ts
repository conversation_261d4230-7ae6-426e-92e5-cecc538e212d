function cleanCharactersForSMS(text: string) {
  // Replace paragraphs with a single space
  const withoutParagraphs = text.replace(/\n+/g, ' ');

  // Remove emojis
  const withoutEmojis = withoutParagraphs.replace(
    /[\uD800-\uDBFF][\uDC00-\uDFFF]/g,
    '',
  );

  // Normalize to remove accents
  const withoutAccents = withoutEmojis
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');

  // Remove special characters, keeping only numbers, letters, spaces, and allowed special characters
  const allowedCharacters = withoutAccents.replace(
    /[^a-zA-Z0-9 !@#$%^&*()\.\[\],;:'"<>/\\?{}|_+-=]/g,
    '',
  );

  return allowedCharacters;
}

export const SmsUtils = {
  cleanCharactersForSMS,
};
