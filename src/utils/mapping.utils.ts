export interface MappingFields {
  source: string;
  destination: string;
}

function mapJsonStructure({
  input,
  withEmptyDestination,
}: {
  input: any;
  withEmptyDestination?: boolean;
}): MappingFields[] {
  if (Array.isArray(input)) {
    if (input.length === 0) return [];

    if (typeof input[0] !== 'object' || input[0] === null) {
      return [
        {
          source: '[0]',
          destination: withEmptyDestination ? '' : 'custom_field_0',
        },
      ];
    }

    return mapJsonObject(
      input.find((item) => typeof item === 'object') || {},
      'item',
      withEmptyDestination,
    );
  }

  return mapJsonObject(input, '', withEmptyDestination);
}

function mapJsonObject(
  obj: any,
  prefix: string,
  withEmptyDestination = false,
): MappingFields[] {
  let result: MappingFields[] = [];

  function extractJsonPaths(obj: any, path: string, destination: string) {
    if (Array.isArray(obj)) {
      if (obj.length > 0) {
        extractJsonPaths(obj[0], `${path}[0]`, `${destination}0`);
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        extractJsonPaths(
          obj[key],
          path ? `${path}.${key}` : key,
          destination ? `${destination}_${key}` : key,
        );
      }
    } else {
      result.push({
        source: path,
        destination: withEmptyDestination ? '' : destination,
      });
    }
  }

  extractJsonPaths(obj, prefix, prefix);
  return result;
}

const MappingUtils = {
  mapJsonStructure,
  mapJsonObject,
};

export default MappingUtils;
