import { EncodeDecodeUtils } from './encode-decode.utils';

const isSupported = (): boolean => {
  return (
    'serviceWorker' in navigator &&
    'PushManager' in window &&
    'Notification' in window
  );
};

const getPermissionStatus = (): NotificationPermission => {
  if (!WebPushUtils.isSupported()) {
    return 'denied';
  }
  return Notification.permission;
};

const getSubscriptionToPushNotifications =
  async (): Promise<PushSubscription | null> => {
    try {
      const registration = await WebPushUtils.registerServiceWorker();
      const subscription = await registration.pushManager.getSubscription();

      return subscription;
    } catch (error) {
      console.error('Error checking push notifications status:', error);
      return null;
    }
  };

const parseSubscription = (subscription: PushSubscription) => {
  return {
    endpoint: subscription.endpoint,
    keys: {
      p256dh: EncodeDecodeUtils.arrayBufferToBase64(
        subscription.getKey('p256dh') as Array<PERSON>uffer,
      ),
      auth: EncodeDecodeUtils.arrayBufferToBase64(
        subscription.getKey('auth') as ArrayBuffer,
      ),
    },
  };
};

const registerServiceWorker = async (): Promise<ServiceWorkerRegistration> => {
  if (!WebPushUtils.isSupported()) {
    throw new Error('Este navegador não suporta notificações');
  }

  try {
    const registration =
      await navigator.serviceWorker.register('/service-worker.js');

    await navigator.serviceWorker.ready;

    return registration;
  } catch (error) {
    console.error('Falha ao configurar as notificações', error);
    throw error;
  }
};

const requestPermission = async (): Promise<NotificationPermission> => {
  if (!WebPushUtils.isSupported()) {
    throw new Error('Notificações push não são suportadas neste navegador');
  }

  const permission = await Notification.requestPermission();
  return permission;
};

const locallySubscribeToPushNotifications = async (
  vapidPublicKey: string,
): Promise<PushSubscription | null> => {
  try {
    const registration = await WebPushUtils.registerServiceWorker();
    const existingSubscription =
      await registration.pushManager.getSubscription();

    if (existingSubscription) {
      return existingSubscription;
    }

    const vapidPublicKeyArray =
      EncodeDecodeUtils.base64UrlToUint8Array(vapidPublicKey);

    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: vapidPublicKeyArray as any,
    });

    return subscription;
  } catch (error) {
    return null;
  }
};

const locallyUnsubscribeFromPushNotifications = async (): Promise<boolean> => {
  try {
    const registration = await WebPushUtils.registerServiceWorker();
    const subscription = await registration.pushManager.getSubscription();

    if (subscription) {
      await subscription.unsubscribe();
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
};

export const WebPushUtils = {
  getPermissionStatus,
  getSubscriptionToPushNotifications,
  isSupported,
  parseSubscription,
  registerServiceWorker,
  requestPermission,
  locallySubscribeToPushNotifications,
  locallyUnsubscribeFromPushNotifications,
};
