function formatCurrency(value?: number, maximumFractionDigits = 2): string {
  if (value === undefined) {
    return '-';
  }
  return (value / 100).toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    maximumFractionDigits,
  });
}

function fromCentsToInt(value?: number): number | string {
  if (typeof value !== 'number' || isNaN(value)) {
    return '-';
  }
  return Math.floor(value / 100);
}

function parseToCents(value?: number): number | null {
  if (typeof value !== 'number' || isNaN(value)) {
    return null;
  }
  return Math.round(value * 100);
}

export const MoneyUtils = {
  formatCurrency,
  fromCentsToInt,
  parseToCents,
};
