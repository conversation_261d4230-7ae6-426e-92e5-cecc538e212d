import { ExperimentStatus } from './../../types/CampaignExperiment';
export default {
  translation: {
    weekdays: {
      short: {
        sun: 'Dom',
        mon: 'Seg',
        tue: 'Ter',
        wed: 'Qua',
        thu: 'Qui',
        fri: 'Sex',
        sat: 'Sáb',
      },
      long: {
        sunday: 'Domingo',
        monday: 'Segunda-feira',
        tuesday: 'Terça-feira',
        wednesday: 'Quarta-feira',
        thursday: 'Quinta-feira',
        friday: '<PERSON><PERSON>-feira',
        saturday: 'Sábado',
      },
    },
    enums: {
      FlowTriggerType: {
        exact_match: 'Mensagem igual',
        quick_reply_message_template: 'Resposta rápida',
        keyword_match: 'Mensagem contém',
        abandoned_cart: 'Carrinho abandonado',
        csat: 'Ticket finalizado com CSAT',
        initial_contact: 'Contato inicial',
      },
      FlowNodeType: {
        trigger: 'Trigger',
        send_whatsapp_message: 'Enviar mensagem',
        move_conversation_to_category: 'Mover conversa para categoria',
        send_whatsapp_media: 'Enviar mídia',
        add_tag_to_customer: 'Adicionar Tag ao Cliente',
        end_whatsapp_conversation: 'Finalizar Conversa',
        save_customer_response: 'Salvar resposta do cliente',
        conditions_check: 'Condições',
        http_request: 'Requisição HTTP',
        time_delay: 'Time delay',
        send_whatsapp_message_template: 'Enviar template de whatsapp',
        send_email_template: 'Enviar template de email',
        assign_conversation_ticket_to_agent: 'Atribuir atendimento à atendente',
      },
      TimeDelayUnit: {
        minutes: 'Minutos',
        hours: 'Horas',
      },
      ConversationTicketStatus: {
        open: 'Aberto',
        closed: 'Finalizado',
      },
      WhatsappCampaignStatus: {
        in_progress: 'Em progresso',
        completed: 'Finalizada',
        scheduled: 'Agendada',
        canceled: 'Cancelada',
        interrupted: 'Interrompida',
        failed: 'Falhou',
      },
      ExperimentStatus: {
        scheduled: 'Agendado',
        in_progress: 'Em progresso',
        completed: 'Finalizado',
        canceled: 'Cancelado',
      },
      SmsCampaignStatus: {
        in_progress: 'Em progresso',
        completed: 'Finalizada',
        scheduled: 'Agendada',
        canceled: 'Cancelada',
        failed: 'Falhou',
      },
      MessageTemplateStatus: {
        pending: 'Pendente',
        rejected: 'Rejeitado',
        approved: 'Aprovado',
        deleted: 'Deletado',
        disabled: 'Desativado',
        paused: 'Pausado',
      },
      RFMGroup: {
        champions: 'Campeões',
        loyal_customers: 'Clientes fiéis',
        potential_loyalists: 'Potenciais fiéis',
        recent_customers: 'Clientes recentes',
        promising: 'Promissores',
        need_attention: 'Precisam de atenção',
        about_to_sleep: 'Prestes a dormir',
        cannot_lose_them: 'Não pode perder',
        at_risk: 'Em risco',
        hibernating: 'Hibernando',
        lost_customers: 'Perdidos',
      },
      AppModules: {
        CHAT: 'Chat',
        CUSTOMERS: 'Clientes',
        CAMPAIGNS: 'Campanhas',
        TEMPLATES: 'Templates',
        EMAIL_TEMPLATES: 'Templates de Email',
        AUTOMATIONS: 'Automatizações',
        SETTINGS: 'Configurações',
        REPORTS: 'Relatórios',
        DEBUG_TOOLS: 'Ferramentas de Debug',
        HOME: 'Tela de Início',
        PRODUCTS: 'Produtos',
      },
      EmailTemplateTypes: {
        MARKETING: 'Marketing',
        TRANSACTIONAL: 'Transacional',
        ABANDONED_CART: 'Carrinho Abandonado',
        TRACKING_CODE: 'Código de Rastreamento',
        ORDER_STATUS_UPDATE: 'Atualização de Status do Pedido',
      },
      couponStep: {
        pending: 'Pendente',
        creation_whatsapp_sent: 'Enviado',
        reminder_whatsapp_sent: 'Lembrete',
        last_day_whatsapp_sent: 'Último Dia',
      },
      AbandonedCartStatus: {
        flow_triggered: 'Fluxo acionado',
        abandoned: 'Abandonado',
        whatsapp_sent: 'Whatsapp enviado',
        failed: 'Falhou',
      },
    },
  },
};
