import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

interface Announcement {
  createdAt: string;
  updatedAt: string;
  id: string;
  html: string;
}

const listAnnouncements = async (params?: {
  viewed?: boolean;
  lastXDays?: number;
}): Promise<AxiosResponse<Announcement[]>> => {
  return request.get(
    apiRoutes.listAnnouncements({
      viewed: params?.viewed,
      lastXDays: params?.lastXDays,
    }),
  );
};

const updateUserAnnouncementView = async (id: string) => {
  return request.post(apiRoutes.updateUserLastAnnouncementViewedAt(id));
};

export const AnnouncementsService = {
  listAnnouncements,
  updateUserAnnouncementView,
};
