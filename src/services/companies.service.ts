import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { BillingSettings } from '../types/BillingSettings';
import { Company } from '../types/Company';
import {
  CompanyDefinedField,
  CompanyDefinedFieldDataTypeEnum,
  CompanyDefinedFieldTableEnum,
} from '../types/CompanyDefinedField';
import { parseJwt } from '../utils/parse-jwt.utils';
import { CompanyMessageBalanceByCategoryResponse } from '../types/CompanyMessageBalanceByCategoryResponse';
import { GupshupProfile } from '../types/GupshupProfile';

const getCompanyDetails = (): Promise<AxiosResponse<Company>> => {
  return request.get(apiRoutes.getCompanyDetails());
};

export interface UpdateCompanyDto {
  firstContactMessage: string;
  isAutomaticSortingActive: boolean;
}

export interface CompanyBusinessHours {
  weekday: number;
  startTime: Date;
  endTime: Date;
}

export interface CompanyBusinessHoursPreferencesDto {
  businessHours: CompanyBusinessHours[];
  afterHoursMessage: string | null;
  calculateTicketMetricsByBusinessHours?: boolean;
}

export interface UpdateGupshupProfileDto {
  description?: string;
  vertical?: string;
  website?: string;
}

const updateCompany = (updateCompanyDto: UpdateCompanyDto) => {
  const accessToken = localStorage.getItem('access_token');
  const userData = parseJwt(accessToken!);
  return request.put(
    apiRoutes.updateCompany(userData!.companyId),
    updateCompanyDto,
  );
};

const getCompanyMessageBalance = (): Promise<
  AxiosResponse<{
    daily: {
      total: number;
      sent: number;
    };
    monthly: {
      total: number;
      sent: number;
      billable: number;
    };
    canSend: boolean;
  }>
> => {
  return request.get(apiRoutes.getCompanyMessageBalance());
};

const getCompanyMessageBalanceByCategory = (): Promise<
  AxiosResponse<CompanyMessageBalanceByCategoryResponse>
> => {
  return request.get(apiRoutes.getCompanyMessageBalanceByCategory());
};

const listCompanyDefinedFields = (
  table: CompanyDefinedFieldTableEnum,
): Promise<AxiosResponse<CompanyDefinedField[]>> => {
  return request.get(apiRoutes.listCompanyDefinedFields(table));
};

const listValuesInCompanyDefinedField = (
  companyDefinedField: string,
): Promise<AxiosResponse<string[]>> => {
  return request.get(
    apiRoutes.listValuesInCompanyDefinedField(companyDefinedField),
  );
};

export interface CreateCompanyDefinedFieldDto {
  table: CompanyDefinedFieldTableEnum;
  name: string;
  dataType: CompanyDefinedFieldDataTypeEnum;
}

const createCompanyDefinedField = (
  createCompanyDefinedFieldDto: CreateCompanyDefinedFieldDto,
): Promise<AxiosResponse<CompanyDefinedField>> => {
  return request.post(
    apiRoutes.createCompanyDefinedField(),
    createCompanyDefinedFieldDto,
  );
};

export interface ToggleColumnIsActiveResponse {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  companyId: string;
  table: string;
  name: string;
  dataType: 'string' | 'number' | 'boolean' | 'date';
  isActive: boolean;
}

const toggleColumnIsActive = async (
  companyDefinedFieldId: string,
): Promise<AxiosResponse<ToggleColumnIsActiveResponse>> => {
  return request.put(apiRoutes.toggleColumnIsActive(companyDefinedFieldId));
};

const getCompanyBusinessHoursPreferences = (): Promise<
  AxiosResponse<CompanyBusinessHoursPreferencesDto>
> => {
  return request.get(apiRoutes.getCompanyBusinessHoursPreferences());
};

const updateCompanyBusinessHoursPreferences = (
  updateCompanyBusinessHoursDto: CompanyBusinessHoursPreferencesDto,
) => {
  return request.post(
    apiRoutes.updateCompanyBusinessHoursPreferences(),
    updateCompanyBusinessHoursDto,
  );
};

const getCompanyBillingSettings = (): Promise<
  AxiosResponse<BillingSettings>
> => {
  return request.get(apiRoutes.getCompanyBillingSettings());
};

const toggleDefaultColumnIsActive = async (columnId: string) => {
  return request.post(apiRoutes.toggleDefaultDisabledColumn(columnId));
};

const getCompanyGupshupProfile = () => {
  return request.get<GupshupProfile>(apiRoutes.getCompanyGupshupProfile());
};

const updateGupshupProfile = (
  updateGupshupProfileDto: UpdateGupshupProfileDto,
) => {
  return request.patch(
    apiRoutes.updateGupshupProfile(),
    updateGupshupProfileDto,
  );
};

const updateGupshupProfilePhoto = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return request.put(apiRoutes.updateGupshupProfilePhoto(), formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const CompaniesService = {
  getCompanyDetails,
  updateCompany,
  getCompanyMessageBalance,
  getCompanyMessageBalanceByCategory,
  listCompanyDefinedFields,
  createCompanyDefinedField,
  listValuesInCompanyDefinedField,
  toggleColumnIsActive,
  getCompanyBusinessHoursPreferences,
  updateCompanyBusinessHoursPreferences,
  getCompanyBillingSettings,
  toggleDefaultColumnIsActive,
  getCompanyGupshupProfile,
  updateGupshupProfile,
  updateGupshupProfilePhoto,
};
