import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { Tag, TagWithCount } from '../types/Tag';

export interface CreateTagDto {
  name: string;
}

export interface UpdateTagDto {
  id: string;
  name: string;
}

const listTags = async (
  tagsIds?: string,
): Promise<AxiosResponse<TagWithCount[]>> => {
  return request.get(apiRoutes.listTags(tagsIds));
};

const createTag = async (
  tag: Pick<Tag, 'name'>,
): Promise<AxiosResponse<Tag>> => {
  return request.post(apiRoutes.createTag(), tag);
};

const updateTag = async (tag: UpdateTagDto) => {
  const { id, ...updateData } = tag;
  return request.put(apiRoutes.updateTag(id), updateData);
};

const deleteTags = async (tagIds: string): Promise<AxiosResponse<void>> => {
  return request.delete(apiRoutes.deleteTags(tagIds));
};

export const TagsService = {
  listTags,
  createTag,
  updateTag,
  deleteTags,
};
