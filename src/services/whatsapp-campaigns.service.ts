import { PaginatedResponse } from './../types/PaginatedResponse.d';
import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { WhatsappCampaignStatsData } from '../types/WhatsappCampaignsData';

const listWhatsappCampaigns = async (
  page: number,
  perPage: number,
  templateIds?: string,
): Promise<AxiosResponse<PaginatedResponse<WhatsappCampaignStatsData>>> => {
  return request.get(
    apiRoutes.listWhatsappCampaigns(page, perPage, templateIds),
  );
};

const getWhatsappCampaignDetails = async (
  campaignId: string,
): Promise<AxiosResponse<WhatsappCampaignStatsData>> => {
  return request.get(apiRoutes.getWhatsappCampaignDetails(campaignId));
};

export interface SendOrScheduleWhatsappCampaignDto {
  templateId: string;
  customerIds: string[];
  templateArgs?: {
    [key: string]: string | undefined;
  };
  filterCriteria?: string;
  scheduledExecutionTime?: string | null;
}

const sendOrScheduleWhatsappCampaign = async (
  SendOrScheduleWhatsappCampaignDto: SendOrScheduleWhatsappCampaignDto,
) => {
  return request.post(
    apiRoutes.sendOrScheduleWhatsappCampaign(),
    SendOrScheduleWhatsappCampaignDto,
    { timeout: 90000 },
  );
};

const cancelWhatsappCampaign = async (campaignId: string) => {
  return request.post(apiRoutes.cancelWhatsappCampaign(campaignId));
};

const listCustomersOptOut = async (campaignId: string) => {
  return request.get(apiRoutes.listCustomersOptOut(campaignId));
};

const listWhatsappCampaignDetailedReplies = async (campaignId: string) => {
  return request.get(apiRoutes.listWhatsappCampaignDetailedReplies(campaignId));
};

export const WhatsappCampaignsService = {
  listWhatsappCampaigns,
  getWhatsappCampaignDetails,
  sendOrScheduleWhatsappCampaign,
  cancelWhatsappCampaign,
  listCustomersOptOut,
  listWhatsappCampaignDetailedReplies,
};
