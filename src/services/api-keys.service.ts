import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { AxiosResponse } from 'axios';
import { ApiKey, SourceIntegration } from '../types/Prisma';

const getApiKey = async (
  integration: SourceIntegration,
  name: string,
): Promise<AxiosResponse<ApiKey>> => {
  return request.get(apiRoutes.getApiKey(integration, name));
};

export interface CreateApiKeyDto {
  integration: SourceIntegration;
  name: string;
}

const createApiKey = async (
  createApiKeyDto: CreateApiKeyDto,
): Promise<AxiosResponse<ApiKey>> => {
  return request.post(apiRoutes.createApiKey(), createApiKeyDto);
};

const generateNewApiKey = async (
  apiKeyId: string,
): Promise<AxiosResponse<ApiKey>> => {
  return request.patch(apiRoutes.generateNewApiKey(apiKeyId));
};

export const ApiKeysService = {
  getApi<PERSON><PERSON>,
  create<PERSON>pi<PERSON><PERSON>,
  generateNewApiKey,
};
