import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { EmailDomain } from '../types/Prisma';

export interface CreateEmailDomainDto {
  domain: string;
  address: string[];
  mailFromDomain: string;
}

const createEmailDomain = async (
  createEmailDomainDto: CreateEmailDomainDto,
): Promise<EmailDomain> => {
  return request.post(apiRoutes.createEmailDomain(), createEmailDomainDto);
};

const listEmailDomains = async (): Promise<AxiosResponse<EmailDomain[]>> => {
  return request.get(apiRoutes.listEmailDomains());
};

const checkEmailDomainStatus = async (): Promise<EmailDomain[]> => {
  return request.get(apiRoutes.checkEmailDomainStatus());
};

export const EmailDomainsService = {
  createEmailDomain,
  listEmailDomains,
  checkEmailDomainStatus,
};
