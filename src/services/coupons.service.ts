import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { CouponStep } from '../types/CouponStepEnum';
import { SourceIntegration } from '../types/Prisma';
import { PaginatedResponse } from '../types/PaginatedResponse';
import { CouponData } from '../types/CouponData';

export interface ListCashbackCouponsParams {
  perPage?: number;
  page?: number;
  source: SourceIntegration;
  startDate?: Date;
  endDate?: Date;
  couponSteps?: CouponStep[];
  isActive?: boolean;
  isUsed?: boolean;
  searchQuery?: string;
}

const listCashbackCoupons = async (
  params: ListCashbackCouponsParams,
): Promise<AxiosResponse<PaginatedResponse<CouponData>>> => {
  const couponStepsString = params.couponSteps?.length
    ? params.couponSteps.join(',')
    : undefined;

  return request.get(
    apiRoutes.listCashbackCoupons({
      ...params,
      couponSteps: couponStepsString,
    }),
  );
};

export const CouponsService = {
  listCashbackCoupons,
};
