import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { CashbackConfig, SourceIntegration } from '../types/Prisma';

const listCashbackConfig = async (
  integration: string,
): Promise<AxiosResponse<CashbackConfig>> => {
  return request.get(apiRoutes.listCashbackConfig(integration));
};

interface DiscountType {
  percentage: 'percentage';
  fixed_in_cents: 'fixed_in_cents';
}

export interface CreateCashbackConfigDto {
  companyId: string;
  discountValue: number;
  discountType: DiscountType;
  daysToExpire: number;
  cumulative?: boolean;
  minOrderValue?: number;
  maxOrderValue?: number;
  integration: SourceIntegration;
  creationTemplateId: string;
  reminderTemplateId: string;
  reminderDays: number;
  statusTrigger: string;
  isActive?: boolean;
}

export interface UpdateCashbackConfigDto extends CreateCashbackConfigDto {}

const createCashbackConfig = async (
  createCashbackConfigDto: CreateCashbackConfigDto,
): Promise<AxiosResponse<CashbackConfig>> => {
  return request.post(
    apiRoutes.createCashbackConfig(),
    createCashbackConfigDto,
  );
};

const updateCashbackConfig = async (
  cashbackConfigId: string,
  updateCashbackConfigDto: UpdateCashbackConfigDto,
): Promise<AxiosResponse<CashbackConfig>> => {
  return request.put(
    apiRoutes.updateCashbackConfig(cashbackConfigId),
    updateCashbackConfigDto,
  );
};

const toggleCashback = async (
  cashbackConfigId: string,
): Promise<AxiosResponse<CashbackConfig>> => {
  return request.put(apiRoutes.toggleCashback(cashbackConfigId));
};

export const CashbackConfigsService = {
  listCashbackConfig,
  createCashbackConfig,
  updateCashbackConfig,
  toggleCashback,
};
