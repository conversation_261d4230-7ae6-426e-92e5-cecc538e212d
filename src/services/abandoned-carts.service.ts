import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

export enum AbandonedCartStatus {
  FLOW_TRIGGERED = 'flow_triggered',
  ABANDONED = 'abandoned',
  WHATSAPP_SENT = 'whatsapp_sent',
  FAILED = 'failed',
}

interface AbandonedCart {
  createdAt: string;
  updatedAt: string;
  id: string;
  sourceCreatedAt: string;
  sourceId: string | null;
  sourceOrderId: string | null;
  customerPhoneNumber: string | null;
  customerEmail: string | null;
  customerName: string;
  phoneNumberId: string;
  companyId: string;
  source: string;
  status: AbandonedCartStatus;
  recoveryMessageId: string | null;
  value: number | null;
  cartUrl: string | null;
  errorMessage: string | null;
  scheduledSendTime: string | null;
  products: any[] | null;
}

interface Meta {
  page: number;
  perPage: number;
  totalPages: number;
  totalItems: number;
}

export interface AbandonedCartResponse {
  data: AbandonedCart[];
  meta: Meta;
}

const listAbandonedCarts = async (params?: {
  perPage?: number;
  page?: number;
  startDate?: Date;
  endDate?: Date;
  status?: string;
  searchQuery?: string;
  createdAtOrder?: 'asc' | 'desc';
}): Promise<AbandonedCartResponse> => {
  const {
    perPage = 20,
    page = 1,
    startDate,
    endDate,
    status,
    searchQuery,
    createdAtOrder,
  } = params ?? {};

  const response = await request.get(
    apiRoutes.listAbandonedCarts({
      perPage,
      page,
      startDate,
      endDate,
      status,
      searchQuery,
      createdAtOrder,
    }),
  );
  return response.data;
};

export const AbandonedCartsService = {
  listAbandonedCarts,
};
