import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { OrderProduct } from '../types/OrderProduct';

export interface ListOrderProductsDto {
  search?: string;
  productIds?: string[];
}
const listOrderProducts = async (
  listOrderProductsDto: ListOrderProductsDto,
): Promise<AxiosResponse<OrderProduct[]>> => {
  return request.post(apiRoutes.listOrderProducts(), listOrderProductsDto);
};

export const OrderProductsService = {
  listOrderProducts,
};
