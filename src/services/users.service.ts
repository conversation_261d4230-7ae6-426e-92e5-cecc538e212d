import { AxiosResponse } from 'axios';
import { request } from '../constants/request';
import { User } from '../types/Prisma';
import { apiRoutes } from '../constants/api-routes';

const listCompanyAgents = async (): Promise<AxiosResponse<User[]>> => {
  return request.get(apiRoutes.listCompanyAgents());
};

const listUsers = async (): Promise<AxiosResponse<User[]>> => {
  return request.get(apiRoutes.listUsers());
};

export const UsersService = {
  listCompanyAgents,
  listUsers,
};
