import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import {
  DebugImpersonateDto,
  SetMyAppModulesPermissionsDto,
  SimulateMessageReceiveDto,
} from '../types/Debug';
import { Message, User } from '../types/Prisma';
import { AppModule } from '../types/AppModule';

const debugSetMyAppModulesPermissions = async (
  args: SetMyAppModulesPermissionsDto,
) => {
  return request.post<
    SetMyAppModulesPermissionsDto,
    AxiosResponse<AppModule[]>
  >(apiRoutes.debugSetMyAppModulesPermissions(), args);
};

const debugImpersonate = async (args: DebugImpersonateDto) => {
  return request.post<
    DebugImpersonateDto,
    AxiosResponse<{ accessToken: string }>
  >(apiRoutes.debugImpersonate(), args);
};

const debugListAvailableUsersForImpersonate = async () => {
  return request.get<undefined, AxiosResponse<User[]>>(
    apiRoutes.debugListAvailableUsersForImpersonate(),
  );
};

const debugSimulateMessageReceive = async (args: SimulateMessageReceiveDto) => {
  return request.post<SimulateMessageReceiveDto, AxiosResponse<Message>>(
    apiRoutes.debugSimulateMessageReceive(),
    args,
  );
};

export const DebugService = {
  debugSetMyAppModulesPermissions,
  debugImpersonate,
  debugListAvailableUsersForImpersonate,
  debugSimulateMessageReceive,
};
