import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { CreateRoleDto, RoleWithIncludes, UpdateRoleDto } from '../types/Role';

const listRoles = async (): Promise<AxiosResponse<RoleWithIncludes[]>> => {
  return request.get(apiRoutes.listRoles());
};

const getRole = async (
  roleId: string,
): Promise<AxiosResponse<RoleWithIncludes>> => {
  return request.get(apiRoutes.getRole(roleId));
};

const createRole = async (
  createRoleDto: CreateRoleDto,
): Promise<AxiosResponse<RoleWithIncludes>> => {
  return request.post(apiRoutes.createRole(), createRoleDto);
};

const updateRole = async (
  roleId: string,
  updateRoleDto: UpdateRoleDto,
): Promise<AxiosResponse<RoleWithIncludes>> => {
  return request.patch(apiRoutes.updateRole(roleId), updateRoleDto);
};

const deleteRole = async (roleId: string): Promise<AxiosResponse<void>> => {
  return request.delete(apiRoutes.deleteRole(roleId));
};

export const RolesService = {
  listRoles,
  getRole,
  createRole,
  updateRole,
  deleteRole,
};
