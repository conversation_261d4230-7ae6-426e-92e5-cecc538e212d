import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { Log, LogSource, LogType } from '../types/Logs';

export interface CreateInfoLogDto {
  message: string;
  type: LogType;
  source: LogSource;
  meta?: Record<string, unknown> | unknown[];
}

const createInfoLog = async (
  data: CreateInfoLogDto,
): Promise<AxiosResponse<Log>> => {
  return request.post(apiRoutes.createInfoLog(), data);
};

export const LogsService = {
  createInfoLog,
};
