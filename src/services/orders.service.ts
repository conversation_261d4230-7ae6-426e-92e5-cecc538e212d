import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { FormDataUtils } from '../utils/form-data.utils';
import { Order } from '../types/Prisma';
import { ListFieldValuesResponse } from '../types/FieldValuesResponse';

export interface UploadOrdersDto extends Record<any, any> {}

const uploadOrders = async (uploadOrdersDto: UploadOrdersDto) => {
  const { file } = uploadOrdersDto;
  const data = FormDataUtils.convertJsonToFormData({
    file,
  });

  return request.post(apiRoutes.uploadOrders(), data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 60000,
  });
};

const listOrderFieldValues = async (
  field: keyof Order,
  source?: string,
): Promise<AxiosResponse<ListFieldValuesResponse>> => {
  return request.get(apiRoutes.listOrderFieldValues(field, source));
};

export const OrdersService = {
  uploadOrders,
  listOrderFieldValues,
};
