import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { ReportBufferData } from '../types/ReportBufferData';
import ReportOverviewData from '../types/ReportOverviewData';
import { DateUtils } from '../utils/date.utils';

export interface StartedSessionsByHourAndDayOfWeekReport {
  weekDay: number;
  hour: number;
  count: number;
}

export interface ChatAgentMetricsReport {
  agentId: string;
  agentName?: string;
  allocatedTickets: number;
  answeredTickets: number;
  messagesSent: number;
  messagesReceived: number;
  finishedTickets: number;
  medianTimeToFirstResponse: number;
  medianTimeToFinish: number;
}

const exportFlowClicksReport = async (
  flowId: string,
): Promise<AxiosResponse<ReportBufferData>> => {
  return request.get(apiRoutes.exportFlowClicksReport(flowId), {
    timeout: 240000,
  });
};

const getOverview = async (
  startDate: string,
  endDate: string,
): Promise<AxiosResponse<ReportOverviewData>> => {
  return request.get(apiRoutes.getReportOverview(startDate, endDate));
};

const exportCampaignSalesReport = async ({
  startDate,
  endDate,
  campaignIds,
}: {
  startDate?: string;
  endDate?: string;
  campaignIds?: string[];
}): Promise<AxiosResponse<ReportBufferData>> => {
  return request.get(
    apiRoutes.exportCampaignSalesReport(startDate, endDate, campaignIds),
    {
      timeout: 120000,
    },
  );
};

const exportCampaignEmailReport = async ({
  startDate,
  endDate,
  campaignIds,
}: {
  startDate?: string;
  endDate?: string;
  campaignIds?: string[];
}): Promise<AxiosResponse<ReportBufferData>> => {
  return request.get(
    apiRoutes.exportCampaignEmailReport(startDate, endDate, campaignIds),
    {
      timeout: 120000,
    },
  );
};

const exportCampaignSmsReport = async ({
  startDate,
  endDate,
  campaignIds,
}: {
  startDate?: string;
  endDate?: string;
  campaignIds?: string[];
}): Promise<AxiosResponse<ReportBufferData>> => {
  return request.get(
    apiRoutes.exportCampaignSmsReport(startDate, endDate, campaignIds),
    {
      timeout: 120000,
    },
  );
};

const getStartedSessionsByHourAndDayOfWeekReport = async (
  startDate: Date,
  endDate: Date,
): Promise<AxiosResponse<StartedSessionsByHourAndDayOfWeekReport[]>> => {
  return await request.get(
    apiRoutes.getStartedSessionsByHourAndDayOfWeekReport(startDate, endDate),
  );
};

const getChatAgentMetricsReport = async (
  startDate: Date,
  endDate: Date,
): Promise<AxiosResponse<ChatAgentMetricsReport[]>> => {
  if (!DateUtils.isValidDate(startDate) || !DateUtils.isValidDate(endDate)) {
    Promise.reject('Invalid date');
  }

  return request.get(apiRoutes.getChatAgentMetricsReport(startDate, endDate));
};

const exportAttendanceMetricsReport = async (
  startDate: string,
  endDate: string,
): Promise<AxiosResponse<ReportBufferData>> => {
  return request.get(
    apiRoutes.exportAttendanceMetricsReport(startDate, endDate),
  );
};

export const ReportsService = {
  exportFlowClicksReport,
  getOverview,
  exportCampaignSalesReport,
  exportCampaignSmsReport,
  exportCampaignEmailReport,
  exportAttendanceMetricsReport,
  getStartedSessionsByHourAndDayOfWeekReport,
  getChatAgentMetricsReport,
};
