import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

export interface WebPushCredentials {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

interface SubscribeToWebPushDto {
  credentials: WebPushCredentials;
}

interface SendWebPushTestNotificationDto {
  credentials: WebPushCredentials;
}

interface ValidateWebPushCredentialsDto {
  credentials: WebPushCredentials;
}

const getWebPushVapidPublicKey = async () => {
  return request.get<{ vapidPublicKey: string | null }>(
    apiRoutes.getWebPushVapidPublicKey(),
  );
};

const subscribeToWebPush = async (
  subscribeToWebPushDto: SubscribeToWebPushDto,
) => {
  return request.post(apiRoutes.subscribeToWebPush(), subscribeToWebPushDto);
};

const removeWebPushSubscription = async () => {
  return request.delete(apiRoutes.removeWebPushSubscription());
};

const sendWebPushTestNotification = async (
  sendWebPushTestNotificationDto: SendWebPushTestNotificationDto,
) => {
  return request.post<void>(
    apiRoutes.sendWebPushTestNotification(),
    sendWebPushTestNotificationDto,
  );
};

const validateWebPushCredentials = async (
  validateWebPushCredentialsDto: ValidateWebPushCredentialsDto,
) => {
  return request.post<{ isValid: boolean }>(
    apiRoutes.validateWebPushCredentials(),
    validateWebPushCredentialsDto,
  );
};

export const WebPushService = {
  getWebPushVapidPublicKey,
  subscribeToWebPush,
  removeWebPushSubscription,
  sendWebPushTestNotification,
  validateWebPushCredentials,
};
