import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

export interface SendEmailTemplateTestDto {
  emailTemplateId: string;
  recipientName: string;
  recipientEmail: string;
  templateArgs: Record<string, string | undefined | null>;
}

export type RecipientSeparator = ',' | ';' | '|' | ' ';

export interface SendSpamTestDto {
  emailTemplateId: string;
  separator: RecipientSeparator;
  recipients: string;
  templateArgs?: Record<string, string | undefined>;
}

export interface CustomerNameAndCompanyName {
  customerName: string;
  companyName: string | undefined;
}

const sendEmailTemplateTest = async (
  sendEmailTemplateTestDto: SendEmailTemplateTestDto,
) => {
  return request.post(
    apiRoutes.sendEmailTemplateTest(),
    sendEmailTemplateTestDto,
  );
};

const sendSpamTest = async (sendSpamTestDto: SendSpamTestDto) => {
  return request.post(apiRoutes.sendSpamTest(), sendSpamTestDto);
};

const emailOptOut = async (
  customerId: string,
): Promise<CustomerNameAndCompanyName> => {
  return (await request.post(apiRoutes.emailOptOut(customerId))).data;
};

export const EmailService = {
  sendEmailTemplateTest,
  sendSpamTest,
  emailOptOut,
};
