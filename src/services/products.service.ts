import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { Product } from '../types/Product';
import { ListFieldValuesResponse } from '../types/FieldValuesResponse';
import { PaginatedResponse } from '../types/PaginatedResponse';
import { SourceIntegration } from '../types/Prisma';

export type ListProductsParams = {
  page?: number;
  perPage?: number;
  searchQuery?: string;
  status?: string;
  source?: string;
  integrationConfigId?: string;
  minPrice?: number;
  maxPrice?: number;
  minStock?: number;
  maxStock?: number;
  sortBy?: string;
  sortOrder?: string;
};

const listProducts = async (
  params?: ListProductsParams,
): Promise<AxiosResponse<PaginatedResponse<any>>> => {
  return request.get(apiRoutes.listProducts(params));
};

const listProductDetails = async (
  productId: string,
): Promise<AxiosResponse<any>> => {
  return request.get(apiRoutes.listProductDetails(productId));
};

const syncProducts = async (
  source: SourceIntegration,
): Promise<AxiosResponse<Product[]>> => {
  return request.post(apiRoutes.syncProducts(source), { source });
};

const listProductVariantsFieldValues = async (
  field: string,
  source?: string,
): Promise<AxiosResponse<ListFieldValuesResponse>> => {
  return request.get(apiRoutes.listProductVariantsFieldValues(field, source));
};

export const ProductsService = {
  listProducts,
  listProductDetails,
  syncProducts,
  listProductVariantsFieldValues,
};
