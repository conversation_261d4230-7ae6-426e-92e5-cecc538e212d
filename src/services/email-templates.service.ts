import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { EmailTemplate, EmailTemplateType } from '../types/Prisma';

export interface CreateEmailTemplateDto {
  name: string;
  subject: string;
  type: EmailTemplateType;
  html: string;
  text: string;
  unlayerDesign: JSON;
}

export interface ValidateEmailTemplateDto {
  type: EmailTemplateType;
  html: string;
}

export interface ListEmailTemplatesDto {
  type?: EmailTemplateType;
}

const listEmailTemplates = async (
  params?: ListEmailTemplatesDto,
): Promise<AxiosResponse<EmailTemplate[]>> => {
  return request.get(apiRoutes.listEmailTemplates(params));
};

const deleteEmailTemplate = async (emailTemplateId: string): Promise<void> => {
  return request.delete(apiRoutes.deleteEmailTemplate(emailTemplateId));
};

const createEmailTemplate = async (
  createEmailTemplateDto: CreateEmailTemplateDto,
) => {
  return request.post(apiRoutes.createEmailTemplate(), createEmailTemplateDto, {
    timeout: 30000,
  });
};

const validateEmailTemplate = async (
  createEmailTemplateDto: ValidateEmailTemplateDto,
) => {
  return request.post(
    apiRoutes.validateEmailTemplate(),
    createEmailTemplateDto,
    {
      timeout: 30000,
    },
  );
};

const updateEmailTemplate = async (
  emailTemplateId: string,
  createEmailTemplateDto: CreateEmailTemplateDto,
) => {
  return request.put(
    apiRoutes.updateEmailTemplate(emailTemplateId),
    createEmailTemplateDto,
    {
      timeout: 30000,
    },
  );
};

export const EmailTemplatesService = {
  listEmailTemplates,
  deleteEmailTemplate,
  createEmailTemplate,
  updateEmailTemplate,
  validateEmailTemplate,
};
