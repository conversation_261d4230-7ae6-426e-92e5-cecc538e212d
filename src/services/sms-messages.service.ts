import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

export interface SendSmsTemplateTestDto {
  recipientName: string;
  templateArgs: Record<string, string>;
  templateId: string;
  phoneNumber: string;
}

const sendSmsTemplateTest = async (
  sendSmsTemplateTestDto: SendSmsTemplateTestDto,
): Promise<AxiosResponse<boolean>> => {
  return request.post(apiRoutes.sendSmsTemplateTest(), sendSmsTemplateTestDto);
};
export const SmsMessagesService = {
  sendSmsTemplateTest,
};
