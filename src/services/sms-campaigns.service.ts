import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { SmsCampaignStatsData } from '../types/SmsCampaignStatsData';
import { PaginatedResponse } from '../types/PaginatedResponse';

const listSmsCampaigns = async (params: {
  page?: number;
  perPage?: number;
}): Promise<AxiosResponse<PaginatedResponse<SmsCampaignStatsData>>> => {
  const { page = 1, perPage = 10 } = params;
  return request.get(apiRoutes.listSmsCampaigns(page, perPage));
};

// const getSmsCampaignDetails = async (
//   campaignId: string
// ): Promise<AxiosResponse<SmsCampaignStatsData>> => {
//   return request.get(apiRoutes.getSmsCampaignDetails(campaignId));
// };

export interface SendOrScheduleSmsCampaignDto {
  templateId: string;
  customerIds: string[];
  templateArgs?: {
    [key: string]: string | undefined;
  };
  filterCriteria?: string;
  scheduledExecutionTime?: string | null;
}

const sendOrScheduleSmsCampaign = async (
  data: SendOrScheduleSmsCampaignDto,
): Promise<AxiosResponse> => {
  return request.post(apiRoutes.sendOrScheduleSmsCampaign(), data);
};

const cancelSmsCampaign = async (
  campaignId: string,
): Promise<AxiosResponse> => {
  return request.post(apiRoutes.cancelSmsCampaign(campaignId));
};

export const SmsCampaignsService = {
  listSmsCampaigns,
  // getSmsCampaignDetails,
  sendOrScheduleSmsCampaign,
  cancelSmsCampaign,
};
