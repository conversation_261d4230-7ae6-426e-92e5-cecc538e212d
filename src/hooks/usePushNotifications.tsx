import { useState, useEffect, useCallback } from 'react';
import {
  WebPushService,
  WebPushCredentials,
} from '../services/web-push.service';
import { WebPushUtils } from '../utils/web-push.utils';
import { useMutation, UseMutationResult, useQuery } from 'react-query';
import { apiRoutes } from '../constants/api-routes';
import { useToast } from '@chakra-ui/react';
import { AxiosResponse } from 'axios';

interface UsePushNotificationsReturn {
  isSupported: boolean;
  isEnabled: boolean;
  permission: NotificationPermission;
  isLoading: boolean;
  enablePushNotifications: () => Promise<void>;
  disablePushNotifications: () => Promise<void>;
  sendTestPushNotificationMutation: UseMutationResult<
    AxiosResponse<void, any>,
    unknown,
    void,
    unknown
  >;
}

export const usePushNotifications = (): UsePushNotificationsReturn => {
  const toast = useToast();

  const [vapidPublicKey, setVapidPublicKey] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  const [credentials, setCredentials] = useState<WebPushCredentials | null>(
    null,
  );
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [permission, setPermission] =
    useState<NotificationPermission>('default');

  const { isLoading: isLoadingVapidPublicKey } = useQuery({
    queryKey: apiRoutes.getWebPushVapidPublicKey(),
    queryFn: async () => {
      const { data } = await WebPushService.getWebPushVapidPublicKey();
      return data;
    },
    onSuccess: (data) => {
      setVapidPublicKey(data.vapidPublicKey ?? null);
    },
  });

  const {
    isLoading: isLoadingValidateWebPushCredentials,
    mutateAsync: validateWebPushCredentialsMutateAsync,
  } = useMutation({
    mutationFn: WebPushService.validateWebPushCredentials,
    onSuccess: () => {
      setIsEnabled(true);
    },
  });

  const {
    mutateAsync: subscribeToWebPushMutateAsync,
    isLoading: isLoadingSubscribeToWebPush,
  } = useMutation({
    mutationFn: WebPushService.subscribeToWebPush,
    onSuccess: () => {
      setIsEnabled(true);
    },
  });

  const {
    mutateAsync: unsubscribeFromWebPushMutationAsync,
    isLoading: isLoadingUnsubscribeFromWebPush,
  } = useMutation({
    mutationFn: WebPushService.removeWebPushSubscription,
    onSuccess: () => {
      setIsEnabled(false);
      setCredentials(null);
    },
  });

  const sendTestPushNotificationMutation = useMutation({
    mutationFn: async () => {
      if (!credentials) {
        throw new Error('Subscription is not set');
      }

      return await WebPushService.sendWebPushTestNotification({
        credentials,
      });
    },
  });

  const checkStatus = useCallback(async (): Promise<void> => {
    const supported = WebPushUtils.isSupported();
    setIsSupported(supported);
    if (supported) {
      const permissionStatus = WebPushUtils.getPermissionStatus();
      setPermission(permissionStatus);
      const subscription =
        await WebPushUtils.getSubscriptionToPushNotifications();

      if (subscription) {
        const parsedSubscription = WebPushUtils.parseSubscription(subscription);
        setCredentials(parsedSubscription);
        const valid = (
          await validateWebPushCredentialsMutateAsync({
            credentials: parsedSubscription,
          })
        ).data.isValid;
        setIsEnabled(valid);
      }
    }
  }, [vapidPublicKey]);

  const enablePushNotifications = useCallback(async (): Promise<void> => {
    if (!vapidPublicKey) {
      throw new Error('VAPID_PUBLIC_KEY is not set');
    }

    const subscription =
      await WebPushUtils.locallySubscribeToPushNotifications(vapidPublicKey);

    if (!subscription) {
      throw new Error('Subscription is not set');
    }

    const parsedSubscription = WebPushUtils.parseSubscription(subscription);
    setCredentials(parsedSubscription);
    await subscribeToWebPushMutateAsync({
      credentials: parsedSubscription,
    });
  }, [vapidPublicKey, subscribeToWebPushMutateAsync, credentials]);

  const disablePushNotifications = useCallback(async (): Promise<void> => {
    await WebPushUtils.locallyUnsubscribeFromPushNotifications();
    await unsubscribeFromWebPushMutationAsync();
    setIsEnabled(false);
  }, [unsubscribeFromWebPushMutationAsync]);

  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    isSupported,
    isEnabled,
    permission,
    isLoading:
      isLoadingVapidPublicKey ||
      isLoadingValidateWebPushCredentials ||
      isLoadingSubscribeToWebPush ||
      isLoadingUnsubscribeFromWebPush,
    enablePushNotifications,
    disablePushNotifications,
    sendTestPushNotificationMutation,
  };
};
