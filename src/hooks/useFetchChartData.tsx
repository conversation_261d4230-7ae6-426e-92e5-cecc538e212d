import { useCallback, useEffect, useState } from 'react';
import { request } from '../constants/request';

const useFetchChartData = (requestRoute: string): any => {
  const [data, setData] = useState(null);

  const fetchChartData = useCallback(async () => {
    const response: any = await request.get(requestRoute);
    setData(response.data);
  }, [requestRoute]);

  useEffect(() => {
    fetchChartData();
  }, [fetchChartData]);

  return [data, setData];
};

export default useFetchChartData;
