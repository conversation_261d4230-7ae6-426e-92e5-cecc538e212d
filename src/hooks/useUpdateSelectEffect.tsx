import { useEffect } from 'react';
import { SelectOption } from '../components/InputSelect';
import { CustomerFiltersEnum } from '../types/CustomerFiltersEnum';

const useUpdateSelectEffect = (
  options: SelectOption[],
  selectedValues: any,
  valueToSet: CustomerFiltersEnum,
  updateFn: (args: any) => void
) => {
  useEffect(() => {
    if (options.length > 0) {
      updateFn({
        selectedValues,
        sourcedata: options,
        valueToSet,
        optionValue: 'value',
        optionLabel: 'label',
      });
    }
  }, [options, selectedValues, updateFn, valueToSet]);
};

export default useUpdateSelectEffect;