import { useEffect, useState } from 'react';
import { DropResult } from 'react-beautiful-dnd';

const removeFromList = (list: any, index: any) => {
  const listCopy = { ...list };
  const result = Array.from(listCopy.items);
  const [removed] = result.splice(index, 1);
  listCopy.items = result;
  return [removed, listCopy];
};

const addToList = (list: any, index: any, element: any) => {
  const listCopy = { ...list };
  const result = Array.from(listCopy.items);
  result.splice(index, 0, element);
  listCopy.items = result;
  return listCopy;
};

interface UseDragAndDropProps<T> {
  elements: any;
}

const useDragAndDrop = <T>({
  elements: providedElements,
}: UseDragAndDropProps<T>) => {
  const [elements, setElements] = useState<{
    [key: string]: {
      name: string;
      items: any;
    };
  }>({});

  useEffect(() => {
    setElements(providedElements);
  }, [providedElements]);

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }
    const listCopy: any = { ...elements };

    const sourceList = listCopy[result.source.droppableId];
    const [removedElement, newSourceList] = removeFromList(
      sourceList,
      result.source.index,
    );
    listCopy[result.source.droppableId] = newSourceList;
    const destinationList = listCopy[result.destination.droppableId];
    listCopy[result.destination.droppableId] = addToList(
      destinationList,
      result.destination.index,
      removedElement,
    );
    setElements(listCopy);
  };

  return {
    onDragEnd,
    elements,
  };
};

export default useDragAndDrop;
