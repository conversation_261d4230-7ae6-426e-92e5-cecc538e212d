import { useContext, useCallback } from 'react';
import { EventContext } from '../contexts/EventContext';
import { EventsEnum } from '../constants/events';

export function useEventEmitter<T = unknown>() {
  const { emit } = useContext(EventContext);

  const emitEvent = useCallback(
    (type: EventsEnum, detail: T) => {
      emit(type, detail);
    },
    [emit],
  );

  return {
    emitEvent,
  };
}
