import { useSelector } from 'react-redux';
import { appRoutes } from '../routes/app.routes';
import { RootState } from '../state/store';
import { AppModule } from '../types/AppModule';
import { useCallback } from 'react';

export const useAppModuleAccessGuard = () => {
  const { currentUser } = useSelector((state: RootState) => state.auth);

  const checkUserHasAppModuleAccess = useCallback(
    (appModules: AppModule[] | null) =>
      currentUser?.roleId === null ||
      appModules === null ||
      appModules.some((appModule) =>
        currentUser?.appModulePermissions?.includes(appModule),
      ),
    [currentUser?.appModulePermissions],
  );

  const getAppModuleByPath = useCallback((path: string) => {
    const appRoute = appRoutes.find((route) => route.path === path);
    if (!appRoute) return;
    return appRoute?.appModule;
  }, []);

  const checkUserHasPathAccess = useCallback(
    (path: string) => {
      const appModules = getAppModuleByPath(path);
      if (appModules === undefined) return false;

      return checkUserHasAppModuleAccess(appModules);
    },
    [checkUserHasAppModuleAccess, getAppModuleByPath],
  );

  return {
    checkUserHasAppModuleAccess,
    checkUserHasPathAccess,
    getAppModuleByPath,
  };
};
