import { useCallback, useState } from 'react';
import { Accept, useDropzone } from 'react-dropzone';
import useFileValidation from './useFileValidation';

interface UseFileDropzoneProps {
  accept?: Accept;
  maxFiles?: number;
  multiple?: boolean;
}

const useFileDropzone = ({
  accept,
  maxFiles,
  multiple,
}: UseFileDropzoneProps = {}) => {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | ArrayBuffer | null>(null);
  const { validateFile } = useFileValidation();

  const onDrop = useCallback(
    (acceptedFiles: Array<File>) => {
      const file = acceptedFiles[0];
      if (!file || !validateFile(file)) {
        return;
      }

      const fileReader = new FileReader();
      fileReader.onload = function () {
        setPreview(fileReader.result);
      };
      fileReader.readAsDataURL(file);
      setFile(file);
    },
    [validateFile],
  );

  const dropzone = useDropzone({
    onDrop,
    accept,
    maxFiles,
    multiple,
  });

  function clear() {
    setFile(null);
    setPreview(null);
  }

  return {
    file,
    dropzone,
    preview,
    clear,
  };
};

export default useFileDropzone;
