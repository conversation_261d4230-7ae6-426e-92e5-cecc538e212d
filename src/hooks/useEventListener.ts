import { useContext, useEffect, useRef } from 'react';
import { EventContext } from '../contexts/EventContext';
import { EventsEnum } from '../constants/events';

export function useEventListener<T = unknown>(
  type: EventsEnum,
  callback: (event: CustomEvent<T>) => void,
) {
  const { on, off } = useContext(EventContext);
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    const handler = (event: Event) => {
      if (event instanceof CustomEvent) {
        callbackRef.current(event);
      }
    };

    on(type, handler);
    return () => off(type, handler);
  }, [type, on, off]);
}
