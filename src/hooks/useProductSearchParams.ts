import { useSearchParams } from 'react-router-dom';
import { ProductFiltersEnum } from '../types/ProdutFiltersEnum';

export function useProductSearchParams() {
  const [searchParams] = useSearchParams();

  const source = searchParams.get(ProductFiltersEnum.SOURCE) || '';
  const status = searchParams.get(ProductFiltersEnum.STATUS) || '';
  const searchQuery = searchParams.get(ProductFiltersEnum.SEARCH_QUERY) || '';

  const minPriceStr = searchParams.get(ProductFiltersEnum.MIN_PRICE);
  const maxPriceStr = searchParams.get(ProductFiltersEnum.MAX_PRICE);
  const minStockStr = searchParams.get(ProductFiltersEnum.MIN_STOCK);
  const maxStockStr = searchParams.get(ProductFiltersEnum.MAX_STOCK);
  const pageStr = searchParams.get(ProductFiltersEnum.PAGE);
  const perPageStr = searchParams.get(ProductFiltersEnum.PER_PAGE);

  const minPrice = minPriceStr ? Number(minPriceStr) : undefined;
  const maxPrice = maxPriceStr ? Number(maxPriceStr) : undefined;
  const minStock = minStockStr ? Number(minStockStr) : undefined;
  const maxStock = maxStockStr ? Number(maxStockStr) : undefined;
  const page = pageStr ? Number(pageStr) : 1;
  const perPage = perPageStr ? Number(perPageStr) : 10;

  const sortBy = searchParams.get(ProductFiltersEnum.SORT_BY) as
    | 'nameAsc'
    | 'nameDesc'
    | 'sourceCreatedAtAsc'
    | 'sourceCreatedAtDesc'
    | null;

  return {
    source,
    status,
    searchQuery,
    minPrice,
    maxPrice,
    minStock,
    maxStock,
    sortBy,
    page,
    perPage,
  };
}
