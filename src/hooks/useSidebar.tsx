import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useMediaQuery } from '@chakra-ui/react';
import { screenSizes } from '../constants/screen-sizes';

export const SIDEBAR_WIDTH_COLLAPSED = '80px';
export const SIDEBAR_WIDTH_EXPANDED = '280px';

interface SidebarContextType {
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  sidebarWidth: string;
  isMobile: boolean;
  toggleSidebar: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

interface SidebarProviderProps {
  children: ReactNode;
}

export const SidebarProvider = ({ children }: SidebarProviderProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const isMobile = useMediaQuery(screenSizes.mobile)[0];

  const sidebarWidth = isExpanded
    ? SIDEBAR_WIDTH_EXPANDED
    : SIDEBAR_WIDTH_COLLAPSED;

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded);
  };

  const value: SidebarContextType = {
    isExpanded,
    setIsExpanded,
    sidebarWidth,
    isMobile,
    toggleSidebar,
  };

  return (
    <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>
  );
};

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within SidebarProvider');
  }
  return context;
};
