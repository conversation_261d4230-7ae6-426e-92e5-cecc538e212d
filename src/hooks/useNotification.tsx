import { useEffect, useRef, useState } from 'react';
import sound from '../assets/sounds/notification-2.wav';

interface UseNotificationOptions {
  playOnlyOnBackground: boolean;
}

export const useNotification = (
  { playOnlyOnBackground }: UseNotificationOptions = {
    playOnlyOnBackground: true,
  },
) => {
  const [isTabActive, setIsTabActive] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    audioRef.current = new Audio(sound);
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsTabActive(document.visibilityState === 'visible');
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const updateDocumentTitle = (label: string) => {
    const match = document.title.match(/\((\d+)\)/);
    const count = match ? parseInt(match[1], 10) + 1 : 1;
    document.title = `(${count}) ${label}`;
  };

  const playNewMessageNotification = (label: string) => {
    if (isTabActive && playOnlyOnBackground) return;

    updateDocumentTitle(label);
    audioRef.current?.play();
  };

  return {
    playNewMessageNotification,
  };
};
