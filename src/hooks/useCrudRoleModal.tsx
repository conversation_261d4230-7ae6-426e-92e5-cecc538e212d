import { createContext, useContext, useState } from 'react';
import {
  useDisclosure,
  useToast,
  useId,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Text,
} from '@chakra-ui/react';
import { Controller, ControllerRenderProps, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CrudModal from '../components/CrudModal';
import { RolesService } from '../services/roles.service';
import { Role, User } from '../types/Prisma';
import AlertDialogBase from '../components/AlertDialog';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { apiRoutes } from '../constants/api-routes';
import { colors } from '../constants/colors';
import { t } from 'i18next';
import InputSelect, { SelectOption } from '../components/InputSelect';
import { AppModulesEnum } from '../types/AppModule';
import { omit } from 'lodash';
import { UsersService } from '../services/users.service';
import { CreateRoleDto, UpdateRoleDto } from '../types/Role';
import useGetCrudText from './useGetCrudText';
import ModulesPermissionSelector from '../pages/SettingsPage/BackgroundAccessPage/components/SelectorModulePermission';

const schema = yup.object({
  name: yup.string().required('O nome é obrigatório'),
  appModulePermissions: yup.array().of(yup.string()),
  userIds: yup.array().of(yup.string()),
});

const MODULES_FILTER_OUT: AppModulesEnum[] = [AppModulesEnum.DEBUG_TOOLS];

export type SchemaType = yup.InferType<typeof schema>;

interface CrudRoleModalContextData {
  openCreateRoleModal: () => void;
  openEditRoleModal: (id: string) => void;
  openDeleteRoleAlert: (id: string) => void;
}

export interface ExtendedRole extends Role {
  users?: { id: string; name?: string; email?: string }[];
}

const CrudRoleContext = createContext<CrudRoleModalContextData | undefined>(
  undefined,
);

export const CrudRoleProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { isOpen, onOpen, onClose: baseOnClose } = useDisclosure();
  const {
    isOpen: isAlertOpen,
    onOpen: onOpenAlert,
    onClose: onCloseAlert,
  } = useDisclosure();
  const [roleId, setRoleId] = useState<string | null>(null);
  const { crudActionText, crudTitle } = useGetCrudText(!roleId);
  const {
    handleSubmit,
    formState: { errors },
    setValue,
    control,
    reset,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      appModulePermissions: [],
      userIds: [],
    },
  });
  const toast = useToast();
  const formId = useId();
  const queryClient = useQueryClient();
  const isEditing = !!roleId;

  const onClose = () => {
    reset({
      name: '',
      appModulePermissions: [],
      userIds: [],
    });
    baseOnClose();
  };

  const openCreateRoleModal = () => {
    setRoleId(null);
    reset({
      name: '',
      appModulePermissions: [],
      userIds: [],
    });
    onOpen();
  };

  const openEditRoleModal = (id: string) => {
    setRoleId(id);
    onOpen();

    const cachedRoles = queryClient.getQueryData<ExtendedRole[]>(
      apiRoutes.listRoles(),
    );
    const currentRole = cachedRoles?.find((role) => role.id === id);

    if (currentRole) {
      setValue('name', currentRole.name || '');
      setValue(
        'appModulePermissions',
        (currentRole.appModulePermissions as string[]) || [],
      );
      setValue('userIds', currentRole.users?.map((user) => user.id) || []);
    } else {
      RolesService.getRole(id)
        .then(({ data }) => {
          const roleData = data as ExtendedRole;
          setValue('name', roleData.name || '');
          setValue(
            'appModulePermissions',
            (roleData.appModulePermissions as string[]) || [],
          );
          setValue('userIds', roleData.users?.map((user) => user.id) || []);
        })
        .catch((error) => {
          toast({
            title: 'Erro ao carregar dados do cargo',
            description: 'Não foi possível carregar as informações do cargo',
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        });
    }
  };

  const openDeleteRoleAlert = (id: string) => {
    setRoleId(id);
    onOpenAlert();
  };

  const resetState = () => {
    setRoleId(null);
    reset({
      name: '',
      appModulePermissions: [],
      userIds: [],
    });
    onClose();
    onCloseAlert();
  };

  async function handleClickDelete(roleId: string) {
    deleteMutation.mutate(roleId);
  }

  const appModuleOptions: SelectOption[] = Object.values(AppModulesEnum)
    .filter((module) => !MODULES_FILTER_OUT.includes(module))
    .map((module) => ({
      value: module,
      label: t(`enums.AppModules.${module}`),
    }));

  const { data: usersOptions = [] } = useQuery(
    apiRoutes.listUsers(),
    async () => {
      const { data } = await UsersService.listUsers();
      return data.map((user: User) => ({
        value: user.id,
        label: `${user.name} (${user.email})`,
      }));
    },
  );

  const deleteMutation = useMutation(
    (id: string) => RolesService.deleteRole(id),
    {
      onSuccess: () => {
        toast({
          title: 'Cargo deletado com sucesso!',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listRoles());
        resetState();
      },
      onError: () => {
        toast({
          title: 'Erro ao deletar cargo',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      },
    },
  );

  const createRoleMutation = useMutation(
    (data: CreateRoleDto) => RolesService.createRole(data),
    {
      onSuccess: () => {
        toast({
          title: 'Cargo criado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listRoles());
        resetState();
      },
      onError: () => {
        toast({
          title: 'Erro ao criar cargo',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      },
    },
  );

  const updateRoleMutation = useMutation(
    (data: UpdateRoleDto) => RolesService.updateRole(roleId!, data),
    {
      onSuccess: () => {
        toast({
          title: 'Cargo atualizado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listRoles());
        resetState();
      },
      onError: () => {
        toast({
          title: 'Erro ao atualizar cargo',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      },
    },
  );

  const handleChangeUser = (
    value: SelectOption[],
    field: ControllerRenderProps<SchemaType, 'userIds'>,
  ) => {
    const newValues = value.map((item) => item.value);

    const cachedRoles = queryClient.getQueryData<ExtendedRole[]>(
      apiRoutes.listRoles(),
    );
    const currentRole = cachedRoles?.find((role) => role.id === roleId);

    if (isEditing && currentRole?.users && currentRole.users.length > 0) {
      const currentUserIds = currentRole.users.map(
        (user: { id: string }) => user.id,
      );
      const removedUsers = currentUserIds.filter(
        (id: string) => !newValues.includes(id),
      );

      if (removedUsers.length) {
        toast({
          title: 'Operação não permitida',
          description:
            'Não é possível desalocar um usuário de um cargo, apenas realocá-lo para outro cargo.',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
        return; // Prevent the change
      }
    }

    field.onChange(newValues);
  };

  const onSubmit = (data: SchemaType) => {
    if (isEditing) {
      updateRoleMutation.mutate(data as UpdateRoleDto);
    } else {
      createRoleMutation.mutate(data as CreateRoleDto);
    }
  };

  const isMutateButtonDisabled =
    createRoleMutation.isLoading ||
    updateRoleMutation.isLoading ||
    deleteMutation.isLoading;

  return (
    <CrudRoleContext.Provider
      value={{ openCreateRoleModal, openEditRoleModal, openDeleteRoleAlert }}
    >
      <CrudModal
        isOpen={isOpen}
        onClose={onClose}
        title={`${crudTitle} Cargo`}
        actionButtonText={crudActionText}
        formId={formId}
        isLoading={isMutateButtonDisabled}
      >
        <form id={formId} onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={4} mt={4}>
            <FormControl>
              <FormLabel>Nome do Cargo</FormLabel>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    placeholder="Digite o nome do cargo"
                    {...field}
                    isInvalid={!!errors.name}
                  />
                )}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>

            <FormControl>
              <FormLabel>Módulos permitidos</FormLabel>
              <Controller
                name="appModulePermissions"
                control={control}
                render={({ field }) => (
                  <ModulesPermissionSelector
                    value={(field.value || []).filter(
                      (permission): permission is string =>
                        typeof permission === 'string',
                    )}
                    onChange={(newValue) => field.onChange(newValue)}
                  />
                )}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Usuários</FormLabel>
              <Controller
                name="userIds"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    options={usersOptions}
                    isMulti
                    {...omit(field, 'ref')}
                    value={(field.value || [])
                      .filter((userId): userId is string => !!userId)
                      .map(
                        (userId) =>
                          usersOptions.find(
                            (option) => option.value === userId,
                          ) || {
                            value: userId,
                            label: userId,
                          },
                      )}
                    onChange={(value: SelectOption[]) =>
                      handleChangeUser(value, field)
                    }
                    placeholder="Selecione os usuários para este cargo"
                  />
                )}
              />
            </FormControl>
          </Stack>
        </form>
      </CrudModal>
      {roleId && (
        <AlertDialogBase
          isOpen={isAlertOpen}
          onClose={onCloseAlert}
          title="Deletar cargo"
          onConfirm={() => handleClickDelete(roleId)}
        >
          Tem certeza que deseja deletar este cargo?
        </AlertDialogBase>
      )}
      {children}
    </CrudRoleContext.Provider>
  );
};

export const useCrudRoleModal = () => {
  const ctx = useContext(CrudRoleContext);
  if (!ctx)
    throw new Error(
      'useCrudRoleModal precisa estar dentro do CrudRoleProvider',
    );
  return ctx;
};
