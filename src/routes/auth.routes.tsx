import { Route, Routes } from 'react-router-dom';
import { EmailOptOutPage } from '../pages/EmailOptOutPage';
import React from 'react';
import LoginPage from '../pages/LoginPage';
import { appPaths } from '../constants/app-paths';
import UpdatePasswordWithTokenPage from '../pages/UpdatePasswordWithTokenPage';

const AuthRoutes = () => {
  return (
    <Routes>
      <Route
        path="/cancelar-inscricao/:unsubscribeKey"
        element={<EmailOptOutPage />}
      />
      <Route
        path={appPaths.updatePasswordWithToken()}
        element={<UpdatePasswordWithTokenPage />}
      />
      <Route path="/*" element={<LoginPage />} />
    </Routes>
  );
};

export default AuthRoutes;
