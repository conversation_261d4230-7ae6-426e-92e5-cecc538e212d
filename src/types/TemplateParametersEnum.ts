export enum TemplateParametersEnum {
  CUSTOMER_NAME = '[nome do consumidor]',
  COMPANY_NAME = '[nome da empresa]',
  REPRESENTATIVE_NAME = '[nome do atendente]',
  CUSTOM_TEXT = '[texto personalizado]',
  TRACKING_CODE = '[codigo de rastreio]',
  ABANDONED_CART_PRODUCTS = '[produtos do carrinho abandonado]',
  ORDER_ID = '[id do pedido]',
  COUPON_EXPIRATION_DATE = '[data de expiração do cupom]',
  DAYS_UNTIL_COUPON_EXPIRATION = '[dias para expiração do cupom]',
  COUPON_CODE = '[código do cupom]',
  COUPON_VALUE = '[valor do cupom]',

  // CTA_LINK = '[cta link]',
}

export const TemplateParametersList = Object.values(TemplateParametersEnum);
