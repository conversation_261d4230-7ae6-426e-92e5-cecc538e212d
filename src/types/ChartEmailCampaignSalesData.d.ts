export interface ChartEmailCampaignSalesData {
  templateName: string;
  templateText: string;
  filterCriteria: null | string;
  received: {
    totalOrders: number;
    totalOrdersValue: number;
    count: number;
  };
  read: {
    totalOrders: number;
    totalOrdersValue: number;
    count: number;
  };
  engaged: {
    totalOrders: number;
    totalOrdersValue: number;
    count: number;
  };
  failed: {
    count: number;
  };
  totalMessagesSent: number;
  minDate: string;
  maxDate: string;
}
