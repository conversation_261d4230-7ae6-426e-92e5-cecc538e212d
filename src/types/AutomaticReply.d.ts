import { ConversationCategory } from './ConversationCategory';
import { ConversationTicketStatus } from './ConversationTicket.d';
import { File } from './File';
import Prisma from './Prisma';

export interface AutomaticReply extends Prisma.AutomaticReply {
  createdAt: string;
  updatedAt: string;
  conversationCategory: Pick<ConversationCategory, 'name'> | null;
  file: File;
  conversationTicketStatus: ConversationTicketStatus;
}
