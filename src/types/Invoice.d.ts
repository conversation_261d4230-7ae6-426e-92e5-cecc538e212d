interface InvoiceItem {
  createdAt: string;
  updatedAt: string;
  id: string;
  invoiceId: string;
  name: string;
  unitPrice: number;
  quantity: number;
  totalPrice: number;
  discount: number;
  description: string;
}

export interface Invoice {
  createdAt: string;
  updatedAt: string;
  id: string;
  companyId: string;
  paymentMethod: string;
  value: number;
  dueDate: string;
  referenceMonth: string;
  invoiceItems: InvoiceItem[];
}
