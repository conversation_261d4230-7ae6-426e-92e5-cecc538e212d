import { Button } from '../services/message-templates.service';
import MessageTemplateCard from './MessageTemplateCard';
import Prisma from './Prisma';

export type MessageTemplateStatus = Prisma.MessageTemplateStatus;

type MessageTemplateCategory = Prisma.WhatsappTemplateCategory;

export type MessageTemplateType = Prisma.MessageTemplateType;

export interface MessageTemplate extends Prisma.MessageTemplate {
  createdAt: string;
  updatedAt: string;
}

export interface SimplifiedTemplateMessageButton {
  text: string;
  type: MessageButtonType;
  url: string | null;
}

export interface SimplifiedTemplateMessageCard extends MessageTemplateCard {
  buttons: SimplifiedTemplateMessageButton[];
}

export interface MessageTemplateWithIncludes extends MessageTemplate {
  messageTemplateCards?: SimplifiedTemplateMessageCard[];
  messageTemplateButtons?: Button[];
}
