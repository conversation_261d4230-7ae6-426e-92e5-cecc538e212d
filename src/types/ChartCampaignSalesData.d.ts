export interface ChartCampaignSalesData {
  templateName: string;
  templateText: string;
  filterCriteria: null | string;
  received: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  read: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  engaged: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  failed: {
    count: number;
  };
  campaignCost: number;
  totalMessagesSent: number;
  minDate: string;
  maxDate: string;
}
