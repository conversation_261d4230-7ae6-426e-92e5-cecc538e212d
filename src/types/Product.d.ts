export interface Product {
  id: string;
  name: string;
  title: string;
  description: string;
  sourceId: string;
  source: string;
  sourceCreatedAt: string;
  sourceUpdatedAt: string;
  status: string;
  metadata: any;
  createdAt: string;
  updatedAt: string;
  variants: ProductVariant[];
}

export interface ProductVariant {
  id: string;
  sourceCreatedAt: string;
  sourceUpdatedAt: string;
  status: string;
  name: string;
  title: string;
  description: string;
  sku: string;
  sourceId: string;
  barcode: string;
  price: number;
  salePrice: number;
  costPrice: number;
  stockQuantity: number;
  weight: number;
  width: number;
  height: number;
  length: number;
  imageUrl: string;
  url: string;
  variantCode: string;
  metadata: any;
  createdAt: string;
  updatedAt: string;
}
