import { ConversationCategoryDetailed } from './ConversationCategory';
import Prisma, { ConversationCategory } from './Prisma';

export type ConversationSector = Prisma.ConversationSector;

export interface ConversationSector {
  id: string;
  name: string;
  position: number;
  isDeleted: boolean;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface ConversationSectorWithIncludes extends ConversationSector {
  userConversationSectors: {
    userId: string;
    user?: { name: string; email: string };
  }[];
  categories: (ConversationCategory & {
    _count: {
      conversations: number;
    };
  })[];
}

export interface CreateConversationSectorDto {
  name: string;
  userIds?: string[];
}

export interface UpdateConversationSectorDto {
  name?: string;
  userIds?: string[];
}
