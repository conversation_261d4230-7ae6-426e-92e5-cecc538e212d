import MessageTemplateCard, {
  MessageTemplateCardSimplified,
} from './MessageTemplateCard';
import Prisma, { MessageTemplateButton } from './Prisma';

type MessageStatus = Prisma.MessageStatus;
export type MediaType = Prisma.MediaType;
export interface Message extends Prisma.Message {
  createdAt: string;
  updatedAt?: string;
  uploadProgress?: number;
}

export interface MessageWithIncludes extends Message {
  conversation: {
    id: string;
    company: {
      isAutomaticSortingActive: boolean;
    };
  };
}

export interface MessageCard {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  text: string;
  cardIndex: number;
  messageId: string;
  messageTemplateCardId: string;
  messageTemplateCard: MessageTemplateCardSimplified;
}

export interface MessageWithCardsIncludes extends Message {
  messageCards?: MessageCard[];
  context?: ListConversationDetailedItem;
}
