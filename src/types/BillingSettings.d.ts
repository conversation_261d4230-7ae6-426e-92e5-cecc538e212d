export interface BillingSettings {
  id: string;
  companyId: string;
  customerServiceFee: number;
  platformFee: number;
  whatsappMarketingPackageLimit?: number;
  whatsappUtilityPackageLimit?: number;
  whatsappServicePackageLimit?: number;
  whatsappMarketingMessageFee?: number;
  whatsappUtilityMessageFee?: number;
  whatsappServiceMessageFee?: number;
  whatsappMarketingExtraMessageFee?: number;
  whatsappUtilityExtraMessageFee?: number;
  whatsappServiceExtraMessageFee?: number;
  billingContactEmail?: string;
  paymentMethod?: string;
  smsMessageFee?: number;
  smsPackageLimit?: number;
  smsExtraMessageFee?: number;
  createdAt: string;
  updatedAt: string;
  company: {
    id: string;
    cnpj?: string;
    razaoSocial?: string;
  };
}
