import { Customer } from './Customer';

export interface CustomerSql
  extends Pick<
    Customer,
    | 'createdAt'
    | 'phoneNumberId'
    | 'name'
    | 'email'
    | 'companyId'
    | 'isOptedOut'
    | 'customFields'
    | 'city'
    | 'state'
  > {
  totalPurchases: null | number;
  averageOrderValue: null | number;
  totalOrders: null | number;
  customerTags: null | string[];
  conversationId: null | string;
  lastPurchaseAt: null | Date;
}
