import { SmsCampaignStatus } from './Prisma';

export interface SmsCampaignStatsData {
  id: string;
  created_at: Date;
  total_recipients: number;
  scheduled_execution_time?: Date;
  status: SmsCampaignStatus;
  template_name: string;
  template_text: string;
  message_template_id: string;
  failed: number;
  sent: number;
  clicks: number;
  total_orders: number;
  total_orders_value: number;
  roi: number;
  campaign_cost: number;
}
