export enum AppModulesEnum {
  HOME = 'HOME',
  CHAT = 'CHAT',
  CUSTOMERS = 'CUSTOMERS',
  CAMPAIGNS = 'CAMPAIGNS',
  TEMPLATES = 'TEMPLATES',
  AUTOMATIONS = 'AUTOMATIONS',
  PRODUCTS = 'PRODUCTS',
  SETTINGS = 'SETTINGS',
  REPORTS = 'REPORTS',
  DEBUG_TOOLS = 'DEBUG_TOOLS',
}

export const appModules: AppModulesEnum[] = Object.values(AppModulesEnum);

export type AppModule = (typeof AppModulesEnum)[keyof typeof AppModulesEnum];
