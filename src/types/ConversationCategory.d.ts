import Prisma from './Prisma';

export type ConversationCategory = Prisma.ConversationCategory;

export interface ConversationCategoryDetailed {
  id: string | null;
  name: string | null;
  totalOpenConversations: number | null;
  totalClosedConversations: number | null;
  conversationSector: {
    id: string | null;
    name: string | null;
  };
}

type CategoryWithSector = ConversationCategory & {
  conversationSector?: ConversationSector;
};
