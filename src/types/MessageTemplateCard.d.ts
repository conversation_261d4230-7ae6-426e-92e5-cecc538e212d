import { MessageTemplateButton } from './MessageTemplateButton';

export const CardHeaderType: {
  IMAGE: 'IMAGE';
  VIDEO: 'VIDEO';
};

export default interface MessageTemplateCard {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  headerType: CardHeaderType;
  mediaUrl?: string;
  body: string;
  cardIndex: number;
  messageTemplateId: string;
  buttons?: MessageTemplateButton[];
}

export interface MessageTemplateCardSimplified extends MessageTemplateCard {
  id: string;
  headerType: CardHeaderType;
  mediaUrl: string;
  buttons: MessageTemplateButton[];
  messageTemplateId: string;
}
