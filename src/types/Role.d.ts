import { AppModule } from '@prisma/client';
import { Role } from './Prisma';

export interface CreateRoleDto {
  name: string;
  appModulePermissions?: AppModule[];
  userIds?: string[];
}

export interface UpdateRoleDto {
  name?: string;
  appModulePermissions?: AppModule[];
  userIds?: string[];
}

export interface RoleWithIncludes extends Role {
  users: {
    id: string;
    name: string;
    email: string;
  }[];
}
