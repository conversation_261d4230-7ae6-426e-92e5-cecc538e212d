import { RFMGroupsEnum } from './RFMGroupsEnum';

type AutomationData = {
  name: string;
};

type CampaignData = {
  filterCriteria: string;
  rfmGroup?: RFMGroupsEnum;
  customerCount?: number;
};

export type RecommendationType = 'create_automation' | 'create_campaign';

export type AutomationRecommendation = {
  title: string;
  description: string;
  isEnabled: boolean;
  action: 'create_automation';
  data: AutomationData;
  info: string;
};

export type CampaignRecommendation = {
  title: string;
  description: string;
  isEnabled: boolean;
  action: 'create_campaign';
  data: CampaignData;
  info: string;
};

export type Recommendation = {
  title: string;
  description: string;
  isEnabled: boolean;
  action: 'create_campaign' | 'create_automation';
  data: any;
  info: string;
};
