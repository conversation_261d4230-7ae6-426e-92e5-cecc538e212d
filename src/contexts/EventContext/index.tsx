import {
  createContext,
  PropsWithChildren,
  ReactNode,
  useCallback,
  useMemo,
} from 'react';
import { EventsEnum } from '../../constants/events';

export interface EventContextData {
  on: (type: EventsEnum, listener: EventListenerOrEventListenerObject) => void;
  off: (type: EventsEnum, listener: EventListenerOrEventListenerObject) => void;
  emit: (type: EventsEnum, detail?: any) => void;
}

export const EventContext = createContext<EventContextData>(
  {} as EventContextData,
);

interface EventProviderProps {
  instance: EventTarget;
  children: ReactNode;
}

const EventProvider = ({
  instance,
  children,
}: PropsWithChildren<EventProviderProps>) => {
  const on = useCallback(
    (type: EventsEnum, listener: EventListenerOrEventListenerObject) => {
      instance.addEventListener(type, listener);
    },
    [instance],
  );

  const off = useCallback(
    (type: EventsEnum, listener: EventListenerOrEventListenerObject) => {
      instance.removeEventListener(type, listener);
    },
    [instance],
  );

  const emit = useCallback(
    (type: EventsEnum, detail?: any) => {
      instance.dispatchEvent(new CustomEvent(type as string, { detail }));
    },
    [instance],
  );

  const value = useMemo(() => ({ on, off, emit }), [on, off, emit]);

  return (
    <EventContext.Provider value={value}>{children}</EventContext.Provider>
  );
};

export default EventProvider;
