import {
  <PERSON>tar,
  Divider,
  Flex,
  IconButton,
  Text,
  Tooltip,
  useToast,
} from '@chakra-ui/react';
import { BiSolidCategory } from 'react-icons/bi';
import { BsPeopleFill, BsPersonCheckFill } from 'react-icons/bs';
import { FaChartBar, FaInbox, FaSearch } from 'react-icons/fa';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { ConversationTabsEnum } from '../..';
import Sider, { SectionTitle } from '../../../../components/Sider';
import { apiRoutes } from '../../../../constants/api-routes';
import { ConversationCategoriesService } from '../../../../services/conversation-categories.service';
import { ConversationsService } from '../../../../services/conversations.service';
import {
  changeActiveTab,
  getTotalOpenConversationByCategoryId,
  selectSidebarState,
  setConversationCategories,
  setConversationIdToCategoryIdMap,
  setConversationSectors,
  setConversationsIdsByAgentId,
  setHasNoAgentConversationIds,
  setOpenConversationIds,
  setUnreadConverstionIds,
} from '../../../../state/inboxSlice';
import { AppDispatch, RootState } from '../../../../state/store';
import {
  ConversationCategory,
  ConversationCategoryDetailed,
} from '../../../../types/ConversationCategory';
import { MdPersonOff } from 'react-icons/md';
import { ConversationSector, User } from '../../../../types/Prisma';
import { ConversationSectorsService } from '../../../../services/conversation-sectors.service';
import { HiUserGroup } from 'react-icons/hi';
import { AddIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../constants/app-paths';
import { useAppModuleAccessGuard } from '../../../../hooks/useAppModuleAccessGuard';
import PopoverNewConversation from './PopoverNewConversation';

interface SideNavigationProps {
  width?: string;
  companyAgents: User[];
  showToggleButton?: boolean;
  isMobile?: boolean;
  onClose?: () => void;
}

const SideNavigation = ({
  width,
  companyAgents,
  showToggleButton,
  isMobile,
  onClose,
}: SideNavigationProps) => {
  const {
    activeTab,
    conversationCategories,
    conversationSectors,
    openConversationIds,
    unreadConversationIds,
    daysSinceLastMessage,
    conversationCategoryId,
    hasNoAgentConversationIds,
    conversationsIdsByAgentId,
    ticketStatus,
  } = useSelector((state: RootState) => state.inbox);
  const isCollapsed = useSelector(selectSidebarState);
  const currentUser = useSelector((state: RootState) => state.auth.currentUser);
  const dispatch = useDispatch<AppDispatch>();
  const toast = useToast();
  const navigate = useNavigate();

  const { checkUserHasPathAccess } = useAppModuleAccessGuard();

  const queryClient = useQueryClient();
  const deleteConversationCategory = useMutation(
    async (categoryId: string) => {
      const { data } =
        await ConversationCategoriesService.deleteConversationCategory(
          categoryId,
        );
      return data;
    },
    {
      onSuccess: (data) => {
        if (conversationCategoryId === data.id) {
          dispatch(
            changeActiveTab({
              tab: 'all-conversations',
              conversationCategoryId: null,
            }),
          );
        }
        queryClient.setQueryData(
          apiRoutes.listConversationCategoriesDetailed(daysSinceLastMessage),
          (oldData?: ConversationCategoryDetailed[]) => {
            return (
              oldData?.filter((category: any) => category.id !== data.id) || []
            );
          },
        );
        queryClient.setQueryData(
          apiRoutes.listConversationCategories(),
          (oldData?: ConversationCategory[]) => {
            return (
              oldData?.filter((category: any) => category.id !== data.id) || []
            );
          },
        );
        toast({
          title: 'Categoria deletada',
          status: 'info',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );
  const deleteConversationSector = useMutation(
    async (sectorId: string) => {
      const { data } =
        await ConversationSectorsService.deleteConversationSector(sectorId);
      return data;
    },
    {
      onSuccess: (data) => {
        queryClient.setQueryData(
          apiRoutes.listMyConversationSectors(),
          (oldData?: ConversationSector[]) => {
            return (
              oldData?.filter((sector: any) => sector.id !== data.id) || []
            );
          },
        );

        toast({
          title: 'Setor deletado',
          status: 'info',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );
  const moveCategory = useMutation(
    async ({
      categoryId,
      sectorId,
    }: {
      categoryId: string;
      sectorId?: string;
    }) => {
      const { data } =
        await ConversationCategoriesService.updateConversationCategory(
          { conversationSectorId: sectorId },
          categoryId,
        );
      return data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(
          apiRoutes.listConversationCategoriesDetailed(daysSinceLastMessage),
        );

        toast({
          title: 'Categoria movida com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  useQuery(
    apiRoutes.listConversationCategoriesDetailed(daysSinceLastMessage),
    async () => {
      const { data } =
        await ConversationCategoriesService.listConversationCategoriesDetailed(
          daysSinceLastMessage,
        );
      return data;
    },
    {
      onSuccess: (data) => {
        dispatch(setConversationCategories(data));
      },
      refetchInterval: 1000 * 60 * 1, // redundant with the socket to guarantee the data is always up to date
    },
  );

  useQuery(
    apiRoutes.listMyConversationSectors(),
    async () => {
      const { data } =
        await ConversationSectorsService.listMyConversationSectors();
      return data;
    },
    {
      onSuccess: (data) => {
        dispatch(setConversationSectors(data));
      },
      refetchInterval: 1000 * 60 * 1, // redundant with the socket to guarantee the data is always up to date
    },
  );

  useQuery(
    apiRoutes.getConversationsCount({
      ticketStatus,
      daysSinceLastMessage,
    }),
    async () => {
      const { data } = await ConversationsService.getConversationsCount({
        ticketStatus,
        daysSinceLastMessage,
      });
      return data;
    },
    {
      onSuccess: (data) => {
        dispatch(setOpenConversationIds(data.map((c) => c.id)));

        const noAgentConversationIds = data
          .filter(({ agentId, id }) => !agentId && id)
          .map(({ id }) => id);
        dispatch(setHasNoAgentConversationIds(noAgentConversationIds));

        const conversationsByAgentId = data.filter(
          ({ agentId }) => agentId !== null,
        ) as { id: string; agentId: string }[];
        dispatch(setConversationsIdsByAgentId(conversationsByAgentId));
      },
      refetchInterval: 1000 * 60 * 1, // redundant with the socket to guarantee the data is always up to date
    },
  );

  useQuery(
    apiRoutes.getConversationIdsWithUnreadMessages({
      daysSinceLastMessage,
      ticketStatus,
    }),
    async () => {
      const { data } =
        await ConversationsService.getConversationIdsWithUnreadMessages({
          daysSinceLastMessage,
          ticketStatus,
        });
      return data;
    },
    {
      onSuccess: (data) => {
        dispatch(setUnreadConverstionIds(data.map((c) => c.id)));
      },
      refetchInterval: 1000 * 60 * 1, // redundant with the socket to guarantee the data is always up to date
    },
  );

  useQuery(
    apiRoutes.listConversationSummary({
      daysSinceLastMessage,
    }),
    async () => {
      const { data } = await ConversationsService.listConversationSummary({
        daysSinceLastMessage,
      });
      return data;
    },
    {
      onSuccess: (data) => {
        dispatch(setConversationIdToCategoryIdMap(data));
      },
      refetchInterval: 1000 * 60 * 1, // redundant with the socket to guarantee the data is always up to date
    },
  );

  const organizedSectors = conversationSectors.reduce(
    (acc, sector) => {
      acc[sector.id] = {
        sectorId: sector.id,
        name: sector.name,
        categories: [],
      };
      return acc;
    },
    {} as Record<string, { sectorId: string; name: string; categories: any[] }>,
  );

  organizedSectors['general'] = {
    sectorId: 'general',
    name: 'Geral',
    categories: [],
  };

  conversationCategories.forEach((category) => {
    const sectorId = category.conversationSector?.id;
    const categoryItem = {
      rightElement: (
        <Text>
          {(ticketStatus === 'open'
            ? category.totalOpenConversations
            : category.totalClosedConversations) || ''}
        </Text>
      ),
      title: category.name!,
      onClick: () =>
        dispatch(
          changeActiveTab({
            tab: 'conversation-category',
            conversationCategoryId: category.id,
          }),
        ),
    };

    if (sectorId && organizedSectors[sectorId]) {
      organizedSectors[sectorId].categories.push(categoryItem);
    } else {
      organizedSectors['general'].categories.push(categoryItem);
    }
  });

  Object.values(organizedSectors).forEach((sector) => {
    sector.categories.sort((a, b) => a.title.localeCompare(b.title));
  });

  const sortedSectors = Object.values(organizedSectors).sort((a, b) => {
    if (a.name === 'Geral') return -1;
    if (b.name === 'Geral') return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div>
      <Sider
        topElement={
          <>
            <Flex
              justifyContent={'space-between'}
              alignItems="center"
              height="60px"
              direction={{ base: 'column', md: 'row' }}
              gap={2}
            >
              <Text
                fontSize={{ base: 'xl', md: '2xl' }}
                fontWeight="bold"
                display={{
                  base: 'block',
                  md: isCollapsed ? 'none' : 'block',
                }}
                textAlign="left"
                width="100%"
                mt={isMobile ? '25px' : '0'}
              >
                Inbox
              </Text>
            </Flex>
            <Divider />
          </>
        }
        showToggleButton={showToggleButton}
        width={width}
        activeTab={activeTab}
        headerActions={
          <Flex justifyContent="space-between" alignItems="center">
            <SectionTitle isCollapsed={isCollapsed}>Principal</SectionTitle>
            <Flex gap={1} visibility={isCollapsed ? 'hidden' : 'visible'}>
              {!isMobile &&
                checkUserHasPathAccess(appPaths.reports.index()) && (
                  <IconButton
                    aria-label="Análises de Atendimentos"
                    onClick={() => navigate(appPaths.reports.index())}
                    icon={<FaChartBar />}
                    size="sm"
                    variant="ghost"
                  />
                )}
              <PopoverNewConversation
                isMobile={isMobile}
                onSidebarClose={isMobile ? onClose : () => {}}
              />
              {!isMobile &&
                checkUserHasPathAccess(appPaths.customers.index()) && (
                  <IconButton
                    aria-label="Pesquisar"
                    onClick={() => navigate(appPaths.customers.index())}
                    icon={<FaSearch />}
                    size="sm"
                    variant="ghost"
                  />
                )}
            </Flex>
          </Flex>
        }
        items={[
          {
            rightElement: (
              <Text>{Object.keys(unreadConversationIds).length || ''}</Text>
            ),
            title: ConversationTabsEnum.NOT_READ,
            icon: <FaInbox />,
            onClick: () => {
              dispatch(
                changeActiveTab({
                  tab: 'not-read',
                  conversationCategoryId: null,
                }),
              );
            },
            children: [],
          },
          {
            rightElement: (
              <Text>{Object.keys(openConversationIds).length || ''}</Text>
            ),
            title: ConversationTabsEnum.ALL_CONVERSATIONS,
            icon: <BsPeopleFill />,
            onClick: () =>
              dispatch(
                changeActiveTab({
                  tab: 'all-conversations',
                  conversationCategoryId: null,
                }),
              ),
            children: [],
          },
          {
            rightElement: (
              <Text>
                {(conversationsIdsByAgentId[currentUser?.sub!] || []).length ||
                  ''}
              </Text>
            ),
            title: ConversationTabsEnum.MY_CONVERSATIONS,
            icon: <BsPersonCheckFill />,
            onClick: () =>
              dispatch(
                changeActiveTab({
                  tab: 'agent-conversations',
                  conversationCategoryId: null,
                  agentId: currentUser?.sub,
                }),
              ),
            children: [],
          },
          {
            rightElement: (
              <Text>{Object.keys(hasNoAgentConversationIds).length || ''}</Text>
            ),
            title: ConversationTabsEnum.NO_ASSIGNMENT,
            icon: <MdPersonOff />,
            onClick: () =>
              dispatch(
                changeActiveTab({
                  tab: 'no-assignment',
                  conversationCategoryId: null,
                }),
              ),
            children: [],
          },
          {
            title: 'Setores',
            icon: <BiSolidCategory />,
            onClick: () => {},
            children: [
              ...sortedSectors.map((sectorData) => ({
                rightElement: <></>,
                contextMenuItems:
                  sectorData.sectorId !== 'general'
                    ? [
                        {
                          title: 'Apagar Setor',
                          onClick: () =>
                            deleteConversationSector.mutate(
                              sectorData.sectorId,
                            ),
                        },
                      ]
                    : undefined,
                title: sectorData.name,
                children: [...sectorData.categories],
                onClick: () => {},
              })),
              {
                title: '',
                leftElement: isMobile ? null : (
                  <Tooltip label="Você será redirecionado para a página de gerenciamento de setores de conversa">
                    <Flex
                      alignItems="center"
                      justifyContent="center"
                      cursor="pointer"
                      width="100%"
                      role="button"
                      flex="1"
                      mx={4}
                      onClick={() =>
                        navigate(
                          appPaths.settings.conversationSectorsManagement.index(),
                        )
                      }
                    >
                      <AddIcon fontSize="12px" mr={4} />
                      <Text fontSize="14px" fontWeight="semibold" w="100%">
                        Novo Setor
                      </Text>
                    </Flex>
                  </Tooltip>
                ),
                onClick: () => {},
                isHidden: !checkUserHasPathAccess(
                  appPaths.settings.conversationSectorsManagement.index(),
                ),
              },
            ],
            rightElement: <></>,
          },
          {
            rightElement: (
              <Text>{Object.keys(companyAgents).length || ''}</Text>
            ),
            title: 'Colaboradores',
            isHidden: !currentUser?.sub || !currentUser.canViewAllConversations,
            icon: <HiUserGroup />,
            onClick: () => {},
            children: companyAgents
              .filter((agent) => !!agent.id)
              .map((agent) => ({
                rightElement: (
                  <Text>
                    {(conversationsIdsByAgentId[agent.id!] || []).length || ''}
                  </Text>
                ),
                title: agent.name!,
                icon: <Avatar name={agent.name} size={'xs'} />,
                onClick: () =>
                  dispatch(
                    changeActiveTab({
                      tab: 'agent-conversations',
                      conversationCategoryId: null,
                      agentId: agent.id,
                      agentName: agent.name,
                    }),
                  ),
              })),
          },
        ]}
      />
    </div>
  );
};

export default SideNavigation;
