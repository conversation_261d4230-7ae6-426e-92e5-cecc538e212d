import React, { useState, CSSProperties } from 'react';
import { Box, Flex, IconButton, Text } from '@chakra-ui/react';
import { FaTimesCircle } from 'react-icons/fa';
import Rive from '@rive-app/react-canvas';
import { colors } from '../../../../constants/colors';
import { useQuery } from 'react-query';
import './styles/ai-suggestion.css';

interface AIReplySuggestionsProps {
  isLoading: boolean;
  aiReplySuggestions: string[];
  onSelect: (val: string) => void;
  onClose: () => void;
}

const AIReplySuggestions = ({
  isLoading,
  aiReplySuggestions,
  onSelect,
  onClose,
}: AIReplySuggestionsProps) => {
  const riveAnimationFile = '/animations/rive/ai-generate-loading.riv';
  const [showLoading, setShowLoading] = useState(true);
  const [currentArtBoard, setCurrentArtBoard] = useState('Magic');
  const [animateItems, setAnimateItems] = useState(false);

  useQuery([isLoading, aiReplySuggestions], () => {
    if (aiReplySuggestions.length > 0 && !isLoading) {
      setCurrentArtBoard('Tick blue');
      setTimeout(() => {
        setShowLoading(false);
      }, 1450);
      setTimeout(() => {
        setAnimateItems(true);
      }, 1500);
    } else {
      setAnimateItems(false);
    }
  });

  const handleClose = () => {
    onClose();
  };

  return (
    <Flex
      flexDir="column"
      gap={2}
      position="absolute"
      bottom="100%"
      padding="10px"
      width="100%"
      overflowY="auto"
      overflowX="hidden"
      maxHeight="60vh"
      boxShadow="inner"
      bg="white"
    >
      <Flex justify="flex-end">
        <IconButton
          backgroundColor="transparent"
          aria-label="Fechar"
          onClick={handleClose}
          icon={<FaTimesCircle size={21} color={colors.danger} />}
        />
      </Flex>
      {showLoading ? (
        <Flex height="100px" align="center" justify="center">
          <Rive
            key={currentArtBoard}
            src={riveAnimationFile}
            artboard={currentArtBoard}
            stateMachines="State appear"
          />
        </Flex>
      ) : (
        aiReplySuggestions.map((reply, index) => {
          const delay = index * 0.1;

          const itemStyle: CSSProperties = {
            opacity: animateItems ? 1 : 0,
            transform: animateItems ? 'translateX(0)' : 'translateX(100%)',

            transition: 'transform 0.5s ease-out, opacity 0.5s ease-out',
            transitionDelay: `${delay}s`,

            backgroundColor: 'white',
          };

          return (
            <div style={itemStyle}>
              <div
                key={index}
                className="gradient-border"
                id="suggestion-card"
                onClick={() => {
                  onSelect(reply);
                }}
                style={{
                  cursor: 'pointer',
                }}
              >
                <Text noOfLines={1} title={reply}>
                  {reply}
                </Text>
              </div>
            </div>
          );
        })
      )}
    </Flex>
  );
};

export default AIReplySuggestions;
