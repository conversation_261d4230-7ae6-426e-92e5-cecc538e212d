#suggestion-card {
  padding: 4px;
  font-size: 12px;
  margin-bottom: 2px;
  font-weight: bold;
  display: flex;
  width: 100%;
  height: 40px;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
#suggestion-card:hover {
  background-color: rgba(245, 245, 245, 0.98);
}

.gradient-border {}

.gradient-border:hover {
  --borderWidth: 2px;
  background: white;
  position: relative;
  border-radius: var(--borderWidth);
}
.gradient-border:after {
  content: '';
  position: absolute;
  top: calc(-1 * var(--borderWidth));
  left: calc(-1 * var(--borderWidth));
  height: calc(100% + var(--borderWidth) * 2);
  width: calc(100% + var(--borderWidth) * 2);
  background: linear-gradient(
    60deg,
    #f79533,
    #f37055,
    #4169e1,
    #7851a9,
    #5073b8,
    red,
    #4169e1,
    red
  );
  border-radius: calc(2 * var(--borderWidth));
  z-index: -1;
  animation: animatedgradient 3s ease alternate infinite;
  background-size: 300% 300%;
}

@keyframes animatedgradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
