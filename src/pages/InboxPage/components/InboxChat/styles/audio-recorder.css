/* Original CSS */
.audio-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-container audio {
  height: 20px;
}

.audio-container button {
  color: #6E6E6E;
  border-radius: 50%;
  height: 22px;
  width: 22px;
  /* background-color: #e8e6e6; */
  border: none;
  cursor: pointer;
}

.button-stop-container {
  position: relative;
  display: inline-block;
  height: 20px;
}

.button-stop-container .button-stop-recording {
  position: relative;
  z-index: 1;
  font-size: 20px;
  border: none;
  cursor: pointer;
  color: white;
  border-radius: 5px;
}

.button-stop-container .circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background-color: red;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: expand 2s infinite;
  z-index: 0;
}

@keyframes expand {
  0% {
      width: 3px;
      height: 3px;
      opacity: 1;
  }
  100% {
      width: 40px;
      height: 40px;
      opacity: 0;
  }
}

.audio-modal {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audio-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.audio-modal-title {
  font-weight: 500;
  font-size: 16px;
}

.audio-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #6E6E6E;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-modal-player {
  width: 100%;
  height: 40px;
}

.audio-modal-actions {
  display: flex;
  justify-content: space-between;
}

.audio-modal-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  flex: 1;
}

.audio-modal-button span {
  font-size: 12px;
  margin-top: 4px;
  color: #6E6E6E;
}

.audio-menu-button {
  color: #6E6E6E;
  border-radius: 50%;
  height: 22px;
  width: 22px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-menu-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6E6E6E;
}