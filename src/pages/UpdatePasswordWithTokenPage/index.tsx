import {
  Box,
  Button,
  Checkbox,
  Container,
  Flex,
  Image,
  Input,
  InputGroup,
  InputRightElement,
  Link,
  Stack,
  Text,
  useBoolean,
  useToast,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { RootState } from '../../state/store';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { CheckIcon, CloseIcon } from '@chakra-ui/icons';
import _ from 'lodash';
import { useMutation } from 'react-query';
import { AuthService, UpdatePasswordDto } from '../../services/auth.service';

const passwordRequirements = [
  { label: 'Mínimo 10 caracteres', regex: /.{10,}/ },
  { label: 'Ao menos uma letra maiúscula', regex: /[A-Z]/ },
  { label: 'Ao menos uma letra minúscula', regex: /[a-z]/ },
  { label: 'Ao menos um número', regex: /[0-9]/ },
  { label: 'Ao menos um caractere especial', regex: /[@$!%*?&#]/ },
];

const schema = yup.object().shape({
  password: yup
    .string()
    .required('Senha é obrigatória')
    .test(
      'passwordRequirements',
      'A senha não atende aos requisitos',
      (value) =>
        passwordRequirements.every((req) => req.regex.test(value || '')),
    ),
  confirmationPassword: yup
    .string()
    .required('Confirmação de senha é obrigatória')
    .oneOf([yup.ref('password'), null], 'As senhas devem coincidir'),
});

const UpdatePasswordWithTokenPage = () => {
  const [password, setPassword] = useState('');
  const [confirmationPassword, setConfirmationPassword] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    mode: 'onChange',
  });
  const { currentUser } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const updatePasswordToken = searchParams.get('token');
  const toast = useToast();
  const [showPassword, setShowPassword] = useBoolean(false);
  const [showConfirmationPassword, setShowConfirmationPassword] =
    useBoolean(false);

  useEffect(() => {
    if (currentUser || !updatePasswordToken) {
      navigate('/');
    }
  }, [currentUser, navigate]);

  const updatePasswordWithToken = useMutation(
    AuthService.updatePasswordWithToken,
    {
      onSuccess: () => {
        toast({
          title: 'Senha alterada com sucesso.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        navigate('/login');
      },
    },
  );

  const onSubmit = (data: any) => {
    if (!updatePasswordToken) {
      toast({
        title: 'Token de reset inválido',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    if (!_.isEmpty(errors)) return;

    updatePasswordWithToken.mutate({
      updatePasswordToken,
      newPassword: data.password,
    });
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  const handleChangeConfirmationPassword = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setConfirmationPassword(e.target.value);
  };

  const isRequirementMet = (regex: RegExp) => regex.test(password);
  const arePasswordsMatching =
    password && confirmationPassword && password === confirmationPassword;

  if (!updatePasswordToken) {
    return null;
  }

  return (
    <Box position="relative" height="100vh">
      <Container pt={10}>
        <Flex width="100%" justify={'center'} align="center">
          <Image
            src="/revi-logo-text.png"
            width="300px"
            height="200px"
            objectFit={'cover'}
          />
        </Flex>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <Stack spacing={2}>
              <Text fontSize="md" fontWeight="medium" textAlign="center">
                Redefina sua senha
              </Text>
              <Text textAlign="left" fontSize="sm">
                Por favor, insira uma nova senha que atenda aos requisitos
                abaixo e confirme-a para redefinir sua senha.
              </Text>
            </Stack>
            <InputGroup size="md">
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder="Senha"
                {...register('password')}
                onChange={handlePasswordChange}
              />
              <InputRightElement width="fit-content" mr={1.5}>
                <Button
                  h="1.75rem"
                  px={1}
                  size="sm"
                  onClick={setShowPassword.toggle}
                >
                  {showPassword ? 'Esconder' : 'Mostrar'}
                </Button>
              </InputRightElement>
            </InputGroup>
            <InputGroup size="md">
              <Input
                type={showConfirmationPassword ? 'text' : 'password'}
                placeholder="Confirme sua senha"
                {...register('confirmationPassword')}
                onChange={handleChangeConfirmationPassword}
              />
              <InputRightElement width="fit-content" mr={1.5}>
                <Button
                  h="1.75rem"
                  px={1}
                  size="sm"
                  onClick={setShowConfirmationPassword.toggle}
                >
                  {showConfirmationPassword ? 'Esconder' : 'Mostrar'}
                </Button>
              </InputRightElement>
            </InputGroup>
            <Box>
              {passwordRequirements.map((req, index) => (
                <Flex key={index} align="center">
                  {isRequirementMet(req.regex) ? (
                    <CheckIcon color="green.500" />
                  ) : (
                    <CloseIcon color="red.500" />
                  )}
                  <Text fontSize="sm" ml={2}>
                    {req.label}
                  </Text>
                </Flex>
              ))}
              <Flex align="center">
                {arePasswordsMatching ? (
                  <CheckIcon color="green.500" />
                ) : (
                  <CloseIcon color="red.500" />
                )}
                <Text fontSize="sm" ml={2}>
                  Senhas devem coincidir
                </Text>
              </Flex>
            </Box>
            <Button type="submit" isLoading={updatePasswordWithToken.isLoading}>
              Mudar senha
            </Button>
          </Stack>
        </form>
      </Container>
      <Box
        position={'absolute'}
        bottom={'10px'}
        left="0"
        right="0"
        display="flex"
        justifyContent={'center'}
        alignItems="center"
        flexDir={'column'}
      >
        <Text>©2024 Revi. Todos os direitos reservados</Text>
        <Link
          href="https://revi-public.s3.amazonaws.com/Politica_de_Privacidade_Revi.pdf"
          target={'_blank'}
        >
          Política de privacidade
        </Link>
      </Box>
    </Box>
  );
};

export default UpdatePasswordWithTokenPage;
