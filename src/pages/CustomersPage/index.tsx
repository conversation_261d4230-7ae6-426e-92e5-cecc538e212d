import {
  Box,
  Button,
  ButtonGroup,
  Divider,
  Flex,
  Grid,
  GridItem,
  Heading,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  Tooltip,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { SetStateAction, useEffect, useRef, useState } from 'react';
import { BsCheckAll, BsSearch } from 'react-icons/bs';
import { MdOutlineCampaign } from 'react-icons/md';
import { TbFileImport, TbTable } from 'react-icons/tb';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { appPaths } from '../../constants/app-paths';
import {
  finishCampaignCreation,
  resetSelectedCustomers,
  setFilterCriteria,
} from '../../state/campaignCreationSlice';
import { RootState } from '../../state/store';
import FilterSidebar from './components/FilterSidebar';
import TableCustomersPaginated from './components/TableCustomersPaginated';
import { UploadFileModal } from './components/UploadFileModal';
import { AddTagModal } from './components/AddTagModal';
import { FaChartBar, FaTrashAlt } from 'react-icons/fa';
import { DeleteCustomerModal } from './components/DeleteCustomerModal';
import { LuHeadphones, LuTags } from 'react-icons/lu';
import GraphCustomers from './components/GraphCustomers';
import { useAppModuleAccessGuard } from '../../hooks/useAppModuleAccessGuard';
import { AssignDefaultAgentModal } from './components/AssignDefaultAgentModal';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { scrollbarStyles } from '../../styles/scrollbar.styles';
import { motion, AnimatePresence } from 'framer-motion';
import { colors } from '../../constants/colors';

const CustomersPage = () => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const { showSelectCustomerRows, selectedCustomerRows } = useSelector(
    (state: RootState) => state.campaignCreation,
  );
  const dispatch = useDispatch();
  const uploadFileModal = useDisclosure();
  const addTagModal = useDisclosure();
  const assignDefaultAgentModal = useDisclosure();
  const deleteCustomerModal = useDisclosure();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const shouldFinishCampaignCreationOnUnmountRef = useRef(true);
  const toast = useToast();
  const [searchValue, setSearchValue] = useState(
    searchParams.get('searchQuery') || '',
  );

  useEffect(() => {
    if (searchParams.toString()) {
      const paramsCount = searchParams.toString().split('&').length;
      toast({
        title: 'Filtros aplicados',
        description: `${paramsCount} filtros aplicados`,
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    }
  }, []);

  useEffect(() => {
    setSearchValue(searchParams.get('searchQuery') || '');
  }, [searchParams]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleSearchSubmit = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const newParams = new URLSearchParams(searchParams);
      if (searchValue) {
        newParams.set('searchQuery', searchValue);
      } else {
        newParams.delete('searchQuery');
      }
      setSearchParams(newParams);
    }
  };

  function handleClickCancelCampaign() {
    setSearchParams('');
    dispatch(finishCampaignCreation());
    navigate(appPaths.customers.index());
  }

  function handleClickNewCampaign() {
    const newPath = `${appPaths.campaigns.create()}?${searchParams.toString()}`;
    navigate(newPath);
  }

  function handleClickConfirmCustomersSelection() {
    shouldFinishCampaignCreationOnUnmountRef.current = false;
    dispatch(setFilterCriteria(window.location.search.substring(1)));
    navigate(appPaths.campaigns.create());
  }

  useEffect(() => {
    shouldFinishCampaignCreationOnUnmountRef.current = true;
  }, []);

  useEffect(() => {
    return () => {
      if (shouldFinishCampaignCreationOnUnmountRef.current) {
        dispatch(finishCampaignCreation());
      }
    };
  }, [shouldFinishCampaignCreationOnUnmountRef, dispatch]);

  const hasSelectedRows = Object.values(selectedCustomerRows).some(Boolean);
  const selectedCount =
    Object.values(selectedCustomerRows).filter(Boolean).length;

  const [selectedViewFormat, setSelectedViewFormat] = useState<
    'table' | 'graph'
  >('table');

  const handleSelectView = (view: SetStateAction<'table' | 'graph'>) => {
    setSelectedViewFormat(view);
  };

  const handleAssignDefaultAgentModalClose = () => {
    assignDefaultAgentModal.onClose();
  };

  const [isFilterOpen, setIsFilterOpen] = useState(true);

  const bulkActionsVariants = {
    hidden: {
      opacity: 0,
      height: 0,
      marginBottom: 0,
    },
    visible: {
      opacity: 1,
      height: 'auto',
      marginBottom: 4,
      transition: {
        duration: 0.3,
        ease: 'easeInOut',
      },
    },
    exit: {
      opacity: 0,
      height: 0,
      marginBottom: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut',
      },
    },
  };

  const contentVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        delay: 0.1,
        duration: 0.2,
        ease: 'easeOut',
      },
    },
  };

  return (
    <Grid
      templateColumns={isFilterOpen ? '290px 1fr' : 'auto 1fr'}
      templateRows="auto auto 1fr"
      height="100vh"
      width="100%"
    >
      <GridItem
        rowSpan={3}
        colSpan={1}
        boxShadow="lg"
        position="relative"
        width={isFilterOpen ? '290px' : '60px'}
        overflow="hidden"
        bg="white"
        transition="all 0.3s ease"
      >
        <Flex
          align="center"
          justify="space-between"
          p={isFilterOpen ? '5' : '3'}
          pt={8}
          margin="auto"
          borderBottom="1px solid"
          borderColor="gray.200"
          transition="all 0.3s ease"
        >
          {isFilterOpen && (
            <Heading size="md" whiteSpace="nowrap">
              Clientes
            </Heading>
          )}

          <Tooltip
            label={isFilterOpen ? 'Ocultar' : 'Expandir'}
            aria-label="Botão para expandir ou ocultar sidebar"
          >
            <IconButton
              aria-label={isFilterOpen ? 'Fechar filtros' : 'Abrir filtros'}
              icon={isFilterOpen ? <FiChevronLeft /> : <FiChevronRight />}
              size="24"
              width="36px"
              height="36px"
              borderRadius="full"
              boxShadow="lg"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              variant="ghost"
              color="blue.600"
              _hover={{ bg: 'transparent' }}
              _active={{ bg: 'transparent' }}
            />
          </Tooltip>
        </Flex>

        <Box transition="all 0.3s ease" px="2">
          <FilterSidebar isOpen={isFilterOpen} />
        </Box>
      </GridItem>

      <GridItem colSpan={1}>
        <Flex padding={4} justifyContent="space-between" alignItems="center">
          <Flex alignItems="center">
            <InputGroup minWidth="450px" mr={4}>
              <InputLeftElement pointerEvents="none">
                <BsSearch color="gray.300" />
              </InputLeftElement>
              <Input
                placeholder="Nome, telefone e etc..."
                value={searchValue}
                onChange={handleSearchChange}
                onKeyPress={handleSearchSubmit}
              />
            </InputGroup>

            <Box display="flex" alignItems="center">
              <ButtonGroup isAttached size="md" variant="outline">
                <Tooltip label="Tabela">
                  <IconButton
                    icon={<TbTable size="20px" />}
                    aria-label="Tabela"
                    variant={
                      selectedViewFormat === 'table' ? 'primary' : 'outline'
                    }
                    onClick={() => handleSelectView('table')}
                  />
                </Tooltip>
                <Tooltip label="Gráficos">
                  <IconButton
                    icon={<FaChartBar fontSize="18px" />}
                    aria-label="Gráficos"
                    variant={
                      selectedViewFormat === 'graph' ? 'primary' : 'outline'
                    }
                    onClick={() => handleSelectView('graph')}
                  />
                </Tooltip>
              </ButtonGroup>
            </Box>
          </Flex>

          <Flex gap={2}>
            {showSelectCustomerRows ? (
              <Flex gap={2}>
                <Button variant="outline" onClick={handleClickCancelCampaign}>
                  Cancelar
                </Button>
                <Button
                  leftIcon={<BsCheckAll fontSize="24px" />}
                  variant="primary"
                  onClick={handleClickConfirmCustomersSelection}
                  isDisabled={
                    Object.values(selectedCustomerRows).filter(Boolean)
                      .length === 0
                  }
                >
                  Confirmar
                </Button>
              </Flex>
            ) : (
              <Flex gap={2}>
                <Button
                  leftIcon={<TbFileImport size="24px" />}
                  variant="outline"
                  onClick={uploadFileModal.onOpen}
                >
                  Importar arquivo
                </Button>
                {checkUserHasPathAccess(appPaths.campaigns.create()) && (
                  <Button
                    variant="primary"
                    leftIcon={<MdOutlineCampaign size="24px" />}
                    onClick={handleClickNewCampaign}
                  >
                    Nova Campanha
                  </Button>
                )}
              </Flex>
            )}
          </Flex>
        </Flex>
      </GridItem>

      <GridItem colSpan={1}>
        <AnimatePresence>
          {hasSelectedRows && (
            <motion.div
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={bulkActionsVariants}
            >
              <motion.div variants={contentVariants}>
                <Flex padding="0 16px 12px">
                  <Box
                    bg="white"
                    borderRadius="md"
                    py={2}
                    px={4}
                    boxShadow="sm"
                    border="1px solid"
                    borderColor={colors.lightGrey}
                    display="flex"
                    alignItems="center"
                    width="100%"
                  >
                    <Flex alignItems="center">
                      <Box
                        fontWeight="medium"
                        color="gray.700"
                        minWidth="120px"
                      >
                        {selectedCount} selecionados
                      </Box>

                      <Divider
                        orientation="vertical"
                        height="24px"
                        mx={3}
                        borderColor="gray.300"
                      />

                      <Flex alignItems="center">
                        <Tooltip label="Selecionar Atendente Padrão">
                          <IconButton
                            icon={<LuHeadphones size="20px" />}
                            aria-label="Selecionar Atendente Padrão"
                            size="md"
                            variant="outline"
                            onClick={assignDefaultAgentModal.onOpen}
                            mr={2}
                          />
                        </Tooltip>
                        <Tooltip label="Adicionar Tag">
                          <IconButton
                            icon={<LuTags size="20px" />}
                            aria-label="Adicionar Tag"
                            size="md"
                            variant="outline"
                            onClick={addTagModal.onOpen}
                            mr={2}
                          />
                        </Tooltip>
                        <Tooltip label="Deletar Cliente">
                          <IconButton
                            icon={<FaTrashAlt fontSize="18px" />}
                            aria-label="Deletar Cliente"
                            size="md"
                            variant="outline"
                            onClick={deleteCustomerModal.onOpen}
                          />
                        </Tooltip>
                      </Flex>

                      <Divider
                        orientation="vertical"
                        height="24px"
                        mx={3}
                        borderColor="gray.300"
                      />

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => dispatch(resetSelectedCustomers())}
                      >
                        Limpar
                      </Button>
                    </Flex>
                  </Box>
                </Flex>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </GridItem>

      <GridItem
        colSpan={1}
        css={scrollbarStyles({ width: '4px' })}
        mr={1}
        overflow="auto"
      >
        {selectedViewFormat === 'graph' ? (
          <GraphCustomers />
        ) : (
          <TableCustomersPaginated />
        )}
      </GridItem>

      <UploadFileModal
        isOpen={uploadFileModal.isOpen}
        onClose={uploadFileModal.onClose}
      />
      <AssignDefaultAgentModal
        isOpen={assignDefaultAgentModal.isOpen}
        onClose={handleAssignDefaultAgentModalClose}
        selectedUsers={selectedCustomerRows}
      />
      <AddTagModal
        isOpen={addTagModal.isOpen}
        onClose={addTagModal.onClose}
        selectedUsers={selectedCustomerRows}
      />
      <DeleteCustomerModal
        isOpen={deleteCustomerModal.isOpen}
        onClose={deleteCustomerModal.onClose}
        selectedUsers={selectedCustomerRows}
        redirectTo={appPaths.customers.index()}
      />
    </Grid>
  );
};

export default CustomersPage;
