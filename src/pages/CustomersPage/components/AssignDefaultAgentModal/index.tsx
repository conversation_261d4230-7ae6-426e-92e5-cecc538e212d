import {
  Button,
  Flex,
  Modal,
  Modal<PERSON>ody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Select,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { CustomersService } from '../../../../services/customers.service';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../constants/app-paths';
import { UsersService } from '../../../../services/users.service';
import { User } from '../../../../types/Prisma';

interface AssignDefaultAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedUsers: Record<string, boolean>;
}

export const AssignDefaultAgentModal = ({
  isOpen,
  onClose,
  selectedUsers,
}: AssignDefaultAgentModalProps) => {
  const toast = useToast();
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  const navigate = useNavigate();

  const { data: companyAgents = [] } = useQuery(
    apiRoutes.listCompanyAgents(),
    async () => {
      const { data } = await UsersService.listCompanyAgents();
      return data.filter((agent) => agent.isActive);
    },
  );

  const updateCustomersAgentMutation = useMutation(
    CustomersService.bulkAssignDefaultAgentToCustomers,
    {
      onSuccess: () => {
        toast({
          title: 'Atendente padrão definido com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        onClose();
        navigate({
          pathname: appPaths.customers.index(),
          search: `?selectedDefaultAgentIds=${selectedAgentId}`,
        });
      },
    },
  );

  const handleAgentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedAgentId(e.target.value);
  };

  const handleClickSubmit = () => {
    if (!selectedAgentId) return;
    updateCustomersAgentMutation.mutate({
      ids: Object.keys(selectedUsers),
      defaultAgentId: selectedAgentId,
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Selecionar Atendente Padrão</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text size="xs" mb={4}>
            O atendente padrão será automáticamente o responsável pelos contatos
            selecionados.
          </Text>
          <Select
            placeholder="Selecione um atendente padrão"
            onChange={handleAgentChange}
            value={selectedAgentId}
          >
            {companyAgents.map((agent: User) => (
              <option key={`agent-to-assign-${agent.id}`} value={agent.id}>
                {agent.name}
              </option>
            ))}
          </Select>
          <Text fontSize={12} mt={1} color="gray.600">
            O atendente da conversa ainda poderá ser alterado a qualquer
            momento.
          </Text>
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button variant="outline" onClick={onClose} disabled={false}>
              Cancelar
            </Button>
            <Button
              isDisabled={!selectedAgentId}
              onClick={handleClickSubmit}
              isLoading={updateCustomersAgentMutation.isLoading}
              variant="primary"
            >
              Confirmar
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
