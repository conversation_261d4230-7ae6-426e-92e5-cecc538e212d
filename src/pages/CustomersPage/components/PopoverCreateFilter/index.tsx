import {
  <PERSON>over,
  <PERSON>overTrigger,
  Button,
  PopoverContent,
  FocusLock,
  PopoverArrow,
  Stack,
  Input,
  ButtonGroup,
  Text,
  useToast,
} from '@chakra-ui/react';
import { ReactNode, useEffect, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import {
  CreateFilterDto,
  FiltersService,
} from '../../../../services/filters.service';
import { Filter } from '../../../../types/Filter';
import { FilterType } from '../../../../types/FilterType';

export interface PopoverSaveFilterProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  children: ReactNode;
  searchParams: URLSearchParams;
  onCreateFilter: (filter: Filter) => void;
  baseCopyFilter: string | undefined;
}

const PopoverCreateFilter = ({
  isOpen,
  onOpen,
  onClose,
  children,
  searchParams,
  onCreateFilter,
  baseCopyFilter,
}: PopoverSaveFilterProps) => {
  const [newFilterName, setNewFilterName] = useState('');

  useEffect(() => {
    if (isOpen) {
      setNewFilterName(baseCopyFilter ? `${baseCopyFilter} (copy)` : '');
    }
  }, [isOpen]);

  const toast = useToast();
  const queryClient = useQueryClient();

  const createFilter = useMutation(
    async (createFilterDto: CreateFilterDto) => {
      const { data } = await FiltersService.createFilter(createFilterDto);
      return data;
    },
    {
      onSuccess: (data) => {
        queryClient.setQueryData(apiRoutes.listFilters(), (oldData: any) => {
          return [...oldData, data];
        });

        onCreateFilter(data);
        onClose();
        toast({
          title: 'Filtro salvo com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleClickCreateFilter() {
    await createFilter.mutateAsync({
      name: newFilterName,
      criteria: searchParams.toString(),
      type: FilterType.customer,
    });
  }

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      placement="top-start"
      closeOnBlur={false}
    >
      <PopoverTrigger>{children}</PopoverTrigger>
      <PopoverContent p={5}>
        <FocusLock>
          <PopoverArrow />
          <Stack spacing={4}>
            <Text>Digite o nome do filtro:</Text>
            <Input
              id="first-name"
              onChange={(e) => setNewFilterName(e.target.value)}
              value={newFilterName}
            />
            <ButtonGroup
              display="flex"
              justifyContent="space-between"
              width="100%"
            >
              <Button
                flex="1"
                variant="outline"
                onClick={() => {
                  onClose();
                }}
              >
                Cancelar
              </Button>
              <Button
                flex="1"
                isDisabled={!newFilterName}
                colorScheme="teal"
                onClick={() => {
                  handleClickCreateFilter();
                  onClose();
                }}
                isLoading={createFilter.isLoading}
              >
                Salvar
              </Button>
            </ButtonGroup>
          </Stack>
        </FocusLock>
      </PopoverContent>
    </Popover>
  );
};

export default PopoverCreateFilter;
