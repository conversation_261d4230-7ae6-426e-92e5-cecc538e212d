export interface GraphDataItem {
  label: string;
  count: number;
}

export interface CustomerGraphData {
  stateData: GraphDataItem[];
  orderCountData: GraphDataItem[];
  totalPurchasesData: GraphDataItem[];
  averageOrderValueData: GraphDataItem[];
  daysSinceLastPurchaseData: GraphDataItem[];
  tagsData: GraphDataItem[];
}

export interface CustomersInsightsResponse {
  data: CustomerGraphData & { totalCustomers: number };
}

export type ChartTypeId =
  | 'state'
  | 'orderCount'
  | 'totalPurchases'
  | 'averageOrderValue'
  | 'daysSinceLastPurchase'
  | 'tags';
