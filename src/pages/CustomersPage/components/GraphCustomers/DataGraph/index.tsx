import { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Flex,
  Spinner,
  Text,
  List,
  ListItem,
  Button,
} from '@chakra-ui/react';
import CustomECharts from '../../../../../components/CustomECharts';
import { UrlUtils } from '../../../../../utils/url.utils';
import { useQuery } from 'react-query';
import { request } from '../../../../../constants/request';
import {
  ChartTypeId,
  CustomerGraphData,
  GraphDataItem,
  CustomersInsightsResponse,
} from '../types/ChartData';
import { chartFormatSaveOptions, chartTypes } from './chart-types.constants';
import { getBarChartOption } from '../charts/barChart';
import { getPieChartOption } from '../charts/pieChart';
import { getHorizontalBarChartOption } from '../charts/horizontalBarChart';
import { colors } from '../../../../../constants/colors';

interface CustomerDataChartsProps {
  dataUrl: string;
  queryParameters?: Record<string, string | undefined | null>;
}

const CustomersGraphData = ({
  queryParameters,
  dataUrl,
}: CustomerDataChartsProps) => {
  const [dataUrlWithParams, setDataUrlWithParams] = useState(
    `${dataUrl}?${UrlUtils.convertObjectToQueryString(queryParameters)}`,
  );

  const {
    data: customersInsights,
    refetch: refetchChartData,
    isFetching: isFetchingData,
  } = useQuery<CustomersInsightsResponse>(dataUrlWithParams, async () => {
    const { data } = await request.get(dataUrlWithParams, {
      timeout: 1000 * 120,
    });
    return data;
  });
  const [selectedChartType, setSelectedChartType] =
    useState<ChartTypeId>('state');
  const [chartKey, setChartKey] = useState(0);

  useEffect(() => {
    setDataUrlWithParams(
      `${dataUrl}?${UrlUtils.convertObjectToQueryString(queryParameters)}`,
    );
  }, [dataUrl, JSON.stringify(queryParameters), refetchChartData]);

  const handleChartTypeChange = (chartType: ChartTypeId) => {
    setSelectedChartType(chartType);
    setChartKey((prevKey) => prevKey + 1);
  };

  const getSelectedChartTitle = () => {
    const selectedChart = chartTypes.find(
      (chart) => chart.id === selectedChartType,
    );
    return selectedChart ? selectedChart.title : '';
  };

  const getSelectedChartSubtitle = () => {
    const selectedChart = chartTypes.find(
      (chart) => chart.id === selectedChartType,
    );
    return selectedChart?.subtitle || '';
  };

  const getSelectedChartData = (): GraphDataItem[] => {
    const selectedChart = chartTypes.find(
      (chart) => chart.id === selectedChartType,
    );

    if (!selectedChart || !customersInsights?.data) {
      return [];
    }

    const chartData = customersInsights.data as CustomerGraphData;
    return chartData[selectedChart.dataKey as keyof CustomerGraphData] || [];
  };

  const getChartOption = (type: ChartTypeId, data: GraphDataItem[]) => {
    switch (type) {
      case 'state':
        return getHorizontalBarChartOption(data);
      case 'orderCount':
        return getBarChartOption(data);
      case 'totalPurchases':
        return getBarChartOption(data);
      case 'averageOrderValue':
        return getHorizontalBarChartOption(data);
      case 'daysSinceLastPurchase':
        return getBarChartOption(data);
      case 'tags':
        return getPieChartOption(data);
      default:
        return getBarChartOption(data);
    }
  };

  const getColorForChartType = (chartTypeId: ChartTypeId) => {
    const chartColorMapping = {
      state: colors.chartColors.lightBlue,
      orderCount: colors.chartColors.lightGreen,
      totalPurchases: colors.chartColors.lightYellow,
      averageOrderValue: colors.chartColors.lightPurple,
      daysSinceLastPurchase: colors.chartColors.lightBlue,
      tags: colors.chartColors.lightRed,
    };

    return chartColorMapping[chartTypeId] || colors.chartColors.grey;
  };

  return (
    <Box p={4}>
      <Box mb={5}>
        <Heading>Análise de Dados dos Clientes</Heading>
        <Text fontSize="md" variant="subtitle" mt={2}>
          Total de clientes:{' '}
          <strong>{customersInsights?.data.totalCustomers}</strong>
        </Text>
      </Box>
      <Flex direction="row" gap={4}>
        <Box flex="3" minH="400px">
          {isFetchingData ? (
            <Card h="100%">
              <CardBody
                display="flex"
                justifyContent="center"
                alignItems="center"
                h="100%"
                w="100%"
              >
                <Spinner size="xl" />
              </CardBody>
            </Card>
          ) : (
            <Card h="100%">
              <CardHeader>
                <Heading size="lg">{getSelectedChartTitle()}</Heading>
                <Text fontSize="md" variant="subtitle" mt={1}>
                  {getSelectedChartSubtitle()}
                </Text>
              </CardHeader>
              <CardBody>
                {getSelectedChartData().length === 0 ? (
                  <Flex
                    justifyContent="center"
                    alignItems="center"
                    h="100%"
                    w="100%"
                  >
                    <Text>Nenhum dado disponível</Text>
                  </Flex>
                ) : (
                  <CustomECharts
                    key={chartKey}
                    option={getChartOption(
                      selectedChartType,
                      getSelectedChartData(),
                    )}
                    chartHeight="500px"
                    chartWidth="100%"
                  />
                )}
              </CardBody>
            </Card>
          )}
        </Box>

        <Box flex="1" display="flex" flexDirection="column" gap={4}>
          <Card flex="1">
            <CardHeader>
              <Heading size="lg">Tipos de Gráficos</Heading>
              <Text fontSize="md" variant="subtitle" mt={1}>
                Selecione o tipo de análise
              </Text>
            </CardHeader>
            <CardBody>
              <List spacing={3}>
                {chartTypes.map((chartType) => (
                  <ListItem key={chartType.id}>
                    <Button
                      variant="outline"
                      width="100%"
                      justifyContent="flex-start"
                      onClick={() => handleChartTypeChange(chartType.id)}
                      bg={
                        selectedChartType === chartType.id
                          ? getColorForChartType(chartType.id).primary
                          : 'white'
                      }
                      color={
                        selectedChartType === chartType.id ? 'white' : 'black'
                      }
                      fontWeight={
                        selectedChartType === chartType.id ? 'bold' : 'normal'
                      }
                      borderRadius="md"
                      boxShadow="none"
                      transition="all 0.2s"
                      _hover={{
                        bg:
                          selectedChartType === chartType.id
                            ? getColorForChartType(chartType.id).primary
                            : 'gray.50',
                      }}
                    >
                      {chartType.title}
                    </Button>
                  </ListItem>
                ))}
              </List>
            </CardBody>
          </Card>
          <Card flex="1" display="none">
            <CardHeader>
              <Heading size="lg">Exportar Dados</Heading>
              <Text fontSize="md" variant="subtitle" mt={1}>
                Baixe os dados em diferentes formatos
              </Text>
            </CardHeader>
            <CardBody>
              <List spacing={2}>
                {chartFormatSaveOptions.map((saveOption) => (
                  <ListItem key={saveOption.id}>
                    <Button
                      variant="outline"
                      width="100%"
                      justifyContent="flex-start"
                      onClick={() => console.log('salvar o gráfico')}
                    >
                      {saveOption.title}
                    </Button>
                  </ListItem>
                ))}
              </List>
            </CardBody>
          </Card>
        </Box>
      </Flex>
    </Box>
  );
};

export default CustomersGraphData;
