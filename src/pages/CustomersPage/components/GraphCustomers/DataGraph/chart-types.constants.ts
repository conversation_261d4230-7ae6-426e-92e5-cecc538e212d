import { ChartTypeId, CustomerGraphData } from '../types/ChartData';

export const chartTypes: ChartType[] = [
  {
    id: 'state',
    title: 'Distribuição por Estado',
    subtitle: 'Top 10 estados com mais clientes',
    dataKey: 'stateData',
  },
  {
    id: 'orderCount',
    title: 'Total de Pedidos',
    subtitle: 'Quantidade de clientes por número de pedidos',
    dataKey: 'orderCountData',
  },
  {
    id: 'totalPurchases',
    title: 'Total em compras',
    subtitle: 'Distribuição de clientes por faixa de valor total em compras',
    dataKey: 'totalPurchasesData',
  },
  {
    id: 'averageOrderValue',
    title: 'Ticket Médio dos clientes',
    subtitle: 'Valor médio de compra por cliente',
    dataKey: 'averageOrderValueData',
  },
  {
    id: 'daysSinceLastPurchase',
    title: 'Médida de dias desde a última compra',
    subtitle: 'Tempo desde a última interação',
    dataKey: 'daysSinceLastPurchaseData',
  },
  {
    id: 'tags',
    title: 'Tags',
    subtitle: 'Tempo desde a última interação',
    dataKey: 'tagsData',
  },
];

export const chartFormatSaveOptions: chartSaveOptions[] = [
  {
    id: 'pdf',
    title: 'Exportar como PDF',
  },
  {
    id: 'excel',
    title: 'Exportar como Excel',
  },
  {
    id: 'csv',
    title: 'Exportar como CSV',
  },
];

interface ChartType {
  id: ChartTypeId;
  title: string;
  subtitle: string;
  dataKey: keyof CustomerGraphData;
}
interface chartSaveOptions {
  id: string;
  title: string;
}
