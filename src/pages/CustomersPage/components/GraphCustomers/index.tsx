import { useSelector } from 'react-redux';
import { useCustomerSearchParams } from '../../../../hooks/useCustomerSearchParams';
import { RootState } from '../../../../state/store';
import CustomersGraphData from './DataGraph';

const GraphCustomers = () => {
  const { showSelectCustomerRows } = useSelector(
    (state: RootState) => state.campaignCreation,
  );
  const {
    selectedEngagementTemplateIds,
    selectedEngagementEmailTemplateIds,
    searchQuery,
    minTotalPurchases,
    maxTotalPurchases,
    minAverageOrderValue,
    maxAverageOrderValue,
    minTotalOrders,
    maxTotalOrders,
    selectedEngagementActionTypes,
    selectedEmailEngagementActionTypes,
    startOrdersCreatedAt,
    endOrdersCreatedAt,
    minDaysSinceLastCampaign,
    minDaysSinceLastEmailCampaign,
    sortBy,
    minAverageItemValue,
    maxAverageItemValue,
    selectedTags,
    minDaysSinceLastPurchase,
    maxDaysSinceLastPurchase,
    exactDaysSinceLastPurchase,
    isOrderSubscription,
    excludedTags,
    selectedDefaultAgentIds,
    excludedTemplateIds,
    excludedEmailTemplateIds,
    selectedProductIds,
    excludedProductIds,
    selectedProductComparator,
    excludedProductComparator,
    minProductQuantity,
    maxProductQuantity,
    minDaysSinceLastProductPurchase,
    maxDaysSinceLastProductPurchase,
    productNameContains,
    isLastProductPurchased,
    customFieldId1,
    customFieldValue1,
    customFieldComparisonType1,
    isScheduledCampaignsVisible,
    isScheduledEmailCampaignsVisible,
    platformOrderSource,
    selectedStates,
    selectedCoupons,
    selectedOrdersStatuses,
    selectedCities,
    hasEmail,
    hasPhoneNumberId,
    selectedCampaignChannel,
    daysUntilBirthday,
    selectedOrdersSources,
    selectedOrdersStoreNames,
    selectedOrdersSalesChannels,
  } = useCustomerSearchParams();

  return (
    <CustomersGraphData
      dataUrl="/customers/insights"
      queryParameters={{
        selectedTemplateIds: selectedEngagementTemplateIds,
        selectedEmailTemplateIds: selectedEngagementEmailTemplateIds,
        selectedEngagementActionTypes,
        selectedEmailEngagementActionTypes,
        searchQuery,
        minTotalPurchases: String(Number(minTotalPurchases) * 100),
        maxTotalPurchases: String(Number(maxTotalPurchases) * 100),
        minAverageOrderValue: String(Number(minAverageOrderValue) * 100),
        maxAverageOrderValue: String(Number(maxAverageOrderValue) * 100),
        minAverageItemValue: String(Number(minAverageItemValue) * 100),
        maxAverageItemValue: String(Number(maxAverageItemValue) * 100),
        minTotalOrders,
        maxTotalOrders,
        startOrdersCreatedAt,
        endOrdersCreatedAt,
        minDaysSinceLastCampaign,
        minDaysSinceLastEmailCampaign,
        sortBy,
        selectedTags,
        minDaysSinceLastPurchase,
        maxDaysSinceLastPurchase,
        exactDaysSinceLastPurchase,
        isOrderSubscription,
        excludedTags,
        selectedDefaultAgentIds,
        excludedTemplateIds,
        excludedEmailTemplateIds,
        selectedProductIds,
        excludedProductIds,
        minProductQuantity,
        maxProductQuantity,
        minDaysSinceLastProductPurchase,
        maxDaysSinceLastProductPurchase,
        productNameContains,
        isLastProductPurchased,
        customFieldId1,
        customFieldValue1,
        customFieldComparisonType1,
        isScheduledCampaignsVisible,
        isScheduledEmailCampaignsVisible,
        isCreatingCampaign: String(showSelectCustomerRows),
        platformOrderSource,
        selectedStates,
        selectedCoupons,
        selectedOrdersStatuses,
        selectedCities,
        hasEmail,
        hasPhoneNumberId,
        selectedCampaignChannel,
        selectedProductComparator,
        excludedProductComparator,
        daysUntilBirthday,
        selectedOrdersSources,
        selectedOrdersStoreNames,
        selectedOrdersSalesChannels,
      }}
    />
  );
};

export default GraphCustomers;
