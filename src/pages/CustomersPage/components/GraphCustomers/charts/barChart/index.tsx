import { colors } from '../../../../../../constants/colors';

export const getBarChartOption = (data: any[]) => {
  const gradientColors = [
    colors.chartColors.lightPurple.primary,
    colors.chartColors.lightGreen.primary,
    colors.chartColors.lightBlue.primary,
    colors.chartColors.lightYellow.primary,
    colors.chartColors.lightRed.primary,
  ];

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        return `${params[0].name} </br> Quantidade: <b>${params[0].value}</b> clientes`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) =>
        item.label !== undefined && item.label !== null
          ? item.label
          : item.name || 'Desconhecido',
      ),
      axisLabel: {
        interval: 0,
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: 'Quantidade',
        type: 'bar',
        data: data.map((item, index) => ({
          value: item.count || item.value || 0,
          itemStyle: { color: gradientColors[index % gradientColors.length] },
        })),
      },
    ],
  };
};
