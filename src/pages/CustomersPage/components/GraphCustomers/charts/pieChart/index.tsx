import { colors } from '../../../../../../constants/colors';

export const getPieChartOption = (data: any[]) => {
  const gradientColors = [
    colors.chartColors.lightPurple.primary,
    colors.chartColors.lightGreen.primary,
    colors.chartColors.lightBlue.primary,
    colors.chartColors.lightYellow.primary,
    colors.chartColors.lightRed.primary,
    colors.chartColors.lightYellow.primary,
    colors.chartColors.green.secondary,
    colors.chartColors.blue.secondary,
    colors.chartColors.darkOrange.secondary,
    colors.chartColors.darkBlue.secondary,
    colors.chartColors.lightRed.secondary,
  ];

  return {
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        return `${params.name}</br> Quantidade: <b>${params.value}</b> clientes`;
      },
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        color: '#333',
      },
    },
    series: [
      {
        name: 'Quantidade',
        type: 'pie',
        radius: '50%',
        data: data.map((item, index) => ({
          value: item.count || item.value || 0,
          name: item.label || item.name || 'Desconhecido',
          itemStyle: {
            color: gradientColors[index] || colors.chartColors.blue.primary,
          },
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
};
