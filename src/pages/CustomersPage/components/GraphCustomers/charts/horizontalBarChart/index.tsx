import { colors } from '../../../../../../constants/colors';

export const getHorizontalBarChartOption = (data: any[]) => {
  const gradientColors = [
    colors.chartColors.lightPurple.primary,
    colors.chartColors.lightGreen.primary,
    colors.chartColors.lightBlue.primary,
    colors.chartColors.lightYellow.primary,
    colors.chartColors.lightRed.primary,
  ];

  const sortedData = [...data].sort(
    (a, b) => (b.count || b.value || 0) - (a.count || a.value || 0),
  );

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function (params: any) {
        return `${params[0].name}</br> Quantidade: <b>${params[0].value}</b> clientes`;
      },
    },
    grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: sortedData.map((item) => item.label || item.name || 'Desconhecido'),
      axisLabel: { interval: 0, rotate: 45 },
    },
    series: [
      {
        name: 'Quantidade',
        type: 'bar',
        data: sortedData.map((item, index) => ({
          value: item.count || item.value || 0,
          itemStyle: { color: gradientColors[index % gradientColors.length] },
        })),
      },
    ],
  };
};
