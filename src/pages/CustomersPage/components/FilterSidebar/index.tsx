import {
  Accordion,
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel as ChakraFormLabel,
  Select,
  useDisclosure,
  useToast,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Divider,
  Icon,
  Tooltip,
} from '@chakra-ui/react';
import ptBr from 'date-fns/locale/pt-BR';
import { useCallback, useEffect, useState } from 'react';
import { registerLocale } from 'react-datepicker';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useNavigate, useSearchParams } from 'react-router-dom';
import InputSelect, { SelectOption } from '../../../../components/InputSelect';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import { queryStringDelimiter } from '../../../../constants/query-string-delimiter';
import { useCustomerSearchParams } from '../../../../hooks/useCustomerSearchParams';
import {
  FiltersService,
  UpdateFilterDto,
} from '../../../../services/filters.service';
import { scrollbarStyles } from '../../../../styles/scrollbar.styles';
import { CustomerFiltersEnum } from '../../../../types/CustomerFiltersEnum';
import { Filter } from '../../../../types/Filter';
import { UrlUtils } from '../../../../utils/url.utils';
import SectionCampaigns from './SectionCampaigns';
import SectionCustomers from './SectionCustomers';
import SectionCustomFields from './SectionCustomFields';
import SectionProducts from './SectionProducts';
import SectionPurchases from './SectionPurchases';
import SectionRFM from './SectionRFM';
import SectionTags from './SectionTags';
import { resetSelectedCustomers } from '../../../../state/campaignCreationSlice';
import { useDispatch } from 'react-redux';
import PopoverCreateFilter from '../PopoverCreateFilter';
import SectionEmailCampaigns from './SectionEmailCampaigns';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { FiUsers, FiShoppingCart, FiTag, FiBarChart2 } from 'react-icons/fi';
import { colors } from '../../../../constants/colors';
import { AiOutlineTable } from 'react-icons/ai';
import { FaWhatsapp } from 'react-icons/fa';
import { TbZoomMoney } from 'react-icons/tb';

registerLocale('pt-BR', ptBr);

interface FilterSidebarProps {
  isOpen: boolean;
}

export function FilterSidebar({ isOpen }: FilterSidebarProps) {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const toast = useToast();
  const queryClient = useQueryClient();
  const {
    selectedEngagementTemplateIds,
    selectedEngagementEmailTemplateIds,
    searchQuery,
    minTotalPurchases,
    maxTotalPurchases,
    minAverageOrderValue,
    maxAverageOrderValue,
    minTotalOrders,
    maxTotalOrders,
    selectedEngagementActionTypes,
    selectedEmailEngagementActionTypes,
    startOrdersCreatedAt,
    endOrdersCreatedAt,
    minDaysSinceLastCampaign,
    minDaysSinceLastEmailCampaign,
    sortBy,
    minAverageItemValue,
    maxAverageItemValue,
    selectedTags,
    excludedTags,
    selectedDefaultAgentIds,
    minDaysSinceLastPurchase,
    maxDaysSinceLastPurchase,
    exactDaysSinceLastPurchase,
    isOrderSubscription,
    excludedTemplateIds,
    excludedEmailTemplateIds,
    selectedProductIds,
    excludedProductIds,
    selectedProductComparator,
    excludedProductComparator,
    minProductQuantity,
    maxProductQuantity,
    minDaysSinceLastProductPurchase,
    maxDaysSinceLastProductPurchase,
    productNameContains,
    isLastProductPurchased,
    customFieldId1,
    customFieldValue1,
    customFieldComparisonType1,
    customFieldId2,
    customFieldValue2,
    customFieldComparisonType2,
    customFieldId3,
    customFieldValue3,
    customFieldComparisonType3,
    customFieldId4,
    customFieldValue4,
    customFieldComparisonType4,
    isScheduledCampaignsVisible,
    isScheduledEmailCampaignsVisible,
    selectedStates,
    selectedCoupons,
    selectedOrdersStatuses,
    selectedCities,
    hasEmail,
    hasPhoneNumberId,
    selectedCampaignChannel,
    daysUntilBirthday,
    selectedOrdersSources,
    selectedOrdersStoreNames,
    selectedOrdersSalesChannels,
  } = useCustomerSearchParams();
  const createFilterPopover = useDisclosure();
  const actionsPopover = useDisclosure();
  const [selectedFilter, setSelectedFilter] = useState<Filter | null>(null);

  const { data: savedFilters = [] } = useQuery(
    apiRoutes.listFilters(),
    async () => {
      const { data } = await FiltersService.listFilters();
      return data;
    },
  );
  const updateFilter = useMutation(
    (updateFilterDto: UpdateFilterDto) =>
      FiltersService.updateFilter(updateFilterDto),
    {
      onSuccess: (res) => {
        queryClient.setQueryData(apiRoutes.listFilters(), (oldData: any) => {
          return oldData.map((filter: Filter) =>
            filter.id === res.data.id ? res.data : filter,
          );
        });
        toast({
          title: 'Filtro atualizado com sucesso',
          status: 'success',
          duration: 2000,
          isClosable: true,
        });
      },
    },
  );

  const valuesFromUrl = {
    searchQuery,
    selectedEngagementTemplateIds: [] as SelectOption[], // this field is not cached
    selectedEngagementEmailTemplateIds: [] as SelectOption[], // this field is not cached
    selectedEngagementActionTypes: [] as SelectOption[], // this field is not cached
    selectedEmailEngagementActionTypes: [] as SelectOption[], // this field is not cached
    totalPurchases: {
      minValue: minTotalPurchases,
      maxValue: maxTotalPurchases,
    },
    averageOrderValue: {
      minValue: minAverageOrderValue,
      maxValue: maxAverageOrderValue,
    },
    averageItemValue: {
      minValue: minAverageItemValue,
      maxValue: maxAverageItemValue,
    },
    totalOrders: {
      minValue: minTotalOrders,
      maxValue: maxTotalOrders,
    },
    startOrdersCreatedAt,
    endOrdersCreatedAt,
    minDaysSinceLastCampaign,
    minDaysSinceLastEmailCampaign,
    sortBy,
    selectedTags: [] as SelectOption[], // this field is not cached
    excludedTags: [] as SelectOption[], // this field is not cached
    selectedDefaultAgentIds: [] as SelectOption[],
    minDaysSinceLastPurchase,
    maxDaysSinceLastPurchase,
    exactDaysSinceLastPurchase,
    isOrderSubscription: isOrderSubscription
      ? isOrderSubscription === 'true'
        ? true
        : false
      : undefined,
    excludedTemplateIds: [] as SelectOption[],
    excludedEmailTemplateIds: [] as SelectOption[],
    selectedProductIds: [] as SelectOption[], // this field is not cached
    excludedProductIds: [] as SelectOption[], // this field is not cached
    selectedProductComparator: selectedProductComparator || '',
    excludedProductComparator: excludedProductComparator || '',
    productQuantity: {
      minValue: minProductQuantity,
      maxValue: maxProductQuantity,
    },
    daysSinceLastProductPurchase: {
      minValue: minDaysSinceLastProductPurchase,
      maxValue: maxDaysSinceLastProductPurchase,
    },
    productNameContains,
    isLastProductPurchased: isLastProductPurchased === 'true',
    customFieldId1,
    customFieldValue1,
    customFieldComparisonType1,
    customFieldId2,
    customFieldValue2,
    customFieldComparisonType2,
    customFieldId3,
    customFieldValue3,
    customFieldComparisonType3,
    customFieldId4,
    customFieldValue4,
    customFieldComparisonType4,
    isScheduledCampaignsVisible:
      isScheduledCampaignsVisible === 'true' || false,
    isScheduledEmailCampaignsVisible:
      isScheduledEmailCampaignsVisible === 'true',
    selectedStates: [] as SelectOption[], // this field is not cached
    selectedCoupons: [] as SelectOption[], // this field is not cached
    selectedOrdersStatuses: [] as SelectOption[],
    selectedCities: [] as SelectOption[],
    hasEmail: hasEmail === 'true',
    hasPhoneNumberId: hasPhoneNumberId === 'true',
    selectedCampaignChannel,
    daysUntilBirthday,
    selectedOrdersSources: [] as SelectOption[],
    selectedOrdersStoreNames: [] as SelectOption[],
    selectedOrdersSalesChannels: [] as SelectOption[],
  };
  const useFormReturn = useForm({
    defaultValues: valuesFromUrl,
  });

  const { register, handleSubmit, reset, setValue, getValues } = useFormReturn;

  useEffect(() => {
    const {
      selectedEngagementTemplateIds,
      selectedEngagementEmailTemplateIds,
      selectedEngagementActionTypes,
      selectedEmailEngagementActionTypes,
      selectedTags,
      excludedTags,
      selectedDefaultAgentIds,
      selectedProductIds,
      excludedProductIds,
      excludedTemplateIds,
      excludedEmailTemplateIds,
      selectedStates,
      selectedCoupons,
      selectedOrdersStatuses,
      selectedCities,
      daysUntilBirthday,
      selectedOrdersSources,
      selectedOrdersStoreNames,
      selectedOrdersSalesChannels,
    } = getValues();

    reset({
      ...valuesFromUrl,
      selectedEngagementTemplateIds,
      selectedEngagementEmailTemplateIds,
      selectedEngagementActionTypes,
      selectedEmailEngagementActionTypes,
      selectedTags,
      excludedTags,
      selectedDefaultAgentIds,
      selectedProductIds,
      excludedProductIds,
      excludedTemplateIds,
      excludedEmailTemplateIds,
      selectedStates,
      selectedCoupons,
      selectedOrdersStatuses,
      selectedCities,
      daysUntilBirthday,
      selectedOrdersSources,
      selectedOrdersStoreNames,
      selectedOrdersSalesChannels,
    });
  }, [reset, searchParams.toString(), selectedFilter?.criteria]);

  const updateSelectedValues = useCallback(
    (options: {
      selectedValues: string;
      sourceData: any[] | undefined;
      valueToSet: any;
      optionValue: string;
      optionLabel: string;
    }) => {
      const {
        selectedValues,
        sourceData,
        valueToSet,
        optionValue,
        optionLabel,
      } = options;

      if (!selectedValues || selectedValues.length === 0) {
        setValue(valueToSet, []);
        return;
      }
      if (!sourceData) return;

      const valueIds = selectedValues.split(queryStringDelimiter);
      const selectedOptions = sourceData.filter((item) =>
        valueIds.includes(item[optionValue]),
      );
      const updatedValues = selectedOptions.map((item) => ({
        value: item[optionValue],
        label: item[optionLabel],
      }));

      setValue(valueToSet, updatedValues);
    },
    [setValue],
  );

  function onSubmit(data: {
    searchQuery?: string;
    selectedEngagementTemplateIds?: SelectOption[];
    selectedEngagementEmailTemplateIds?: SelectOption[];
    selectedEngagementActionTypes?: SelectOption[];
    selectedEmailEngagementActionTypes?: SelectOption[];
    totalPurchases?: { minValue?: any; maxValue?: any };
    averageOrderValue?: { minValue?: any; maxValue?: any };
    averageItemValue?: { minValue?: any; maxValue?: any };
    totalOrders?: { minValue?: any; maxValue?: any };
    startOrdersCreatedAt?: string;
    endOrdersCreatedAt?: string;
    minDaysSinceLastCampaign?: any;
    minDaysSinceLastEmailCampaign?: any;
    sortBy?: string;
    selectedTags?: SelectOption[];
    excludedTags?: SelectOption[];
    selectedDefaultAgentIds?: SelectOption[];
    minDaysSinceLastPurchase?: any;
    maxDaysSinceLastPurchase?: any;
    exactDaysSinceLastPurchase?: any;
    isOrderSubscription?: boolean;
    excludedTemplateIds?: SelectOption[];
    excludedEmailTemplateIds?: SelectOption[];
    selectedProductIds?: SelectOption[];
    excludedProductIds?: SelectOption[];
    selectedProductComparator?: string;
    excludedProductComparator?: string;
    productQuantity?: { minValue?: any; maxValue?: any };
    daysSinceLastProductPurchase?: { minValue?: any; maxValue?: any };
    productNameContains?: string;
    isLastProductPurchased?: boolean;
    customFieldId1?: string;
    customFieldValue1?: string;
    customFieldComparisonType1?: string;
    customFieldId2?: string;
    customFieldValue2?: string;
    customFieldComparisonType2?: string;
    customFieldId3?: string;
    customFieldValue3?: string;
    customFieldComparisonType3?: string;
    customFieldId4?: string;
    customFieldValue4?: string;
    customFieldComparisonType4?: string;
    isScheduledCampaignsVisible?: boolean;
    isScheduledEmailCampaignsVisible?: boolean;
    selectedStates?: SelectOption[];
    selectedCoupons?: SelectOption[];
    selectedOrdersStatuses?: SelectOption[];
    selectedCities?: SelectOption[];
    hasEmail?: boolean;
    hasPhoneNumberId?: boolean;
    selectedCampaignChannel?: string;
    daysUntilBirthday?: string;
    selectedOrdersSources?: SelectOption[];
    selectedOrdersStoreNames?: SelectOption[];
    selectedOrdersSalesChannels?: SelectOption[];
  }) {
    const {
      searchQuery,
      selectedEngagementTemplateIds,
      selectedEngagementEmailTemplateIds,
      selectedEngagementActionTypes,
      selectedEmailEngagementActionTypes,
      totalPurchases,
      averageOrderValue,
      totalOrders,
      startOrdersCreatedAt,
      endOrdersCreatedAt,
      minDaysSinceLastCampaign,
      minDaysSinceLastEmailCampaign,
      sortBy,
      averageItemValue,
      selectedTags,
      minDaysSinceLastPurchase,
      maxDaysSinceLastPurchase,
      exactDaysSinceLastPurchase,
      isOrderSubscription,
      excludedTags,
      selectedDefaultAgentIds,
      excludedTemplateIds,
      excludedEmailTemplateIds,
      selectedProductIds,
      excludedProductIds,
      selectedProductComparator,
      excludedProductComparator,
      productQuantity,
      daysSinceLastProductPurchase,
      productNameContains,
      isLastProductPurchased,
      customFieldId1,
      customFieldValue1,
      customFieldComparisonType1,
      customFieldId2,
      customFieldValue2,
      customFieldComparisonType2,
      customFieldId3,
      customFieldValue3,
      customFieldComparisonType3,
      customFieldId4,
      customFieldValue4,
      customFieldComparisonType4,
      isScheduledCampaignsVisible,
      isScheduledEmailCampaignsVisible,
      selectedStates,
      selectedCoupons,
      selectedOrdersStatuses,
      selectedCities,
      hasEmail,
      hasPhoneNumberId,
      selectedCampaignChannel,
      daysUntilBirthday,
      selectedOrdersSources,
      selectedOrdersStoreNames,
      selectedOrdersSalesChannels,
    } = data;

    const queryValues: Record<string, string> = {
      [CustomerFiltersEnum.SEARCH_QUERY]: searchQuery || '',
      [CustomerFiltersEnum.SELECTED_ENGAGEMENT_TEMPLATE_IDS]:
        selectedEngagementTemplateIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_ENGAGEMENT_EMAIL_TEMPLATE_IDS]:
        selectedEngagementEmailTemplateIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_ENGAGEMENT_ACTION_TYPES]:
        selectedEngagementActionTypes
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_ENGAGEMENT_EMAIL_ACTION_TYPES]:
        selectedEmailEngagementActionTypes
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.MIN_TOTAL_PURCHASES]: totalPurchases?.minValue || '',
      [CustomerFiltersEnum.MAX_TOTAL_PURCHASES]: totalPurchases?.maxValue || '',
      [CustomerFiltersEnum.MIN_AVERAGE_ORDER_VALUE]:
        averageOrderValue?.minValue || '',
      [CustomerFiltersEnum.MAX_AVERAGE_ORDER_VALUE]:
        averageOrderValue?.maxValue || '',
      [CustomerFiltersEnum.MIN_TOTAL_ORDERS]: totalOrders?.minValue || '',
      [CustomerFiltersEnum.MAX_TOTAL_ORDERS]: totalOrders?.maxValue || '',
      [CustomerFiltersEnum.START_ORDERS_CREATED_AT]: startOrdersCreatedAt || '',
      [CustomerFiltersEnum.END_ORDERS_CREATED_AT]: endOrdersCreatedAt || '',
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_CAMPAIGN]:
        minDaysSinceLastCampaign || '',
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN]:
        minDaysSinceLastEmailCampaign || '',
      [CustomerFiltersEnum.SORT_BY]: sortBy || '',
      [CustomerFiltersEnum.MIN_AVERAGE_ITEM_VALUE]:
        averageItemValue?.minValue || '',
      [CustomerFiltersEnum.MAX_AVERAGE_ITEM_VALUE]:
        averageItemValue?.maxValue || '',
      [CustomerFiltersEnum.SELECTED_TAGS]:
        selectedTags
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.EXCLUDED_TAGS]:
        excludedTags
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_DEFAULT_AGENT_IDS]:
        selectedDefaultAgentIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PURCHASE]:
        minDaysSinceLastPurchase || '',
      [CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PURCHASE]:
        maxDaysSinceLastPurchase || '',
      [CustomerFiltersEnum.EXACT_DAYS_SINCE_LAST_PURCHASE]:
        exactDaysSinceLastPurchase || '',
      [CustomerFiltersEnum.IS_ORDER_SUBSCRIPTION]:
        isOrderSubscription === undefined ? '' : isOrderSubscription.toString(),
      [CustomerFiltersEnum.EXCLUDED_TEMPLATE_IDS]:
        excludedTemplateIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.EXCLUDED_EMAIL_TEMPLATE_IDS]:
        excludedEmailTemplateIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_PRODUCT_IDS]:
        selectedProductIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.EXCLUDED_PRODUCT_IDS]:
        excludedProductIds
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_PRODUCT_COMPARATOR]:
        selectedProductComparator || '',
      [CustomerFiltersEnum.EXCLUDED_PRODUCT_COMPARATOR]:
        excludedProductComparator || '',
      [CustomerFiltersEnum.MIN_PRODUCT_QUANTITY]:
        productQuantity?.minValue || '',
      [CustomerFiltersEnum.MAX_PRODUCT_QUANTITY]:
        productQuantity?.maxValue || '',
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PRODUCT_PURCHASE]:
        daysSinceLastProductPurchase?.minValue || '',
      [CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PRODUCT_PURCHASE]:
        daysSinceLastProductPurchase?.maxValue || '',
      [CustomerFiltersEnum.PRODUCT_NAME_CONTAINS]: productNameContains || '',
      [CustomerFiltersEnum.IS_LAST_PRODUCT_PURCHASED]: isLastProductPurchased
        ? 'true'
        : '',
      [CustomerFiltersEnum.CUSTOM_FIELD_ID_1]: customFieldId1 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_VALUE_1]: customFieldValue1 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_1]:
        customFieldComparisonType1 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_ID_2]: customFieldId2 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_VALUE_2]: customFieldValue2 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_2]:
        customFieldComparisonType2 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_ID_3]: customFieldId3 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_VALUE_3]: customFieldValue3 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_3]:
        customFieldComparisonType3 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_ID_4]: customFieldId4 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_VALUE_4]: customFieldValue4 || '',
      [CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_4]:
        customFieldComparisonType4 || '',
      [CustomerFiltersEnum.IS_SCHEDULED_CAMPAIGNS_VISIBLE]:
        isScheduledCampaignsVisible ? 'true' : '',
      [CustomerFiltersEnum.IS_SCHEDULED_EMAIL_CAMPAIGNS_VISIBLE]:
        isScheduledEmailCampaignsVisible ? 'true' : '',
      [CustomerFiltersEnum.SELECTED_STATES]:
        selectedStates
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_COUPONS]:
        selectedCoupons
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_CITIES]:
        selectedCities
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.HAS_EMAIL]: hasEmail ? 'true' : '',
      [CustomerFiltersEnum.HAS_PHONE_NUMBER_ID]: hasPhoneNumberId ? 'true' : '',
      [CustomerFiltersEnum.SELECTED_CAMPAIGN_CHANNEL]:
        selectedCampaignChannel || '',
      [CustomerFiltersEnum.SELECTED_ORDERS_STATUSES]:
        selectedOrdersStatuses
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.DAYS_UNTIL_BIRTHDAY]: daysUntilBirthday || '',
      [CustomerFiltersEnum.SELECTED_ORDERS_SOURCES]:
        selectedOrdersSources
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_ORDERS_STORE_NAMES]:
        selectedOrdersStoreNames
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
      [CustomerFiltersEnum.SELECTED_ORDERS_SALES_CHANNELS]:
        selectedOrdersSalesChannels
          ?.map((option) => option.value)
          .join(queryStringDelimiter) || '',
    };

    Object.keys(queryValues).forEach((key) => {
      if (queryValues[key] === '') {
        searchParams.delete(key);
      } else {
        searchParams.set(key, queryValues[key]);
      }
    });

    searchParams.delete('rfmGroup');

    if (searchParams.toString().length >= 1900) {
      toast({
        title: 'Você atingiu o limite de filtros',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setSearchParams(searchParams);
  }

  function handleChangeSelectedFilter(filterId: string) {
    const foundFilter = savedFilters.find(
      (filter: any) => filter.id === filterId,
    );
    if (foundFilter) {
      const normalizedCriteria = foundFilter.criteria.replace(/\+/g, ' ');

      const filterObject =
        UrlUtils.convertQueryStringToObject(normalizedCriteria);
      const filterCriteria = UrlUtils.convertObjectToQueryString({
        ...filterObject,
        excludedTemplateIds,
      });
      setSelectedFilter(foundFilter);
      navigate({
        pathname: appPaths.customers.index(),
        search: filterCriteria,
      });

      reset(parseFilterCriteriaToObject(foundFilter.criteria));
    }
  }

  function parseFilterCriteriaToObject(filterCriteria: string) {
    const params = new URLSearchParams(decodeURI(filterCriteria));

    const parseArray = (key: string) =>
      (params.get(key) || '')
        .split('||')
        .filter(Boolean)
        .map((value) => ({ value, label: value }));

    return {
      searchQuery: params.get(CustomerFiltersEnum.SEARCH_QUERY) || '',
      selectedEngagementTemplateIds: parseArray(
        CustomerFiltersEnum.SELECTED_ENGAGEMENT_TEMPLATE_IDS,
      ),
      selectedEngagementActionTypes: parseArray(
        CustomerFiltersEnum.SELECTED_ENGAGEMENT_ACTION_TYPES,
      ),
      totalPurchases: {
        minValue: params.get(CustomerFiltersEnum.MIN_TOTAL_PURCHASES) || '',
        maxValue: params.get(CustomerFiltersEnum.MAX_TOTAL_PURCHASES) || '',
      },
      averageOrderValue: {
        minValue: params.get(CustomerFiltersEnum.MIN_AVERAGE_ORDER_VALUE) || '',
        maxValue: params.get(CustomerFiltersEnum.MAX_AVERAGE_ORDER_VALUE) || '',
      },
      averageItemValue: {
        minValue: params.get(CustomerFiltersEnum.MIN_AVERAGE_ITEM_VALUE) || '',
        maxValue: params.get(CustomerFiltersEnum.MAX_AVERAGE_ITEM_VALUE) || '',
      },
      totalOrders: {
        minValue: params.get(CustomerFiltersEnum.MIN_TOTAL_ORDERS) || '',
        maxValue: params.get(CustomerFiltersEnum.MAX_TOTAL_ORDERS) || '',
      },
      startOrdersCreatedAt:
        params.get(CustomerFiltersEnum.START_ORDERS_CREATED_AT) || '',
      endOrdersCreatedAt:
        params.get(CustomerFiltersEnum.END_ORDERS_CREATED_AT) || '',
      minDaysSinceLastCampaign:
        params.get(CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_CAMPAIGN) || '',
      sortBy: params.get(CustomerFiltersEnum.SORT_BY) || '',
      selectedTags: parseArray(CustomerFiltersEnum.SELECTED_TAGS),
      excludedTags: parseArray(CustomerFiltersEnum.EXCLUDED_TAGS),
      selectedDefaultAgentIds: parseArray(
        CustomerFiltersEnum.SELECTED_DEFAULT_AGENT_IDS,
      ),
      minDaysSinceLastPurchase:
        params.get(CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PURCHASE) || '',
      maxDaysSinceLastPurchase:
        params.get(CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PURCHASE) || '',
      exactDaysSinceLastPurchase:
        params.get(CustomerFiltersEnum.EXACT_DAYS_SINCE_LAST_PURCHASE) || '',
      isOrderSubscription:
        params.get(CustomerFiltersEnum.IS_ORDER_SUBSCRIPTION) === 'true',
      excludedTemplateIds: parseArray(
        CustomerFiltersEnum.EXCLUDED_TEMPLATE_IDS,
      ),
      selectedProductIds: parseArray(CustomerFiltersEnum.SELECTED_PRODUCT_IDS),
      excludedProductIds: parseArray(CustomerFiltersEnum.EXCLUDED_PRODUCT_IDS),
      selectedProductComparator:
        params.get(CustomerFiltersEnum.SELECTED_PRODUCT_COMPARATOR) || '',
      excludedProductComparator:
        params.get(CustomerFiltersEnum.EXCLUDED_PRODUCT_COMPARATOR) || '',
      productQuantity: {
        minValue: params.get(CustomerFiltersEnum.MIN_PRODUCT_QUANTITY) || '',
        maxValue: params.get(CustomerFiltersEnum.MAX_PRODUCT_QUANTITY) || '',
      },
      daysSinceLastProductPurchase: {
        minValue:
          params.get(
            CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PRODUCT_PURCHASE,
          ) || '',
        maxValue:
          params.get(
            CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PRODUCT_PURCHASE,
          ) || '',
      },
      productNameContains:
        params.get(CustomerFiltersEnum.PRODUCT_NAME_CONTAINS) || '',
      isLastProductPurchased:
        params.get(CustomerFiltersEnum.IS_LAST_PRODUCT_PURCHASED) === 'true',
      customFieldId1: params.get(CustomerFiltersEnum.CUSTOM_FIELD_ID_1) || '',
      customFieldValue1:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_VALUE_1) || '',
      customFieldComparisonType1:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_1) || '',
      customFieldId2: params.get(CustomerFiltersEnum.CUSTOM_FIELD_ID_2) || '',
      customFieldValue2:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_VALUE_2) || '',
      customFieldComparisonType2:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_2) || '',
      customFieldId3: params.get(CustomerFiltersEnum.CUSTOM_FIELD_ID_3) || '',
      customFieldValue3:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_VALUE_3) || '',
      customFieldComparisonType3:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_3) || '',
      customFieldId4: params.get(CustomerFiltersEnum.CUSTOM_FIELD_ID_4) || '',
      customFieldValue4:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_VALUE_4) || '',
      customFieldComparisonType4:
        params.get(CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_4) || '',
      isScheduledCampaignsVisible:
        params.get(CustomerFiltersEnum.IS_SCHEDULED_CAMPAIGNS_VISIBLE) ===
        'true',
      selectedStates: parseArray(CustomerFiltersEnum.SELECTED_STATES),
      selectedCoupons: parseArray(CustomerFiltersEnum.SELECTED_COUPONS),
      selectedOrdersStatuses: parseArray(
        CustomerFiltersEnum.SELECTED_ORDERS_STATUSES,
      ),
      selectedCities: parseArray(CustomerFiltersEnum.SELECTED_CITIES),
      daysUntilBirthday:
        params.get(CustomerFiltersEnum.DAYS_UNTIL_BIRTHDAY) || '',
      selectedOrdersSources: parseArray(
        CustomerFiltersEnum.SELECTED_ORDERS_SOURCES,
      ),
      selectedOrdersStoreNames: parseArray(
        CustomerFiltersEnum.SELECTED_ORDERS_STORE_NAMES,
      ),
      selectedOrdersSalesChannels: parseArray(
        CustomerFiltersEnum.SELECTED_ORDERS_SALES_CHANNELS,
      ),
    };
  }

  async function handleClickUpdateFilter() {
    await updateFilter.mutateAsync({
      filterId: selectedFilter!.id,
      criteria: searchParams.toString(),
    });
  }

  function handleClearFilters() {
    if (searchParams.get('selectedCampaignChannel') === 'email') {
      setSearchParams(
        new URLSearchParams({
          hasEmail: 'true',
          selectedCampaignChannel: 'email',
        }),
      );
    } else if (
      searchParams.get('selectedCampaignChannel') === 'sms' ||
      searchParams.get('selectedCampaignChannel') === 'whatsapp'
    ) {
      setSearchParams(
        new URLSearchParams({
          hasPhoneNumberId: 'true',
          selectedCampaignChannel:
            searchParams.get('selectedCampaignChannel') || '',
        }),
      );
    } else {
      setSearchParams(new URLSearchParams());
    }
    dispatch(resetSelectedCustomers());
    setSelectedFilter(null);
  }

  const totalAppliedFilters = [
    searchQuery,
    selectedEngagementTemplateIds,
    selectedEngagementEmailTemplateIds,
    selectedEngagementActionTypes,
    selectedEmailEngagementActionTypes,
    Number(minTotalPurchases) || Number(maxTotalPurchases),
    Number(minAverageOrderValue) || Number(maxAverageOrderValue),
    Number(minTotalOrders) || Number(maxTotalOrders),
    startOrdersCreatedAt,
    endOrdersCreatedAt,
    Number(minDaysSinceLastCampaign),
    Number(minDaysSinceLastEmailCampaign),
    Number(minAverageItemValue) || Number(maxAverageItemValue),
    selectedTags,
    excludedTags,
    selectedDefaultAgentIds,
    Number(minDaysSinceLastPurchase),
    Number(maxDaysSinceLastPurchase),
    exactDaysSinceLastPurchase,
    isOrderSubscription,
    selectedProductIds,
    excludedProductIds,
    selectedProductComparator,
    excludedProductComparator,
    Number(minProductQuantity) || Number(maxProductQuantity),
    Number(minDaysSinceLastProductPurchase) ||
      Number(maxDaysSinceLastProductPurchase),
    productNameContains,
    isLastProductPurchased,
    customFieldValue1,
    customFieldComparisonType1,
    isScheduledCampaignsVisible,
    isScheduledEmailCampaignsVisible,
    selectedStates,
    selectedCoupons,
    selectedOrdersStatuses,
    selectedCities,
    hasEmail,
    hasPhoneNumberId,
    selectedCampaignChannel,
    Number(daysUntilBirthday),
    selectedOrdersSources,
    selectedOrdersStoreNames,
    selectedOrdersSalesChannels,
  ].filter((filter) => !!filter).length;

  const accordionDefaultIndex = [];
  if (searchParams.get('rfmGroup')) {
    accordionDefaultIndex.push(1);
  }
  if (excludedTemplateIds?.length) {
    accordionDefaultIndex.push(2);
  }
  // TODO-AS: check this
  if (excludedEmailTemplateIds?.length) {
    accordionDefaultIndex.push(3);
  }

  function handleCreateFilter(filter: Filter) {
    setSelectedFilter(filter);
  }

  const icons = [
    { icon: FiUsers, label: 'Clientes' },
    { icon: FiBarChart2, label: 'RFM' },
    { icon: FaWhatsapp, label: 'Campanhas' },
    { icon: FiTag, label: 'Tags' },
    { icon: TbZoomMoney, label: 'Compras' },
    { icon: FiShoppingCart, label: 'Produtos' },
    { icon: AiOutlineTable, label: 'Colunas Customizadass' },
  ];

  if (!isOpen) {
    return (
      <Flex direction="column" align="center" gap={6} paddingY={6}>
        {icons.map(({ icon, label }, index) => (
          <Tooltip key={index} label={label} hasArrow>
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              borderRadius="full"
              transition="all 0.3s"
              p={2}
              _hover={{
                bg: colors.primary,
                transform: 'scale(1.1)',
                '& svg': { color: 'white' },
              }}
              cursor="pointer"
            >
              <Icon as={icon} boxSize={6} color={colors.primary} />
            </Box>
          </Tooltip>
        ))}
      </Flex>
    );
  }

  return (
    <Box height="100%" overflow="hidden">
      <form onSubmit={handleSubmit(onSubmit)} style={{ height: '100%' }}>
        <Flex flexDir="column" height="100%">
          <Box
            overflowY="auto"
            flex="1"
            css={scrollbarStyles({ width: '4px' })}
            p={2}
            gap={2}
          >
            <Flex
              overflowY="scroll"
              flexDir="column"
              maxHeight="69vh"
              flexGrow={1}
              css={scrollbarStyles({ width: '4px' })}
              paddingRight="5px"
              gap={3}
            >
              <Flex gap={4} alignItems="start">
                <FormControl flex="1">
                  <ChakraFormLabel>Filtros salvos</ChakraFormLabel>
                  <InputSelect
                    placeholder="Selecione um filtro"
                    options={savedFilters.map((filter: any) => ({
                      value: filter.id,
                      label: filter.name,
                    }))}
                    value={
                      selectedFilter
                        ? [
                            {
                              value: selectedFilter.id,
                              label: selectedFilter.name,
                            },
                          ]
                        : []
                    }
                    onChange={(option) =>
                      handleChangeSelectedFilter(option.value)
                    }
                  />
                </FormControl>
              </Flex>
              <FormControl>
                <ChakraFormLabel>Ordenar por</ChakraFormLabel>
                <Select
                  size="md"
                  bg="white"
                  placeholder="Selecione"
                  {...register('sortBy')}
                >
                  <option value="createdAtDesc">Data de criação - desc</option>
                  <option value="nameAsc">Nome do cliente - asc</option>
                  <option value="totalPurchasesDesc">
                    Total em compras - desc
                  </option>
                  <option value="totalOrdersDesc">
                    Total de pedidos - desc
                  </option>
                  <option value="lastPurchaseAtDesc">
                    Data de última compra - desc
                  </option>
                  <option value="random">Aleatório</option>
                </Select>
              </FormControl>
              <Divider />
              <Accordion
                defaultIndex={accordionDefaultIndex}
                allowMultiple
                border="1px solid white"
              >
                <SectionCustomers
                  useFormReturn={useFormReturn}
                  updateSelectedValues={updateSelectedValues}
                />
                <SectionRFM selectedFilter={selectedFilter} />
                <SectionCampaigns
                  useFormReturn={useFormReturn}
                  updateSelectedValues={updateSelectedValues}
                />
                <SectionEmailCampaigns
                  useFormReturn={useFormReturn}
                  updateSelectedValues={updateSelectedValues}
                />
                <SectionTags
                  useFormReturn={useFormReturn}
                  updateSelectedValues={updateSelectedValues}
                />
                <SectionPurchases
                  useFormReturn={useFormReturn}
                  updateSelectedValues={updateSelectedValues}
                />
                <SectionProducts
                  useFormReturn={useFormReturn}
                  updateSelectedValues={updateSelectedValues}
                />
                <SectionCustomFields useFormReturn={useFormReturn} />
              </Accordion>
            </Flex>
          </Box>
          <Box bg="white" gap={4} p={4} pb={4}>
            {totalAppliedFilters > 0 ? (
              <Button
                flexGrow={1}
                width="100%"
                variant="outline"
                onClick={() => handleClearFilters()}
              >
                Limpar ({totalAppliedFilters})
              </Button>
            ) : (
              <Button type="submit" variant="primary" width="100%">
                Filtrar
              </Button>
            )}

            {totalAppliedFilters > 0 && (
              <Flex width="100%" gap={3} mt={2}>
                <Flex flex="1" gap={3}>
                  <Menu>
                    <PopoverCreateFilter
                      isOpen={createFilterPopover.isOpen}
                      onOpen={createFilterPopover.onOpen}
                      onClose={createFilterPopover.onClose}
                      searchParams={searchParams}
                      onCreateFilter={handleCreateFilter}
                      baseCopyFilter={selectedFilter?.name}
                    >
                      <Box
                        position="absolute"
                        width="0"
                        height="0"
                        visibility="hidden"
                        zIndex={-1}
                      />
                    </PopoverCreateFilter>

                    <MenuButton
                      as={Button}
                      variant="outline"
                      size="md"
                      p={5}
                      rightIcon={<ChevronDownIcon />}
                    >
                      Ações
                    </MenuButton>

                    <MenuList>
                      {selectedFilter ? (
                        <>
                          <MenuItem
                            onClick={() => {
                              actionsPopover.onClose();
                              handleClickUpdateFilter();
                            }}
                          >
                            Atualizar
                          </MenuItem>
                          <MenuItem onClick={createFilterPopover.onOpen}>
                            Duplicar filtro
                          </MenuItem>
                        </>
                      ) : (
                        <MenuItem onClick={createFilterPopover.onOpen}>
                          Salvar Filtro
                        </MenuItem>
                      )}
                    </MenuList>
                  </Menu>

                  <Flex flex="1">
                    <Button type="submit" flex="1" variant="primary" p={5}>
                      Filtrar
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
            )}
          </Box>
        </Flex>
      </form>
    </Box>
  );
}

export default FilterSidebar;
