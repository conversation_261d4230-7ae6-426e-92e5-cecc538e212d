import {
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
} from '@chakra-ui/react';
import React, { ReactNode, cloneElement } from 'react';
import { colors } from '../../../../../constants/colors';

interface AccordionItemLayoutProps {
  icon: ReactNode;
  children: ReactNode;
  title: string;
}

const AccordionItemLayout = ({
  icon,
  children,
  title,
}: AccordionItemLayoutProps) => {
  return (
    <AccordionItem>
      <h2>
        <AccordionButton paddingLeft={2} fontWeight="sm" height="50px" gap={2}>
          {cloneElement(icon as React.ReactElement, { color: colors.primary })}
          <Box as="span" flex="1" textAlign="left">
            {title}
          </Box>
          <AccordionIcon />
        </AccordionButton>
      </h2>
      <AccordionPanel
        pb={4}
        bgColor="white"
        display="flex"
        flexDir="column"
        gap={5}
      >
        {children}
      </AccordionPanel>
    </AccordionItem>
  );
};

export default AccordionItemLayout;
