import { Text, Link, VStack, Divider } from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';
import { AiOutlineTable } from 'react-icons/ai';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { CompaniesService } from '../../../../../services/companies.service';
import {
  CompanyDefinedFieldDataTypeEnum,
  CompanyDefinedFieldTableEnum,
} from '../../../../../types/CompanyDefinedField';
import AccordionItemLayout from '../AccordionItemLayout';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../../constants/app-paths';
import CustomFieldRow from './components/CustomFieldRow';

interface SectionCustomFieldsProps {
  useFormReturn: UseFormReturn<any>;
}

const SectionCustomFields = ({ useFormReturn }: SectionCustomFieldsProps) => {
  const { setValue, control } = useFormReturn;

  const watchedCustomFieldIds = useWatch({
    name: [
      'customFieldId1',
      'customFieldId2',
      'customFieldId3',
      'customFieldId4',
    ],
    control,
  });

  const [fieldValuesList, setFieldValuesList] = useState<
    { value: string; label: string }[][]
  >([[], [], [], []]);

  const { data: companyDefinedFields } = useQuery(
    apiRoutes.listCompanyDefinedFields(CompanyDefinedFieldTableEnum.CUSTOMERS),
    async () => {
      const { data } = await CompaniesService.listCompanyDefinedFields(
        CompanyDefinedFieldTableEnum.CUSTOMERS,
      );
      return data;
    },
  );

  useEffect(() => {
    const fetchAllFieldValues = async () => {
      const allFieldValues: { value: string; label: string }[][] = [];
      for (let i = 0; i < watchedCustomFieldIds.length; i++) {
        const selectedFieldId = watchedCustomFieldIds[i];

        if (!selectedFieldId) {
          allFieldValues[i] = [];
          setFieldValuesList(allFieldValues);
          return;
        }

        const field = companyDefinedFields?.find(
          (field) => field.id === selectedFieldId,
        );

        if (!field) {
          allFieldValues[i] = [];
          setFieldValuesList(allFieldValues);
          return;
        }

        const { data } = await CompaniesService.listValuesInCompanyDefinedField(
          field.name,
        );
        allFieldValues[i] = data.map((value: string) => ({
          value,
          label: value,
        }));
      }
      setFieldValuesList(allFieldValues);
    };

    if (companyDefinedFields) {
      fetchAllFieldValues();
    }
  }, [watchedCustomFieldIds, companyDefinedFields]);

  const navigate = useNavigate();

  const isDisabled = !companyDefinedFields || companyDefinedFields.length === 0;

  const handleCreateCustomFieldValue = (index: number, inputValue: string) => {
    const selectedField = companyDefinedFields?.find(
      (field) => field.id === watchedCustomFieldIds[index],
    );
    if (selectedField?.dataType !== CompanyDefinedFieldDataTypeEnum.NUMBER) {
      setValue(`customFieldValue${index + 1}`, inputValue, {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  const handleCreateNumberValue = (index: number, inputValue: string) => {
    const numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      setValue(`customFieldValue${index + 1}`, numValue, {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  return (
    <AccordionItemLayout
      title="Colunas customizadas"
      icon={<AiOutlineTable size="18px" />}
    >
      <VStack spacing={4} align="stretch">
        {isDisabled && (
          <Text fontSize="sm" color="gray.500" mt={2}>
            Nenhuma coluna customizada configurada.
            <Link
              color="blue.500"
              onClick={() => navigate(appPaths.settings.customColumns())}
              ml={1}
            >
              Configurar colunas
            </Link>
          </Text>
        )}

        {[...Array(4)].map((_, index) => (
          <>
            <CustomFieldRow
              index={index}
              useFormReturn={useFormReturn}
              companyDefinedFields={companyDefinedFields || []}
              fieldValues={fieldValuesList[index] || []}
              isDisabled={isDisabled}
              onCreateCustomFieldValue={handleCreateCustomFieldValue}
              onCreateNumberValue={handleCreateNumberValue}
            />
            {index < 3 && <Divider />}
          </>
        ))}
      </VStack>
    </AccordionItemLayout>
  );
};

export default SectionCustomFields;
