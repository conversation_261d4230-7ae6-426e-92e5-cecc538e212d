import { FormControl, Switch, useDisclosure } from '@chakra-ui/react';
import { useEffect } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { MdOutlineEmail } from 'react-icons/md';
import { useQuery } from 'react-query';
import { useSelector } from 'react-redux';
import AlertDialogBase from '../../../../../components/AlertDialog';
import FormLabel from '../../../../../components/FormLabel';
import InputNumber from '../../../../../components/InputNumber';
import InputSelect from '../../../../../components/InputSelect';
import { apiRoutes } from '../../../../../constants/api-routes';
import { useCustomerSearchParams } from '../../../../../hooks/useCustomerSearchParams';
import { EmailTemplatesService } from '../../../../../services/email-templates.service';
import { RootState } from '../../../../../state/store';
import { CustomerFiltersEnum } from '../../../../../types/CustomerFiltersEnum';
import AccordionItemLayout from '../AccordionItemLayout';
import { selectEmailEngagementActionOptions } from '../selectEngamentActionOptions';

interface SectionEmailCampaignProps {
  useFormReturn: UseFormReturn<any>;
  updateSelectedValues: (args: any) => void;
}

const SectionEmailCampaigns = ({
  useFormReturn,
  updateSelectedValues,
}: SectionEmailCampaignProps) => {
  const { register, control, watch, setValue } = useFormReturn;
  const { selectedTemplate, selectedEmailTemplate } = useSelector(
    (state: RootState) => state.campaignCreation,
  );
  const {
    selectedEngagementEmailTemplateIds,
    selectedEmailEngagementActionTypes,
    isScheduledEmailCampaignsVisible,
    excludedEmailTemplateIds,
  } = useCustomerSearchParams();
  const isCreatingCampaign = !!selectedTemplate || !!selectedEmailTemplate;
  const { data: emailTemplates = [] } = useQuery(
    apiRoutes.listEmailTemplates(),
    async () => {
      const { data } = await EmailTemplatesService.listEmailTemplates();
      return data;
    },
  );

  const watchSelectedEngagementEmailTemplateId: string[] = watch(
    CustomerFiltersEnum.SELECTED_ENGAGEMENT_EMAIL_TEMPLATE_IDS,
  );
  const removeExcludedEmailTemplateIdsAlert = useDisclosure();

  useEffect(() => {
    setValue(
      'isScheduledEmailCampaignsVisible',
      isScheduledEmailCampaignsVisible === 'true',
    );
    updateSelectedValues({
      selectedValues: selectedEmailEngagementActionTypes,
      sourceData: selectEmailEngagementActionOptions,
      valueToSet: 'selectedEmailEngagementActionTypes',
      optionValue: 'value',
      optionLabel: 'label',
    });
    updateSelectedValues({
      selectedValues: selectedEngagementEmailTemplateIds,
      sourceData: emailTemplates,
      valueToSet: 'selectedEngagementEmailTemplateIds',
      optionValue: 'id',
      optionLabel: 'name',
    });
    updateSelectedValues({
      selectedValues: excludedEmailTemplateIds,
      sourceData: emailTemplates,
      valueToSet: 'excludedEmailTemplateIds',
      optionValue: 'id',
      optionLabel: 'name',
    });
  }, [
    isScheduledEmailCampaignsVisible,
    selectedEmailEngagementActionTypes,
    selectedEngagementEmailTemplateIds,
    setValue,
    emailTemplates,
    updateSelectedValues,
    excludedEmailTemplateIds,
  ]);

  function getEmailTemplateOptions() {
    return emailTemplates.map((emailTemplate) => ({
      value: emailTemplate.id as string,
      label: emailTemplate.name as string,
    }));
  }

  function getEngamentEmailActionOptions() {
    return selectEmailEngagementActionOptions.map((option) => ({
      value: option.value,
      label: option.label,
    }));
  }

  return (
    <AccordionItemLayout title="Emails" icon={<MdOutlineEmail size="18px" />}>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Ocultar clientes que receberam qualquer campanhas nos últimos X dias"
        >
          Ocultar quem recebeu campanha nos últimos
        </FormLabel>
        <Controller
          name="minDaysSinceLastEmailCampaign"
          control={control}
          render={({ field }) => {
            return (
              <InputNumber
                value={field.value}
                onChange={(value) => field.onChange(value)}
                size="sm"
                rightAddon="dias"
              />
            );
          }}
        />
      </FormControl>
      {isCreatingCampaign && (
        <FormControl>
          <FormLabel
            size="sm"
            tooltip="Mostrar clientes que possuem campanhas agendadas"
          >
            Mostrar clientes com campanhas agendadas
          </FormLabel>
          <Controller
            control={control}
            name="isScheduledEmailCampaignsVisible"
            render={({ field: { onChange, onBlur, value } }) => (
              <Switch
                onChange={(e) => {
                  onChange(e.target.checked);
                }}
                onBlur={onBlur}
                isChecked={value}
              />
            )}
          />
        </FormControl>
      )}
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Clientes que receberam o template selecionado"
        >
          Template enviado
        </FormLabel>
        <Controller
          name="selectedEngagementEmailTemplateIds"
          control={control}
          render={({ field }) => (
            <InputSelect
              placeholder="Selecione..."
              options={getEmailTemplateOptions()}
              isMulti
              value={field.value}
              onChange={(value) => {
                field.onChange(value);
                if (value.length === 0) {
                  setValue('selectedEmailEngagementActionTypes', []);
                }
              }}
            />
          )}
        />
      </FormControl>
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Clientes que realizaram uma ação específica para o template selecionado"
        >
          Ação realizada
        </FormLabel>
        <Controller
          name="selectedEmailEngagementActionTypes"
          control={control}
          render={({ field }) => (
            <InputSelect
              placeholder="Qualquer"
              options={getEngamentEmailActionOptions()}
              isMulti
              disabled={watchSelectedEngagementEmailTemplateId.length === 0}
              value={field.value}
              onChange={(value) => {
                field.onChange(value);
              }}
            />
          )}
        />
      </FormControl>
      {isCreatingCampaign && (
        <FormControl>
          <FormLabel
            size="sm"
            tooltip="Ocultar clientes que receberam o template selecionado"
          >
            Ocultar clientes que reberam o template
          </FormLabel>
          <Controller
            name="excludedEmailTemplateIds"
            control={control}
            render={({ field }) => (
              <InputSelect
                placeholder="Selecione..."
                options={getEmailTemplateOptions()}
                isMulti
                value={field.value}
                onChange={(value) => {
                  if (value.length === 0) {
                    removeExcludedEmailTemplateIdsAlert.onOpen();
                    setValue('excludedEmailTemplateIds', []);
                  } else {
                    field.onChange(value);
                  }
                }}
              />
            )}
          />
        </FormControl>
      )}
      <AlertDialogBase
        isOpen={removeExcludedEmailTemplateIdsAlert.isOpen}
        onClose={removeExcludedEmailTemplateIdsAlert.onClose}
        title="Tem certeza disso?"
        onConfirm={() => {
          setValue('excludedEmailTemplateIds', []);
          removeExcludedEmailTemplateIdsAlert.onClose();
        }}
      >
        Ao remover o filtro "Ocultar clientes que reberam o template" você
        poderá impactar clientes que já receberam essa mensagem
      </AlertDialogBase>
    </AccordionItemLayout>
  );
};

export default SectionEmailCampaigns;
