import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Text,
  Textarea,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { colors } from '../../../../constants/colors';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import CompanyLogoInput from './components/CompanyLogoInput';
import {
  CompaniesService,
  UpdateGupshupProfileDto,
} from '../../../../services/companies.service';
import LoadingScreen from '../../../../components/LoadingScreen';
import { GupshupProfileVertical } from '../../../../types/GupshupProfile';
import { companyCategoriesOptions } from './constants';

const DEFAULT_COMPANY_PROFILE_VERTICAL: GupshupProfileVertical = 'OTHER';

interface FormData {
  category?: string;
  description?: string;
  name: string;
  phoneNumber?: string;
  website?: string;
}

const schema = yup
  .object({
    name: yup.string(),
    description: yup
      .string()
      .required('Descrição é um campo obrigatório')
      .min(1, 'Descrição mínima de 1 caracteres')
      .max(250, 'Descrição máxima de 250 caracteres'),
    website: yup.string().url('URL inválida'),
    category: yup
      .string()
      .required('Categoria é um campo obrigatório')
      .oneOf(companyCategoriesOptions.map((option) => option.value)),
    phoneNumber: yup.string(),
  })
  .required();

const GeneralSettingsPage = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  const { handleSubmit, control, setValue } = useForm<FormData>({
    resolver: yupResolver(schema),
  });

  const {
    data: gupshupProfileData,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: apiRoutes.getCompanyGupshupProfile(),
    queryFn: async () => {
      const { data } = await CompaniesService.getCompanyGupshupProfile();
      return data;
    },
    onSuccess: (data) => {
      setValue('name', data.name);
      setValue('description', data.description);
      setValue('website', data.website);
      setValue('category', data.vertical || DEFAULT_COMPANY_PROFILE_VERTICAL);
      setValue('phoneNumber', data.contactNumber);
    },
  });

  const { mutate: updateGupshupProfile, isLoading: isUpdatingGupshupProfile } =
    useMutation({
      mutationFn: async (data: UpdateGupshupProfileDto) =>
        CompaniesService.updateGupshupProfile(data),
      onSuccess: () => {
        toast({
          title: 'Perfil da empresa atualizado com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    });

  const {
    mutate: updateGupshupProfilePhoto,
    isLoading: isUpdatingGupshupProfilePhoto,
  } = useMutation({
    mutationFn: async (file: File) =>
      CompaniesService.updateGupshupProfilePhoto(file),
    onSuccess: () => {
      toast({
        title: 'Logo da empresa atualizada com sucesso',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      queryClient.invalidateQueries(apiRoutes.getCompanyGupshupProfile());
    },
  });

  async function onSubmit(data: FormData) {
    updateGupshupProfile({
      vertical: data.category,
      description: data.description,
      website: data.website,
    });
  }

  return (
    <LoadingScreen isLoading={isLoading || isFetching}>
      <Box
        bg="white"
        borderRadius="md"
        border="1px solid"
        borderColor="gray.200"
        p={8}
      >
        <Heading size="lg" mb={1}>
          Dados cadastrais
        </Heading>
        <Text color="gray.500" mb={8}>
          Informações básicas sobre sua empresa
        </Text>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={6}>
            <FormControl isDisabled>
              <FormLabel>Nome da empresa</FormLabel>
              <Controller
                name="name"
                control={control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      {...field}
                      isInvalid={!!fieldState.error}
                      placeholder="Nome da empresa"
                    />
                    {fieldState.error && (
                      <Text color={colors.danger} fontSize="xs">
                        {fieldState.error.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>Logo da empresa</FormLabel>
              <CompanyLogoInput
                onImageChange={updateGupshupProfilePhoto}
                initialImageUrl={gupshupProfileData?.photo}
                isLoading={
                  isLoading || isFetching || isUpdatingGupshupProfilePhoto
                }
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>Descrição da empresa</FormLabel>
              <Controller
                name="description"
                control={control}
                render={({ field, fieldState }) => (
                  <>
                    <Textarea
                      {...field}
                      isInvalid={!!fieldState.error}
                      placeholder="Descreva brevemente sua empresa"
                    />
                    {fieldState.error && (
                      <Text color={colors.danger} fontSize="xs">
                        {fieldState.error.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Site da empresa</FormLabel>
              <Controller
                name="website"
                control={control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      {...field}
                      isInvalid={!!fieldState.error}
                      placeholder="Site da empresa, ex: https://www.seusite.com.br"
                    />
                    {fieldState.error && (
                      <Text color={colors.danger} fontSize="xs">
                        {fieldState.error.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Categoria da empresa</FormLabel>
              <Controller
                name="category"
                control={control}
                render={({ field, fieldState }) => (
                  <>
                    <Select
                      {...field}
                      isInvalid={!!fieldState.error}
                      placeholder="Selecione uma categoria"
                    >
                      {companyCategoriesOptions.map((category) => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </Select>

                    {fieldState.error && (
                      <Text color={colors.danger} fontSize="xs">
                        {fieldState.error.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </FormControl>

            <FormControl isDisabled>
              <FormLabel>Número WhatsApp cadastrado</FormLabel>
              <Controller
                name="phoneNumber"
                control={control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      {...field}
                      isInvalid={!!fieldState.error}
                      disabled
                      placeholder="+55 (11) 9 9999-9999"
                    />
                    {fieldState.error && (
                      <Text color={colors.danger} fontSize="xs">
                        {fieldState.error.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </FormControl>

            <Divider />

            <Flex justify="flex-end">
              <Button
                width="30%"
                isLoading={isUpdatingGupshupProfile}
                color={colors.white}
                bgColor={colors.primary}
                type="submit"
              >
                Salvar
              </Button>
            </Flex>
          </Stack>
        </form>
      </Box>
    </LoadingScreen>
  );
};

export default GeneralSettingsPage;
