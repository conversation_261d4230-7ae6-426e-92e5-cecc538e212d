import { Box, Image, Text, Spinner, Flex } from '@chakra-ui/react';
import { LuUpload } from 'react-icons/lu';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useDropzone } from 'react-dropzone';
import { useToast } from '@chakra-ui/react';
import useFileValidation from '../../../../../../hooks/useFileValidation';
import { colors } from '../../../../../../constants/colors';
import { FileValidationUtils } from '../../../../../../utils/file-validation.utils';

const MAX_SIZE_MB = FileValidationUtils.limitByFileType('image/png', 'MB');
const MAX_FILE_NAME_LENGTH = 60;
const ACCEPTED_FILE_TYPES = ['.jpeg', '.jpg', '.png'];

interface ImageUploadProps {
  onImageChange?: (file: File) => void;
  initialImageUrl?: string;
  isLoading?: boolean;
  error?: string;
}

const CompanyLogoInput = ({
  onImageChange,
  initialImageUrl,
  isLoading,
  error,
}: ImageUploadProps) => {
  const [preview, setPreview] = useState<string | null>(
    initialImageUrl || null,
  );
  const [file, setFile] = useState<File | null>(null);
  const { validateFile } = useFileValidation();
  const toast = useToast();

  useEffect(() => {
    return () => {
      if (preview && preview.startsWith('blob:')) {
        URL.revokeObjectURL(preview);
      }
      setPreview(null);
      setFile(null);
    };
  }, []);

  const onDrop = useCallback(
    (acceptedFiles: Array<File>) => {
      if (isLoading) return;
      const selectedFile = acceptedFiles?.[0];

      if (!validateFile(selectedFile)) return;

      try {
        onImageChange?.(selectedFile);
        setFile(selectedFile);
        setPreview(URL.createObjectURL(selectedFile));
      } catch (error) {
        console.error(error);
      }
    },
    [validateFile, onImageChange, toast],
  );

  const {
    getRootProps,
    getInputProps,
    open: openFileInput,
  } = useDropzone({
    onDrop,
    accept: {
      'image/*': ACCEPTED_FILE_TYPES,
    },
    maxFiles: 1,
    multiple: false,
    disabled: isLoading,
  });

  const handleClickSelectFile = () => {
    if (isLoading) return;
    openFileInput();
  };

  const trimmedFileName = useMemo(() => {
    if (!file?.name && !initialImageUrl) return null;
    const name = file?.name || initialImageUrl;

    if (!name || !name?.length) return null;

    if (name?.length && name.length > MAX_FILE_NAME_LENGTH) {
      return name.slice(0, MAX_FILE_NAME_LENGTH) + '...';
    }

    return name;
  }, [file?.name, initialImageUrl]);

  return (
    <Flex direction="row" gap={4} align="flex-start">
      <Box
        {...getRootProps()}
        w="96px"
        h="96px"
        border="1px dashed"
        borderColor="gray.200"
        borderRadius="md"
        display="flex"
        alignItems="center"
        justifyContent="center"
        bg="gray.50"
        cursor={isLoading ? 'not-allowed' : 'pointer'}
        _hover={{ bg: 'gray.100' }}
        position="relative"
        overflow="hidden"
      >
        <input {...getInputProps()} />

        {isLoading && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            display="flex"
            alignItems="center"
            justifyContent="center"
            bg="rgba(255, 255, 255, 0.8)"
            zIndex={2}
          >
            <Spinner size="md" />
          </Box>
        )}

        {preview ? (
          <Image
            src={preview}
            alt="Image preview"
            width="100%"
            height="100%"
            objectFit="cover"
            borderRadius="md"
            zIndex={1}
          />
        ) : (
          <LuUpload size={24} color={colors.middleGrey} />
        )}
      </Box>

      <Box flex="1">
        <Box
          border="1px solid"
          borderColor="gray.200"
          borderRadius="md"
          px={4}
          py={2}
          minH="42px"
          display="flex"
          alignItems="center"
        >
          <Text
            fontWeight="medium"
            fontSize="sm"
            onClick={handleClickSelectFile}
            cursor={isLoading ? 'not-allowed' : 'pointer'}
          >
            {trimmedFileName || 'Escolha um arquivo'}
          </Text>
        </Box>

        <Text fontSize="sm" color="gray.500" mt={1}>
          Apenas arquivos PNG ou JPG são suportados. Tamanho máximo de{' '}
          {MAX_SIZE_MB} MB.
        </Text>

        {error && (
          <Text color={colors.danger} fontSize="xs" mt={1}>
            {error}
          </Text>
        )}
      </Box>
    </Flex>
  );
};

export default CompanyLogoInput;
