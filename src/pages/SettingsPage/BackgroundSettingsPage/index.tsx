import { <PERSON>, Tab, <PERSON>b<PERSON>ist, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import { colors } from '../../../constants/colors';
import GeneralSettingsPage from './GeneralSettingsPage';
import AccountSettingsPage from './AccountSettingsPage';

const BackgroundSettingsPage = () => {
  return (
    <Box>
      <Tabs variant="unstyled">
        <TabList borderBottom="1px solid" borderColor="gray.200" mb={4}>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Dados Cadastrais
          </Tab>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Segurança
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0}>
            <GeneralSettingsPage />
          </TabPanel>

          <TabPanel px={0}>
            <AccountSettingsPage />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default BackgroundSettingsPage;
