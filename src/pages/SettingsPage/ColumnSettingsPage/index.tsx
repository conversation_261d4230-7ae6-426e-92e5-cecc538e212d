import { Box, Button, Flex, Heading } from '@chakra-ui/react';
import { useCrudCompanyDefinedFieldModal } from '../../../hooks/useCrudCompanyDefinedFieldModal';
import TableColumnSettings from './components/TableColumnSettings';

const ColumnSettingPage = () => {
  const { openCreateModal } = useCrudCompanyDefinedFieldModal();

  return (
    <Box>
      <Flex width="100%" justifyContent="space-between">
        <Heading>Colunas Customizadas</Heading>
        <Button variant="primary" onClick={openCreateModal}>
          + <PERSON><PERSON>r Campo de Cliente
        </Button>
      </Flex>
      <Box mt={6}>
        <TableColumnSettings />
      </Box>
    </Box>
  );
};

export default ColumnSettingPage;
