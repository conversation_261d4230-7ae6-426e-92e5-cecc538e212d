import { Box, Button, Flex, Heading } from '@chakra-ui/react';
import React, { useState } from 'react';
import { FaTrashAlt } from 'react-icons/fa';
import TableTagSettings from './components/TableTagSettings';
import { useCrudTagModal } from '../../../hooks/useCrudTagModal';

const TagSettingsPage = () => {
  const { openCreateModal, openDeleteAlert } = useCrudTagModal();
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);

  const handleTagSelectionChange = (ids: string[]) => {
    setSelectedTagIds(ids);
  };

  const handleDeleteSelected = () => {
    if (selectedTagIds.length > 0) {
      openDeleteAlert(selectedTagIds.join(','));
      setSelectedTagIds([]);
    }
  };

  return (
    <Box>
      <Flex width="100%" justifyContent="space-between">
        <Heading>Tags</Heading>
        <Flex>
          {selectedTagIds.length > 0 && (
            <Button
              leftIcon={<FaTrashAlt />}
              colorScheme="red"
              variant="outline"
              onClick={handleDeleteSelected}
              mr={4}
            >
              Deletar selecionadas ({selectedTagIds.length})
            </Button>
          )}
          <Button variant="primary" onClick={openCreateModal} minWidth={100}>
            + Tag
          </Button>
        </Flex>
      </Flex>
      <Box mt={6}>
        <TableTagSettings onTagSelectionChange={handleTagSelectionChange} />
      </Box>
    </Box>
  );
};

export default TagSettingsPage;
