import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveIrrobaCredentialsDto,
  SaveIntegrationsConfigDto,
  IntegrationsConfigDto,
} from '../../../../services/integrations.service';
import { useState } from 'react';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import IntegrationsTable, {
  IntegrationsTableColumn,
} from '../../../../components/IntegrationsTable';

const createSchema = (isEditing: boolean) =>
  yup.object({
    description: yup.string().default('').required('A descrição é obrigatória'),
    clientUsername: yup
      .string()
      .default('')
      .required('O nome de usuário/e-mail é obrigatório'),
    clientPassword: isEditing
      ? yup.string().default('')
      : yup.string().default('').required('A senha é obrigatória'),
    isOrderActive: yup.boolean().default(true),
    isAbandonedCartActive: yup.boolean().default(true),
    isActive: yup.boolean().default(true),
  });

const IrrobaPage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [editingIntegrationId, setEditingIntegrationId] = useState<
    string | null
  >(null);

  // Configuração das colunas da tabela - para Irroba, mostramos apenas pedidos
  const tableColumns: IntegrationsTableColumn[] = [
    { key: 'isOrderActive', label: 'Sinc. de Pedidos', show: true },
    { key: 'isProductActive', label: 'Sinc. de Produtos', show: false },
    {
      key: 'isAbandonedCartActive',
      label: 'Sinc. de Carrinhos Abandonados',
      show: true,
    },
  ];
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
  } = useForm({
    resolver: yupResolver(createSchema(!!editingIntegrationId)),
  });

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.irroba),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.irroba,
        );
        return data;
      },
    );

  const saveIrrobaCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveIrrobaCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const clientPasswordValue = getValues('clientPassword');
    const config: SaveIrrobaCredentialsDto = {
      clientUsername: getValues('clientUsername') || '',
      clientPassword: clientPasswordValue || '',
    };

    const data = {
      id: editingIntegrationId || undefined,
      isOrderActive: getValues('isOrderActive'),
      source: SourceIntegration.irroba,
      description: getValues('description'),
      config,
      isActive: getValues('isActive'),
      isAbandonedCartActive: getValues('isAbandonedCartActive'),
      isProductActive: false,
      companyId: '',
    };
    await saveIrrobaCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
    setEditingIntegrationId(null);
  }

  async function onSubmit() {
    handleSaveData();
  }

  const handleEditIntegration = (integration: IntegrationsConfigDto) => {
    setShowForm(true);
    setValue('description', integration.description);
    setValue('isOrderActive', integration.isOrderActive);
    setValue('isAbandonedCartActive', integration.isAbandonedCartActive);
    setValue('isActive', integration.isActive);

    const config = integration.config as SaveIrrobaCredentialsDto;
    if (config) {
      setValue('clientUsername', config.clientUsername || '');
      setValue('clientPassword', ''); // Não carregar a senha por segurança
    }
    setEditingIntegrationId(integration.id);
  };

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração Irroba
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={() => setShowForm(true)}
            isLoading={saveIrrobaCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {editingIntegrationId
              ? 'Editar Integração'
              : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Nome de Usuário</FormLabel>
                <Input
                  placeholder="Nome de usuário"
                  {...register('clientUsername')}
                  isInvalid={!!errors.clientUsername?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.clientUsername?.message}
                </Text>
              </FormControl>

              <FormControl isRequired={!editingIntegrationId}>
                <FormLabel>Senha</FormLabel>
                <Input
                  type="password"
                  placeholder={
                    editingIntegrationId
                      ? 'Deixe em branco para manter a senha atual'
                      : 'Senha'
                  }
                  {...register('clientPassword')}
                  isInvalid={!!errors.clientPassword?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.clientPassword?.message}
                </Text>
                {editingIntegrationId && (
                  <Text fontSize="xs" color="gray.500">
                    Deixe em branco para manter a senha atual
                  </Text>
                )}
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Pedidos Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>
              <FormControl>
                <Controller
                  name="isAbandonedCartActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Sincronização de Carrinhos Abandonados Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    >
                      Integração Ativa
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end" gap={2}>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowForm(false);
                    setEditingIntegrationId(null);
                    reset();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  colorScheme="blue"
                  isLoading={saveIrrobaCredentials.isLoading}
                >
                  Salvar
                </Button>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <IntegrationsTable
        integrationsConfig={integrationsConfig}
        sourceIntegration={SourceIntegration.irroba}
        columns={tableColumns}
        onEdit={handleEditIntegration}
        isLoading={saveIrrobaCredentials.isLoading}
      />
    </Box>
  );
};

export default IrrobaPage;
