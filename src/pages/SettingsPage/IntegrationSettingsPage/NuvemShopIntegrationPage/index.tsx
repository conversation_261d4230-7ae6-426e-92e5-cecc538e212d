import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveNuvemShopCredentialsDto,
} from '../../../../services/integrations.service';
import AlertDialogBase from '../../../../components/AlertDialog';
import { useEffect } from 'react';

const schema = yup.object({
  nuvemShopApiUrl: yup
    .string()
    .default('')
    .url('A url da sua loja deve ser válida')
    .required('A url da sua loja é obrigatória'),
  isNuvemShopOrderActive: yup.boolean().default(false),
});

const NuvemShopIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    if (code) {
      authorizeNuvemShopApp.mutate(code, {
        onSuccess: () => {
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname,
          );
        },
      });
    }
  }, []);

  const {
    onOpen: onOpenSaveConfirmation,
    onClose: onCloseSaveConfirmation,
    isOpen: isOpenSaveConfirmation,
  } = useDisclosure();
  const { data: nuvemShopConfig, refetch: refetchNuvemShopConfig } = useQuery(
    apiRoutes.getNuvemShopConfig(),
    async () => {
      const { data } = await IntegrationsService.getNuvemShopConfig();
      return data;
    },
    {
      onSuccess: (data) => {
        setValue('nuvemShopApiUrl', data.nuvemShopApiUrl || '');
        setValue('isNuvemShopOrderActive', data.isNuvemShopOrderActive || '');
      },
    },
  );

  const saveNuvemShopCredentials = useMutation(
    (data: SaveNuvemShopCredentialsDto) =>
      IntegrationsService.saveNuvemShopCredentials(data),
    {
      onSuccess: () => {
        refetchNuvemShopConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  const authorizeNuvemShopApp = useMutation(
    (data: string) => IntegrationsService.authorizeNuvemShopApp(data),
    {
      onSuccess: () => {
        refetchNuvemShopConfig();
        toast({
          title: 'Aplicativo autorizado com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
      onError: () => {
        toast({
          title: 'Erro ao autorizar aplicativo',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const data = {
      nuvemShopApiUrl: getValues('nuvemShopApiUrl'),
      isNuvemShopOrderActive: getValues('isNuvemShopOrderActive'),
    };
    await saveNuvemShopCredentials.mutateAsync(data);
  }

  async function onSubmit() {
    if (getValues('nuvemShopApiUrl') != nuvemShopConfig?.nuvemShopApiUrl) {
      onOpenSaveConfirmation();
      return;
    }
    handleSaveData();
  }

  function handleAuthorizeReviApp() {
    if (!nuvemShopConfig?.nuvemShopAuthUrl) return;
    const url = nuvemShopConfig?.nuvemShopAuthUrl;
    window.location.href = url;
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>Url da Loja</FormLabel>
            <Input
              placeholder="Url da sua loja"
              {...register('nuvemShopApiUrl')}
              isInvalid={errors.nuvemShopApiUrl?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.nuvemShopApiUrl?.message}
            </Text>
          </FormControl>
          <FormControl>
            <Controller
              name="isNuvemShopOrderActive"
              control={control}
              defaultValue={false}
              render={({ field }) => (
                <Checkbox
                  isChecked={field.value}
                  onChange={(e) =>
                    setValue('isNuvemShopOrderActive', e.target.checked)
                  }
                >
                  Ativar Sincronização de Pedidos?
                </Checkbox>
              )}
            />
          </FormControl>

          <Flex justify="flex-end">
            <Box
              flexDirection="row"
              gap="14px"
              display="flex"
              alignItems="center"
            >
              {nuvemShopConfig?.nuvemShopApiUrl &&
                nuvemShopConfig?.needsToAuthorizeApp && (
                  <Button
                    colorScheme="green"
                    width="fit-content"
                    onClick={handleAuthorizeReviApp}
                    variant="outline"
                    isLoading={
                      saveNuvemShopCredentials.isLoading ||
                      authorizeNuvemShopApp.isLoading
                    }
                  >
                    Autorizar ReviApp na Loja
                  </Button>
                )}
              <Button
                type="submit"
                color={colors.white}
                bgColor={colors.primary}
                width="fit-content"
                isLoading={saveNuvemShopCredentials.isLoading}
              >
                Salvar
              </Button>
            </Box>
          </Flex>
        </Stack>
      </form>

      <AlertDialogBase
        isOpen={isOpenSaveConfirmation}
        onClose={onCloseSaveConfirmation}
        title="Confirmação"
        onConfirm={() => {
          handleSaveData();
          onCloseSaveConfirmation();
        }}
      >
        Após esta ação será necessário autorizar novamente o aplicativo em sua
        loja; deseja continuar?
      </AlertDialogBase>
    </Box>
  );
};

export default NuvemShopIntegrationPage;
