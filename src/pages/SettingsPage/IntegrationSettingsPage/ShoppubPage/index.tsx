import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import React from 'react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveShoppubCredentialsDto,
} from '../../../../services/integrations.service';

const schema = yup.object({
  shoppubAccessToken: yup.string().required('\'Api Key\' é obrigatório'),
  shoppubStoreDomain: yup
    .string()
    .required('\'<PERSON><PERSON>io da Loja\' é obrigatório')
    .matches(
      /^(?!https?:\/\/)([a-zA-Z0-9-_]+\.)+[a-zA-Z]{2,6}?$/,
      '\'Domínio da Loja\' deve ser um domínio válido e não deve incluir http ou https',
    ),
});

const ShoppubIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { data: shoppubConfig, refetch: refetchShoppubConfig } =
    useQuery(
      apiRoutes.getShoppubConfig(),
      async () => {
        const { data } = await IntegrationsService.getShoppubConfig();
        return data;
      },
      {
        onSuccess: (data) => {
          setValue(
            'shoppubStoreDomain',
            data.shoppubStoreDomain || '',
          );
        },
      },
    );

  const saveShoppubCredentials = useMutation(
    (data: SaveShoppubCredentialsDto) =>
      IntegrationsService.saveShoppubCredentials(data),
    {
      onSuccess: () => {
        refetchShoppubConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    await saveShoppubCredentials.mutateAsync(data);
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>Domínio da Loja</FormLabel>
            <Input
              placeholder="Ex.: loja.exemplo.com"
              {...register('shoppubStoreDomain')}
              isInvalid={errors.shoppubStoreDomain?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.shoppubStoreDomain?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Api Key</FormLabel>
            <Input
              placeholder="******************"
              type="password"
              {...register('shoppubAccessToken')}
              isInvalid={errors.shoppubAccessToken?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.shoppubAccessToken?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              type="submit"
              color={colors.white}
              bgColor={colors.primary}
              width="fit-content"
              isLoading={saveShoppubCredentials.isLoading}
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default ShoppubIntegrationPage;
