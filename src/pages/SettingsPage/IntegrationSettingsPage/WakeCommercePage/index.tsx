import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveWakeCommerceCredentialsDto,
  IntegrationsConfigDto,
  SaveIntegrationsConfigDto,
} from '../../../../services/integrations.service';
import { useState } from 'react';
import { SourceIntegration } from '../../../../types/SourceIntegration';
import IntegrationsTable, {
  IntegrationsTableColumn,
} from '../../../../components/IntegrationsTable';
import { ViewIcon, ViewOffIcon } from '@chakra-ui/icons';
import { InputGroup, InputRightElement } from '@chakra-ui/react';

const createSchema = () =>
  yup.object({
    id: yup.string().default(''),
    description: yup.string().default('').required('A descrição é obrigatória'),
    token: yup
      .string()
      .default('')
      .when('id', {
        is: (id: string) => id == '',
        then: (schema) => schema.required('O token de acesso é obrigatório'),
        otherwise: (schema) => schema,
      }),
    isOrderActive: yup.boolean().default(true),
    isActive: yup.boolean().default(true),
  });

const WakeCommercePage = () => {
  const toast = useToast();
  const [showForm, setShowForm] = useState(false);
  const [showToken, setShowToken] = useState(false);

  const tableColumns: IntegrationsTableColumn[] = [
    { key: 'isOrderActive', label: 'Sincronização de Pedidos', show: true },
    { key: 'isProductActive', label: 'Sincronização de Produtos', show: false },
    {
      key: 'isAbandonedCartActive',
      label: 'Sinc. de Carrinhos Abandonados',
      show: false,
    },
  ];

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
    watch,
  } = useForm({
    resolver: yupResolver(createSchema()),
  });

  const watchedId = watch('id');

  const { data: integrationsConfig, refetch: refetchIntegrationsConfig } =
    useQuery(
      apiRoutes.getIntegrationsConfig(SourceIntegration.wake_commerce),
      async () => {
        const { data } = await IntegrationsService.getIntegrationsConfig(
          SourceIntegration.wake_commerce,
        );
        return data;
      },
    );

  const saveWakeCommerceCredentials = useMutation(
    (data: SaveIntegrationsConfigDto<SaveWakeCommerceCredentialsDto>) =>
      IntegrationsService.saveIntegrationConfig(data),
    {
      onSuccess: () => {
        refetchIntegrationsConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSaveData() {
    const config: SaveWakeCommerceCredentialsDto = {
      token: getValues('token') || '',
    };

    const data = {
      id: getValues('id') || undefined,
      isOrderActive: getValues('isOrderActive'),
      source: SourceIntegration.wake_commerce,
      description: getValues('description'),
      config,
      isActive: getValues('isActive'),
      isAbandonedCartActive: false,
      isProductActive: false,
      companyId: '',
    };
    await saveWakeCommerceCredentials.mutateAsync(data);

    reset();
    setShowForm(false);
  }

  async function onSubmit() {
    handleSaveData();
  }

  const handleEditIntegration = (integration: IntegrationsConfigDto) => {
    setShowForm(true);
    setValue('id', integration.id);
    setValue('description', integration.description);
    setValue('isOrderActive', integration.isOrderActive);
    setValue('isActive', integration.isActive);

    const config = integration.config as SaveWakeCommerceCredentialsDto;
    if (config) {
      setValue('token', config.token || '');
    }
  };

  return (
    <Box>
      <Heading size="md" mb={4}>
        Integração Wake Commerce
      </Heading>
      {!showForm ? (
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="green"
            onClick={() => setShowForm(true)}
            isLoading={saveWakeCommerceCredentials.isLoading}
          >
            Adicionar Nova Integração
          </Button>
        </Flex>
      ) : (
        <Box mb={6}>
          <Heading size="sm" mb={4}>
            {watchedId ? 'Editar Integração' : 'Cadastrar Nova Integração'}
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={4} mt={4}>
              <FormControl isRequired>
                <FormLabel>Descrição</FormLabel>
                <Input
                  placeholder="Descrição da integração"
                  {...register('description')}
                  isInvalid={!!errors.description?.message}
                />
                <Text color={colors.danger} fontSize="xs">
                  {errors.description?.message}
                </Text>
              </FormControl>

              <FormControl>
                <FormLabel>Token de Acesso</FormLabel>
                <InputGroup>
                  <Input
                    placeholder="Token de Acesso"
                    type={showToken ? 'text' : 'password'}
                    {...register('token')}
                    isInvalid={!!errors.token?.message}
                  />
                  <InputRightElement h="full">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowToken((v) => !v)}
                      tabIndex={-1}
                      aria-label={
                        showToken ? 'Esconder token' : 'Mostrar token'
                      }
                    >
                      {showToken ? <ViewOffIcon /> : <ViewIcon />}
                    </Button>
                  </InputRightElement>
                </InputGroup>
                <Text color={colors.danger} fontSize="xs">
                  {errors.token?.message}
                </Text>
              </FormControl>

              <FormControl>
                <Controller
                  name="isOrderActive"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) =>
                        setValue('isOrderActive', e.target.checked)
                      }
                    >
                      Sincronizar Pedidos
                    </Checkbox>
                  )}
                />
              </FormControl>

              <FormControl>
                <Controller
                  name="isActive"
                  control={control}
                  defaultValue={true}
                  render={({ field }) => (
                    <Checkbox
                      isChecked={field.value}
                      onChange={(e) => setValue('isActive', e.target.checked)}
                    >
                      Status da Sincronização
                    </Checkbox>
                  )}
                />
              </FormControl>

              <Flex justify="flex-end">
                <Box
                  flexDirection="row"
                  gap="14px"
                  display="flex"
                  alignItems="center"
                >
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowForm(false);
                      reset();
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    color={colors.white}
                    bgColor={colors.primary}
                    width="fit-content"
                    isLoading={saveWakeCommerceCredentials.isLoading}
                  >
                    Salvar
                  </Button>
                </Box>
              </Flex>
            </Stack>
          </form>
        </Box>
      )}

      <IntegrationsTable
        integrationsConfig={integrationsConfig}
        sourceIntegration={SourceIntegration.wake_commerce}
        columns={tableColumns}
        onEdit={handleEditIntegration}
        isLoading={saveWakeCommerceCredentials.isLoading}
      />
    </Box>
  );
};

export default WakeCommercePage;
