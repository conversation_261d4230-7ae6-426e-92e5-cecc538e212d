import React from 'react';
import {
  Box,
  Heading,
  VStack,
  useDisclosure,
  Grid,
  GridItem,
  Divider,
} from '@chakra-ui/react';
import { EcommerceFunctionality, EcommercePlatform } from './types/Ecommerce';
import { ECOMMERCE_PLATFORMS } from './const/ecommerce-plataform';

import CardAction from '../../../../components/CardAction';
import { SourceIntegrationLabelsRoutesAndImages } from '../../../../types/source-integration-labels-routes-and-images';
import ApiKeySettingsModal from './components/ApiKeySettingsModal';

const GoogleTagManagerIntegrationPage = () => {
  const apiKeySettingsModal = useDisclosure();
  const [selectedFunctionality, setSelectedFunctionality] =
    React.useState<EcommerceFunctionality | null>(null);
  const [selectedPlatform, setSelectedPlatform] =
    React.useState<EcommercePlatform | null>(null);

  const handleFunctionalitySelect = (
    platform: EcommercePlatform,
    functionality: EcommerceFunctionality,
  ) => {
    setSelectedPlatform(platform);
    setSelectedFunctionality(functionality);
    apiKeySettingsModal.onOpen();
  };

  const allPlatformFunctions = ECOMMERCE_PLATFORMS.flatMap((platform) =>
    platform.functionalities.map((func) => ({
      platform,
      functionality: func,
    })),
  );

  const getImageSource = (platform: EcommercePlatform) => {
    const sourceIntegration =
      SourceIntegrationLabelsRoutesAndImages[platform.source ?? 'unknown'];
    return `${process.env.PUBLIC_URL}/${sourceIntegration?.image}`;
  };

  return (
    <Box>
      <Heading mb={6} margin="auto" textAlign="center">
        Plataformas - Funcionalidades
      </Heading>
      <Divider marginY={5} />
      <VStack spacing={6} align="stretch" p={2}>
        <Grid templateColumns="repeat(4, 1fr)" gap={6}>
          {allPlatformFunctions.map((item) => (
            <GridItem key={`${item.platform.id}-${item.functionality.id}`}>
              <CardAction
                title={item.functionality.name}
                subtitle={item.platform.name}
                imgSource={getImageSource(item.platform)}
                primaryActionName="Acessar"
                onPrimaryAction={() =>
                  handleFunctionalitySelect(item.platform, item.functionality)
                }
              />
            </GridItem>
          ))}
        </Grid>

        <ApiKeySettingsModal
          isOpen={apiKeySettingsModal.isOpen}
          onClose={apiKeySettingsModal.onClose}
          selectedPlatform={selectedPlatform!}
          selectedFunctionality={selectedFunctionality!}
        />
      </VStack>
    </Box>
  );
};

export default GoogleTagManagerIntegrationPage;
