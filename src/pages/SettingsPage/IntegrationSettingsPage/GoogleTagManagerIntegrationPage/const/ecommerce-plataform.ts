import { FaStore } from 'react-icons/fa';
import { EcommercePlatform } from '../types/Ecommerce';
import { SourceIntegration } from '../../../../../types/SourceIntegration';

export const ECOMMERCE_PLATFORMS: EcommercePlatform[] = [
  {
    id: 'loja-integrada',
    name: 'Loja Integrada',
    source: SourceIntegration.loja_integrada,
    integration: SourceIntegration.google_tag_manager,
    icon: FaStore,
    functionalities: [
      {
        id: `abandoned-cart-${SourceIntegration.loja_integrada}`,
        name: 'Carrinho Abandonado',
        description: 'Rastreamento de carrinhos abandonados',
        category: 'Conversão',
        scriptTemplate: (apiKey) => `
        <!-- Marketplace Abandoned Cart GTM Script -->
        <script>
          (function(window, document, tagName, scriptSource, apiKey) {
            var firstScriptTag = document.getElementsByTagName(tagName)[0];
            var newScriptTag = document.createElement(tagName);
            newScriptTag.async = true;
            newScriptTag.src = scriptSource;
            newScriptTag.id = 'revi-loja-integrada';
            newScriptTag.setAttribute('apiKey', apiKey);
            firstScriptTag.parentNode.insertBefore(newScriptTag, firstScriptTag);
          })(window, document, 'script', 'https://revi-gtm-scripts.s3.us-east-1.amazonaws.com/loja-integrada/main.js', '${apiKey}');
        </script>
        `,
      },
    ],
  },
];
