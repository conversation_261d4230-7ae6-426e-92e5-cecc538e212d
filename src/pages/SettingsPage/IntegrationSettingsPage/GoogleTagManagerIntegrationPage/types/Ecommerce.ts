import { SourceIntegration } from '../../../../../types/Prisma';

export interface EcommerceFunctionality {
  id: string;
  name: string;
  description: string;
  category: string;
  scriptTemplate: (apiKey: string) => string;
}

export interface EcommercePlatform {
  id: string;
  name: string;
  source: SourceIntegration;
  integration: SourceIntegration;
  icon: React.ElementType;
  functionalities: EcommerceFunctionality[];
}
