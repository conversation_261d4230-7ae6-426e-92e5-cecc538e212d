import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  Box,
  VStack,
  Flex,
  Heading,
  Icon,
  Badge,
  Divider,
  Text,
  Input,
  Button,
  HStack,
  useToast,
} from '@chakra-ui/react';
import { CheckCircleIcon, CopyIcon, WarningIcon } from '@chakra-ui/icons';
import {
  EcommerceFunctionality,
  EcommercePlatform,
} from '../../types/Ecommerce';
import { colors } from '../../../../../../constants/colors';
import ScriptDisplay from '../ScriptDisplay';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../../../constants/api-routes';
import {
  ApiKeysService,
  CreateApiKeyDto,
} from '../../../../../../services/api-keys.service';
import { <PERSON><PERSON><PERSON><PERSON>, SourceIntegration } from '../../../../../../types/Prisma';
import Loading from '../../../../../../components/Loading';
import { scrollbarStyles } from '../../../../../../styles/scrollbar.styles';

interface ApiKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPlatform: EcommercePlatform;
  selectedFunctionality: EcommerceFunctionality;
}

const ApiKeySettingsModal = ({
  isOpen,
  onClose,
  selectedPlatform,
  selectedFunctionality,
}: ApiKeyModalProps) => {
  if (!selectedFunctionality || !selectedPlatform) return null;

  const toast = useToast();
  const [apiKey, setApiKey] = useState<ApiKey | null>(null);

  const { isLoading: isLoadingApiKey, isError } = useQuery(
    apiRoutes.getApiKey(selectedPlatform.integration, selectedFunctionality.id),
    async () => {
      const { data } = await ApiKeysService.getApiKey(
        selectedPlatform.integration,
        selectedFunctionality.id,
      );
      return data;
    },
    {
      onSuccess: (data) => {
        setApiKey(data);
      },
    },
  );

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Texto copiado',
      status: 'success',
      duration: 2000,
    });
  };

  const createApiKey = useMutation(
    async (createApiKeyDto: CreateApiKeyDto) => {
      const { data } = await ApiKeysService.createApiKey(createApiKeyDto);
      return data;
    },
    {
      onSuccess: (data) => {
        setApiKey(data);
      },
    },
  );

  const generateNewApiKey = useMutation(
    async (apiKeyId: string) => {
      const { data } = await ApiKeysService.generateNewApiKey(apiKeyId);
      return data;
    },
    {
      onSuccess: (data) => {
        setApiKey(data);
      },
    },
  );

  async function handleGenerateApiKey(
    integration: SourceIntegration,
    name: string,
  ) {
    await createApiKey.mutateAsync({ integration, name });
  }

  async function handleGenerateNewApiKey() {
    await generateNewApiKey.mutateAsync(apiKey!.id);
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg" isCentered>
      <ModalOverlay />
      <Loading
        isLoading={isLoadingApiKey}
        loadingMessage="Procurando API Key..."
        isError={isError}
        errorMessage="Falha ao carregar API Key"
      >
        <ModalContent minWidth="45%">
          {apiKey ? (
            <Box p={6}>
              <VStack spacing={6} align="stretch">
                <Flex justifyContent="space-between" alignItems="center">
                  <Heading size="lg" display="flex" alignItems="center">
                    {selectedPlatform?.icon && (
                      <Icon as={selectedPlatform.icon} mr={3} boxSize={8} />
                    )}
                    {selectedPlatform?.name}
                  </Heading>
                  <Badge
                    colorScheme="green"
                    fontSize="md"
                    borderRadius="full"
                    px={2}
                  >
                    <CheckCircleIcon mr={2} />
                    Configurado
                  </Badge>
                </Flex>

                <Divider />

                <Box>
                  <Heading size="md" mb={2}>
                    {selectedFunctionality.name}
                  </Heading>
                  <Text color="gray.600" mb={4}>
                    {selectedFunctionality.description}
                  </Text>
                </Box>

                <VStack spacing={4} align="stretch">
                  <Box>
                    <Text fontWeight="bold" mb={2}>
                      API Key
                    </Text>
                    <Flex>
                      <Input value={apiKey.key} isReadOnly mr={2} />
                      <Button onClick={() => copyToClipboard(apiKey.key)}>
                        <CopyIcon />
                      </Button>
                    </Flex>
                  </Box>

                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={2}>
                      <Text fontWeight="bold">Script GTM</Text>
                      <Button
                        onClick={() =>
                          copyToClipboard(
                            selectedFunctionality.scriptTemplate(apiKey.key),
                          )
                        }
                        leftIcon={<CopyIcon />}
                      >
                        Copiar Script Completo
                      </Button>
                    </Box>
                    <Box
                      bg={colors.fontDark}
                      p={2}
                      borderRadius="md"
                      width="full"
                      maxHeight="300px"
                      maxWidth="100%"
                      overflow="auto"
                      css={scrollbarStyles({ width: '4px' })}
                      zIndex={2}
                    >
                      <ScriptDisplay
                        code={selectedFunctionality.scriptTemplate(apiKey.key)}
                        language="html"
                      />
                    </Box>
                  </Box>
                </VStack>

                <HStack justifyContent="space-between">
                  <Button onClick={onClose} variant="outline">
                    Fechar
                  </Button>
                  <Button
                    variant="solid"
                    bgColor={colors.red}
                    color="white"
                    _hover={{ bgColor: colors.danger }}
                    onClick={() => handleGenerateNewApiKey()}
                  >
                    Invalidar e Gerar Nova API Key
                  </Button>
                </HStack>
              </VStack>
            </Box>
          ) : (
            <Box p={6}>
              <VStack spacing={6} align="stretch">
                <Flex justifyContent="space-between" alignItems="center">
                  <Heading size="lg" display="flex" alignItems="center">
                    {selectedPlatform?.icon && (
                      <Icon as={selectedPlatform.icon} mr={3} boxSize={8} />
                    )}
                    {selectedPlatform?.name}
                  </Heading>
                  <Badge colorScheme="yellow" fontSize="md" borderRadius="full">
                    <WarningIcon m={2} />
                    Não Configurado
                  </Badge>
                </Flex>

                <Divider />

                <Box>
                  <Heading size="md" mb={2}>
                    {selectedFunctionality.name}
                  </Heading>
                  <Text color="gray.600" mb={4}>
                    {selectedFunctionality.description}
                  </Text>
                </Box>

                <VStack
                  spacing={4}
                  align="stretch"
                  bg="yellow.100"
                  p={4}
                  borderRadius="lg"
                >
                  <Flex alignItems="center">
                    <WarningIcon color="yellow.500" mr={3} boxSize={6} />
                    <Text fontWeight="bold">Nenhuma API Key configurada</Text>
                  </Flex>
                  <Text>
                    Para utilizar esta integração, você precisa gerar uma API
                    Key.
                  </Text>
                </VStack>

                <HStack justifyContent="space-between">
                  <Button onClick={onClose} variant="outline">
                    Cancelar
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() =>
                      handleGenerateApiKey(
                        selectedPlatform.integration,
                        selectedFunctionality.id,
                      )
                    }
                  >
                    Gerar API Key
                  </Button>
                </HStack>
              </VStack>
            </Box>
          )}
        </ModalContent>
      </Loading>
    </Modal>
  );
};

export default ApiKeySettingsModal;
