import React from 'react';
import {
  Box,
  Button,
  Flex,
  HStack,
  Text,
  useClipboard,
  Circle,
  VStack,
} from '@chakra-ui/react';
import { FaCheck } from 'react-icons/fa';
import { MdContentCopy } from 'react-icons/md';

interface SyntaxHighlighterProps {
  code: string;
  language?: 'html' | 'javascript';
}

const SyntaxHighlighter = ({
  code,
  language = 'html',
}: SyntaxHighlighterProps) => {
  const { hasCopied, onCopy } = useClipboard(code);

  const formatCode = (code: string) => {
    const trimmedCode = code.trim();
    const lines = trimmedCode.split('\n').map((line) => line.trimEnd());
    const minIndent = Math.min(
      ...lines
        .filter((line) => line.trim())
        .map((line) => line.match(/^\s*/)?.[0].length ?? 0),
    );
    return lines.map((line) => line.slice(minIndent)).join('\n');
  };

  const highlightSyntax = (code: string) => {
    if (language === 'html') {
      return code
        .replace(
          /(&lt;\/?\w+(?:\s+\w+(?:=["'].*?["'])?)*\s*\/?\s*&gt;)/g,
          '<span style="color: #F687B3">$1</span>',
        )
        .replace(/(&quot;.*?&quot;)/g, '<span style="color: #48BB78">$1</span>')
        .replace(
          /(&lt;!--.*?--&gt;)/g,
          '<span style="color: #718096">$1</span>',
        );
    } else {
      return code
        .replace(
          /(function|var|let|const|return|if|for|while|do|switch|case|break)/g,
          '<span style="color: #B794F4">$1</span>',
        )
        .replace(
          /(".*?"|'.*?'|`.*?`)/g,
          '<span style="color: #48BB78">$1</span>',
        )
        .replace(/(\b\d+\b)/g, '<span style="color: #4299E1">$1</span>');
    }
  };

  const processedCode = formatCode(code)
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;');

  const highlightedCode = highlightSyntax(processedCode);
  const lines = highlightedCode.split('\n');

  return (
    <Box
      bg="gray.900"
      borderRadius="lg"
      p={4}
      position="relative"
      boxShadow="xl"
    >
      <Flex justify="space-between" align="center" mb={3} px={2}>
        <HStack spacing={2}>
          <Circle size="12px" bg="red.500" />
          <Circle size="12px" bg="yellow.500" />
          <Circle size="12px" bg="green.500" />
        </HStack>
        <Text color="gray.400" fontSize="sm" fontFamily="mono">
          {language.toUpperCase()}
        </Text>
      </Flex>

      <Box position="relative">
        <Box
          as="pre"
          fontFamily="mono"
          fontSize="sm"
          lineHeight="tall"
          whiteSpace="pre-wrap"
        >
          <Box as="code" display="block">
            <VStack align="stretch" spacing={0}>
              {lines.map((line, i) => (
                <Flex
                  key={i}
                  _hover={{ bg: 'gray.800' }}
                  transition="background 0.2s"
                  px={2}
                >
                  <Text
                    color="gray.500"
                    w="8"
                    flexShrink={0}
                    userSelect="none"
                    textAlign="right"
                    mr={4}
                  >
                    {i + 1}
                  </Text>
                  <Box
                    flex={1}
                    color="gray.300"
                    dangerouslySetInnerHTML={{ __html: line || '&nbsp;' }}
                  />
                </Flex>
              ))}
            </VStack>
          </Box>
        </Box>

        <Button
          position="absolute"
          zIndex={1}
          top={2}
          right={2}
          size="sm"
          colorScheme={hasCopied ? 'green' : 'gray'}
          onClick={onCopy}
          leftIcon={
            hasCopied ? <FaCheck size={14} /> : <MdContentCopy size={14} />
          }
        >
          {hasCopied ? 'Copiado!' : 'Copiar'}
        </Button>
      </Box>
    </Box>
  );
};

export default SyntaxHighlighter;
