import {
  Box,
  Button,
  Heading,
  Table,
  TableContainer,
  Tag,
  Tbody,
  Td,
  Thead,
  Tr,
  Text,
  Tooltip,
  Flex,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { Th } from '../../../components/CustomTable';
import { apiRoutes } from '../../../constants/api-routes';
import { appPaths } from '../../../constants/app-paths';
import { IntegrationsService } from '../../../services/integrations.service';
import { useAppModuleAccessGuard } from '../../../hooks/useAppModuleAccessGuard';
import { colors } from '../../../constants/colors';

enum Integrations {
  ORDERS = 'Pedidos',
  ABANDONED_CARTS = 'Carrinho abandonado',
  TRACKING_CODE = 'Código de rastreio',
  WELCOME_REGISTRATION = 'Novo cadastro',
  NEW_ORDER = 'Novo pedido',
  ORDER_CONFIRMATION = 'Confirmação de pedido',
  ORDER_PAYMENT_CONFIRMATION = 'Confirmação de pagamento',
  ORDER_STATUS_UPDATE = 'Status de Pedido',
}

// Mapeamento de cores por tipo de integração
const integrationColors = {
  [Integrations.ORDERS]: 'blue',
  [Integrations.ABANDONED_CARTS]: 'orange',
  [Integrations.TRACKING_CODE]: 'green',
  [Integrations.WELCOME_REGISTRATION]: 'teal',
  [Integrations.NEW_ORDER]: 'purple',
  [Integrations.ORDER_CONFIRMATION]: 'cyan',
  [Integrations.ORDER_PAYMENT_CONFIRMATION]: 'pink',
  [Integrations.ORDER_STATUS_UPDATE]: 'yellow',
};

const integrationOptions: {
  name: string;
  integrations: Integrations[];
  path: string;
  statusKey:
    | 'isShopifyActive'
    | 'isVtexActive'
    | 'isUnboxActive'
    | 'isVisualECommerceActive'
    | 'isLojaIntegradaActive'
    | 'isBlingActive'
    | 'isMagazordActive'
    | 'isMagentoActive'
    | 'isIngresseActive'
    | 'isWooCommerceActive'
    | 'isCartPandaActive'
    | 'isOmnyActive'
    | 'isOmieActive'
    | 'isTrayActive'
    | 'isYampiActive'
    | 'isLinxCommerceActive'
    | 'isNuvemShopActive'
    | 'isShoppubActive'
    | 'isTinyActive'
    | 'isVndaActive'
    | 'isMillenniumActive'
    | 'isGoogleTagManagerActive'
    | 'isVarejoOnlineActive'
    | 'isVendaAiActive'
    | 'isIrrobaActive'
    | 'isWakeCommerceActive';
}[] = [
  {
    name: 'Shopify',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.shopify(),
    statusKey: 'isShopifyActive',
  },
  {
    name: 'Vtex',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.vtex(),
    statusKey: 'isVtexActive',
  },
  {
    name: 'Bling',
    integrations: [Integrations.ORDERS, Integrations.TRACKING_CODE],
    path: appPaths.settings.integrationSettings.bling(),
    statusKey: 'isBlingActive',
  },
  {
    name: 'Loja Integrada',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.lojaIntegrada(),
    statusKey: 'isLojaIntegradaActive',
  },
  {
    name: 'CartPanda',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
    ],
    path: appPaths.settings.integrationSettings.cartPanda(),
    statusKey: 'isCartPandaActive',
  },
  {
    name: 'Omny',
    integrations: [Integrations.ORDERS, Integrations.ORDER_STATUS_UPDATE],
    path: appPaths.settings.integrationSettings.omny(),
    statusKey: 'isOmnyActive',
  },
  {
    name: 'Omie',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.omie(),
    statusKey: 'isOmieActive',
  },
  {
    name: 'Tray',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
      Integrations.ABANDONED_CARTS,
    ],
    path: appPaths.settings.integrationSettings.tray(),
    statusKey: 'isTrayActive',
  },
  {
    name: 'Unbox',
    integrations: [Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.unbox(),
    statusKey: 'isUnboxActive',
  },
  {
    name: 'Visual E-Commerce',
    integrations: [Integrations.ORDERS, Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.visualECommerce(),
    statusKey: 'isVisualECommerceActive',
  },
  {
    name: 'Magazord',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.magazord(),
    statusKey: 'isMagazordActive',
  },
  {
    name: 'Magento',
    integrations: [Integrations.ORDERS, Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.magento(),
    statusKey: 'isMagentoActive',
  },
  {
    name: 'Linx Commerce',
    integrations: [Integrations.ORDERS, Integrations.ORDER_STATUS_UPDATE],
    path: appPaths.settings.integrationSettings.linxCommerce(),
    statusKey: 'isLinxCommerceActive',
  },
  {
    name: 'NuvemShop',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ABANDONED_CARTS,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.nuvemShop(),
    statusKey: 'isNuvemShopActive',
  },
  {
    name: 'Shoppub',
    integrations: [
      Integrations.ORDERS,
      Integrations.ABANDONED_CARTS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.shoppub(),
    statusKey: 'isShoppubActive',
  },
  {
    name: 'Tiny',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ORDER_STATUS_UPDATE,
    ],
    path: appPaths.settings.integrationSettings.tiny(),
    statusKey: 'isTinyActive',
  },
  {
    name: 'Vnda',
    integrations: [
      Integrations.ORDERS,
      Integrations.TRACKING_CODE,
      Integrations.ABANDONED_CARTS,
    ],
    path: appPaths.settings.integrationSettings.vnda(),
    statusKey: 'isVndaActive',
  },
  {
    name: 'Millennium',
    integrations: [Integrations.ORDERS, Integrations.ORDER_STATUS_UPDATE],
    path: appPaths.settings.integrationSettings.millennium(),
    statusKey: 'isMillenniumActive',
  },
  {
    name: 'Google Tag Manager',
    integrations: [Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.googleTagManager(),
    statusKey: 'isGoogleTagManagerActive',
  },
  {
    name: 'Varejo Online',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.varejoOnline(),
    statusKey: 'isVarejoOnlineActive',
  },
  {
    name: 'Venda Ai',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.vendaAi(),
    statusKey: 'isVendaAiActive',
  },
  {
    name: 'Irroba',
    integrations: [Integrations.ORDERS],
    path: appPaths.settings.integrationSettings.irroba(),
    statusKey: 'isIrrobaActive',
  },
  {
    name: 'Wake Commerce',
    integrations: [Integrations.ORDERS, Integrations.TRACKING_CODE],
    path: appPaths.settings.integrationSettings.wakeCommerce(),
    statusKey: 'isWakeCommerceActive',
  },
  {
    name: 'Yampi',
    integrations: [Integrations.ABANDONED_CARTS],
    path: appPaths.settings.integrationSettings.yampi(),
    statusKey: 'isYampiActive',
  },
];

const IntegrationSettingsPage = () => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const { data: integrationStatus } = useQuery(
    apiRoutes.getIntegrationStatusSummary(),
    async () => {
      const { data } = await IntegrationsService.getIntegrationStatusSummary();
      return data;
    },
  );

  const renderIntegrationTags = (integrations: Integrations[]) => {
    const MAX_VISIBLE_TAGS = 2;
    const visibleIntegrations = integrations.slice(0, MAX_VISIBLE_TAGS);
    const remainingCount = integrations.length - MAX_VISIBLE_TAGS;

    return (
      <Flex wrap="wrap" gap={2}>
        {visibleIntegrations.map((integration) => (
          <Tag
            key={integration}
            colorScheme={integrationColors[integration] || 'purple'}
            mr={2}
          >
            {integration}
          </Tag>
        ))}

        {remainingCount > 0 && (
          <Tooltip
            label={
              <Box p={1}>
                {integrations.slice(MAX_VISIBLE_TAGS).map((integration) => (
                  <Tag
                    key={integration}
                    colorScheme={integrationColors[integration] || 'purple'}
                    mr={1}
                    mb={1}
                  >
                    {integration}
                  </Tag>
                ))}
              </Box>
            }
            placement="top"
            hasArrow
          >
            <Tag colorScheme="gray">+{remainingCount}</Tag>
          </Tooltip>
        )}
      </Flex>
    );
  };

  return (
    <Box>
      <Heading mb={5}>Integrações</Heading>
      <Box mt={6}>
        <TableContainer
          borderRadius="md"
          border="1px solid"
          borderColor={colors.border}
        >
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Plataforma</Th>
                <Th>Integrações</Th>
                <Th>Status</Th>
                <Th> </Th>
              </Tr>
            </Thead>
            <Tbody>
              {integrationOptions.map((integration) => {
                if (!checkUserHasPathAccess(integration.path)) return null;
                return (
                  <Tr key={integration.name} bg={colors.white}>
                    <Td>{integration.name}</Td>
                    <Td>{renderIntegrationTags(integration.integrations)}</Td>
                    <Td>
                      {integrationStatus?.[integration.statusKey] ? (
                        <Tag colorScheme="green">Ativo</Tag>
                      ) : (
                        <Tag colorScheme="red">Inativo</Tag>
                      )}
                    </Td>
                    <Td>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(integration.path)}
                      >
                        Configurar
                      </Button>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

export default IntegrationSettingsPage;
