import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import React from 'react';
import { useMutation } from 'react-query';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveTinyCredentialsDto,
} from '../../../../services/integrations.service';

const schema = yup.object({
  tinyApiToken: yup.string().required('"Api key" é obrigatória'),
});

const TinyIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    // setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  // const { data: tinyConfig, refetch: refetchTinyConfig } = useQuery(apiRoutes.getTinyConfig(), async () => {
  //   const { data } = await IntegrationsService.getTinyConfig();
  //   return data;
  // }, {
  //   onSuccess: (data) => {
  //     setValue("isTinyActive", data.isTinyActive || '');
  //   },
  // });

  const saveTinyCredentials = useMutation(
    (data: SaveTinyCredentialsDto) =>
      IntegrationsService.saveTinyCredentials(data),
    {
      onSuccess: () => {
        // refetchTinyConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    await saveTinyCredentials.mutateAsync(data);
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>Api Token</FormLabel>
            <Input
              placeholder="******************"
              type="password"
              {...register('tinyApiToken')}
              isInvalid={errors.tinyApiToken?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.tinyApiToken?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              type="submit"
              color={colors.white}
              bgColor={colors.primary}
              width="fit-content"
              isLoading={saveTinyCredentials.isLoading}
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default TinyIntegrationPage;
