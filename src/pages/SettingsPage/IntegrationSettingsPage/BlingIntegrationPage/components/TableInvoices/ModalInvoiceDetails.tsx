import {
  Box,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Table,
  TableContainer,
  Tbody,
  Td,
  Thead,
  Tr,
} from '@chakra-ui/react';
import { Th } from '../../../../../../components/CustomTable';
import { Invoice } from '../../../../../../types/Invoice';
import { MoneyUtils } from '../../../../../../utils/money.utils';
import { format } from 'date-fns';
import { colors } from '../../../../../../constants/colors';
import { scrollbarStyles } from '../../../../../../styles/scrollbar.styles';

interface ModalInvoiceDetailsProps {
  invoice: Invoice | null;
  onClose: () => void;
}

const ModalInvoiceDetails = ({
  invoice,
  onClose,
}: ModalInvoiceDetailsProps) => {
  return (
    <Modal isOpen={!!invoice} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent maxWidth="60vw">
        <ModalHeader>
          {invoice ? (
            <>
              Detalhes da Fatura {invoice.id.charAt(2)}
              <Box fontSize="sm" fontWeight="normal" mt={1} color="gray.600">
                Referente ao mês{' '}
                {format(new Date(invoice.referenceMonth), 'MM/yyyy')} •
                Vencimento: {format(new Date(invoice.dueDate), 'dd/MM/yyyy')}
              </Box>
            </>
          ) : (
            'Carregando...'
          )}
        </ModalHeader>
        <ModalCloseButton />
        {invoice && (
          <ModalBody pb={6}>
            <Flex justify="flex-end" mb={4}>
              <Box textAlign="right">
                <Box fontSize="lg" fontWeight="bold">
                  Total
                </Box>
                <Box fontSize="2xl" fontWeight="bold">
                  {MoneyUtils.formatCurrency(invoice.value)}
                </Box>
              </Box>
            </Flex>

            <TableContainer
              maxH="50vh"
              overflowY="auto"
              borderWidth="1px"
              borderRadius="md"
              css={scrollbarStyles({ width: '3px' })}
            >
              <Table variant="simple" size="md">
                <Thead
                  position="sticky"
                  top={0}
                  bg="white"
                  zIndex={1}
                  bgColor={colors.slateSuperLight}
                >
                  <Tr>
                    <Th>ITEM</Th>
                    <Th isNumeric>QUANTIDADE</Th>
                    <Th isNumeric>VALOR UNIT.</Th>
                    <Th isNumeric>DESCONTO</Th>
                    <Th isNumeric>TOTAL</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {invoice.invoiceItems.map((item, index) => (
                    <Tr key={item.id || index}>
                      <Td>{item.name}</Td>
                      <Td isNumeric>{item.quantity}</Td>
                      <Td isNumeric>
                        {MoneyUtils.formatCurrency(item.unitPrice)}
                      </Td>
                      <Td isNumeric>
                        {MoneyUtils.formatCurrency(item.discount)}
                      </Td>
                      <Td
                        isNumeric
                        fontWeight={item.totalPrice > 0 ? 'bold' : 'normal'}
                      >
                        {MoneyUtils.formatCurrency(item.totalPrice)}
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
          </ModalBody>
        )}
      </ModalContent>
    </Modal>
  );
};

export default ModalInvoiceDetails;
