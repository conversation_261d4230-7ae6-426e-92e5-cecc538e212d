import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveVndaCredentialsDto,
} from '../../../../services/integrations.service';

const schema = yup.object({
  vndaApiToken: yup.string().required('"Api Token" é obrigatório'),
  vndaShopHost: yup
    .string()
    .required('"Shop Host" é obrigatório')
    .matches(
      /^(?!https?:\/\/)([a-zA-Z0-9-_]+\.)+[a-zA-Z]{2,6}?$/,
      '"Shop Host" deve ser um domínio válido e não deve incluir http ou https',
    ),
});

const VndaIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { data: vndaConfig, refetch: refetchVndaConfig } = useQuery(
    apiRoutes.getVndaConfig(),
    async () => {
      const { data } = await IntegrationsService.getVndaConfig();
      return data;
    },
    {
      onSuccess: (data) => {
        setValue('vndaShopHost', data.vndaShopHost || '');
      },
    },
  );

  const saveVndaCredentials = useMutation(
    (data: SaveVndaCredentialsDto) =>
      IntegrationsService.saveVndaCredentials(data),
    {
      onSuccess: () => {
        refetchVndaConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    await saveVndaCredentials.mutateAsync(data);
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>Shop Host</FormLabel>
            <Input
              placeholder="Ex.: loja.exemplo.com"
              {...register('vndaShopHost')}
              isInvalid={errors.vndaShopHost?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.vndaShopHost?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Api Token</FormLabel>
            <Input
              placeholder="******************"
              type="password"
              {...register('vndaApiToken')}
              isInvalid={errors.vndaApiToken?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.vndaApiToken?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              type="submit"
              color={colors.white}
              bgColor={colors.primary}
              width="fit-content"
              isLoading={saveVndaCredentials.isLoading}
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default VndaIntegrationPage;
