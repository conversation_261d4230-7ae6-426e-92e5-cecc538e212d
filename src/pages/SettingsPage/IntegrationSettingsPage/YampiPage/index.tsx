import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveYampiCredentialsDto,
} from '../../../../services/integrations.service';

const schema = yup.object({
  yampiAlias: yup.string().required('"Alias" é obrigatório'),
  yampiUserSecretKey: yup.string().required('"Secrete Key" é obrigatória'),
  yampiUserToken: yup.string().required('"User Token" é obrigatório'),
});

const YampiIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { data: yampiConfig, refetch: refetchYampiConfig } = useQuery(
    apiRoutes.getYampiConfig(),
    async () => {
      const { data } = await IntegrationsService.getYampiConfig();
      return data;
    },
    {
      onSuccess: (data) => {
        setValue('yampiAlias', data.yampiAlias || '');
      },
    },
  );

  const saveYampiCredentials = useMutation(
    (data: SaveYampiCredentialsDto) =>
      IntegrationsService.saveYampiCredentials(data),
    {
      onSuccess: () => {
        refetchYampiConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    await saveYampiCredentials.mutateAsync(data);
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>Alias</FormLabel>
            <Input
              placeholder="Cole o Shop Slug aqui (ex: loja-teste)"
              {...register('yampiAlias')}
              isInvalid={errors.yampiAlias?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.yampiAlias?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Secret Key</FormLabel>
            <Input
              placeholder="Cole o User Secret aqui (ex.: sk_7mtexto...)"
              type="password"
              {...register('yampiUserSecretKey')}
              isInvalid={errors.yampiUserSecretKey?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.yampiUserSecretKey?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>User Token</FormLabel>
            <Input
              placeholder="Cole o User Token aqui (ex.: ntex...)"
              type="password"
              {...register('yampiUserToken')}
              isInvalid={errors.yampiUserToken?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.yampiUserToken?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              type="submit"
              color={colors.white}
              bgColor={colors.primary}
              width="fit-content"
              isLoading={saveYampiCredentials.isLoading}
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default YampiIntegrationPage;
