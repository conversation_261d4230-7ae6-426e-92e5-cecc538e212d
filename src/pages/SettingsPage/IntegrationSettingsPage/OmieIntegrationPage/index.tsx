import { Box, Button, Divider, Flex, FormControl, FormLabel, Heading, Input, Stack, Text, useToast } from '@chakra-ui/react';
import React from 'react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import { IntegrationsService, SaveOmieCredentialsDto } from '../../../../services/integrations.service';

const schema = yup.object({
  omieAppKey: yup.string().required('\'App Key\' é obrigatório'),
  omieAppSecret: yup.string().required('\'App Secret\' é obrigatória'),
});

const OmieIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    // setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  // const { data: omieConfig, refetch: refetchOmieConfig } = useQuery(apiRoutes.getOmieConfig(), async () => {
  //   const { data } = await IntegrationsService.getOmieConfig();
  //   return data;
  // }, {
  //   onSuccess: (data) => {
  //     setValue("isOmieActive", data.isOmieActive);
  //   },
  // });

  const saveOmieCredentials = useMutation(
    (data: SaveOmieCredentialsDto) => IntegrationsService.saveOmieCredentials(data),
    {
      onSuccess: () => {
        // refetchOmieConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    }
  );

  async function onSubmit(data: any) {
    await saveOmieCredentials.mutateAsync(data);
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>App Key</FormLabel>
            <Input
              placeholder="**********************"
              type="password"
              {...register('omieAppKey')}
              isInvalid={errors.omieAppKey?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.omieAppKey?.message}
            </Text>
          </FormControl>

          <FormControl>
            <FormLabel>App Secret</FormLabel>
            <Input
              placeholder="**********************"
              type="password"
              {...register('omieAppSecret')}
              isInvalid={errors.omieAppSecret?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.omieAppSecret?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              type="submit"
              color={colors.white}
              bgColor={colors.primary}
              width="fit-content"
              isLoading={saveOmieCredentials.isLoading}
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default OmieIntegrationPage;
