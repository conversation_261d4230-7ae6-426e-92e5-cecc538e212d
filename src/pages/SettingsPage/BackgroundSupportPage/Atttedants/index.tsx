import {
  Avatar,
  Box,
  Button,
  Card,
  CardBody,
  Flex,
  FormControl,
  Heading,
  Switch,
  Text,
  useBoolean,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { CompaniesService } from '../../../../services/companies.service';
import { useEffect } from 'react';
import { colors } from '../../../../constants/colors';
import TableAttedants from './AttedantsTable';
import WhatsappMessageItem from '../../../../components/WhatsappMessageItem';

const schema = yup
  .object({
    shouldIncludeAttendantNameInMessages: yup.boolean(),
  })
  .required();

const AttendantsPage = () => {
  const [isLoading, setIsLoading] = useBoolean();
  const toast = useToast();
  const {
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    CompaniesService.getCompanyDetails().then(({ data: company }) => {
      setValue(
        'shouldIncludeAttendantNameInMessages',
        company.shouldIncludeAttendantNameInMessages,
      );
    });
  }, []);

  async function onSubmit(data: any) {
    setIsLoading.on();
    CompaniesService.updateCompany(data)
      .then((res) => {
        toast({
          title: 'Opção salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .finally(() => {
        setIsLoading.off();
      });
  }

  const showAgentName = watch('shouldIncludeAttendantNameInMessages');

  const text = 'Olá, tudo bem? Como posso ajudar você hoje?';

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Flex width="100%" justifyContent="space-between" paddingBottom={'16px'}>
        <Box>
          <Heading size="lg" mb={1}>
            Exibir nome do atendente nas respostas
          </Heading>
          <Text color="gray.500" mb={8}>
            Deixe claro quem está respondendo: mostre seu nome ao conversar com
            os clientes.
          </Text>
        </Box>
        <form onSubmit={handleSubmit(onSubmit)}>
          <FormControl>
            <Controller
              control={control}
              name="shouldIncludeAttendantNameInMessages"
              render={({ field }) => (
                <Switch
                  {...field}
                  isChecked={!!field.value}
                  isDisabled={isLoading}
                  onChange={(e) => {
                    const newValue = e.target.checked;
                    field.onChange(newValue);
                    onSubmit({
                      shouldIncludeAttendantNameInMessages: newValue,
                    });
                  }}
                  size="lg"
                  colorScheme="green"
                />
              )}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.firstContactMessage?.message}
            </Text>
          </FormControl>
        </form>
      </Flex>
      <Box>
        <Text
          fontWeight="medium"
          bg="#198B81"
          color="white"
          p={2}
          borderTopRadius="md"
        >
          Preview - Visualização para o cliente
        </Text>
        <Box p={4} bgColor="#e8dfd7">
          <WhatsappMessageItem message={text} showAgentName={showAgentName} />
        </Box>
      </Box>
      <Box mt={8}>
        <Heading size="lg" mb={1}>
          Atendentes
        </Heading>
        <Text color="gray.500" mb={8}>
          Gerencie seus atendentes e suas configurações. Atendentes marcados
          como "Admin" terão acesso a todas as conversas.
        </Text>
        <TableAttedants />
      </Box>
    </Box>
  );
};

export default AttendantsPage;
