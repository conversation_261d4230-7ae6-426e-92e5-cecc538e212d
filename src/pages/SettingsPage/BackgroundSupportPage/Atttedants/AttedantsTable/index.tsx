import {
  Avatar,
  Table,
  <PERSON><PERSON>ontainer,
  <PERSON>body,
  Td,
  Thead,
  Tr,
  Switch,
  Flex,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { colors } from '../../../../../constants/colors';
import { TextTd, Th } from '../../../../../components/CustomTable';
import { UsersService } from '../../../../../services/users.service';
import { User } from '../../../../../types/Prisma';
import { scrollbarStyles } from '../../../../../styles/scrollbar.styles';

const TableAttedants = () => {
  const { data: companyAgents = [] } = useQuery(
    apiRoutes.listCompanyAgents(),
    async () => {
      const { data } = await UsersService.listCompanyAgents();
      return data.filter((agent) => agent.isActive);
    },
  );

  return (
    <TableContainer
      borderRadius="md"
      border="1px solid"
      borderColor={colors.border}
      maxHeight="300px"
      overflowY="auto"
      css={scrollbarStyles({ width: '4px' })}
    >
      <Table variant="simple" style={{ tableLayout: 'fixed' }}>
        <Thead>
          <Tr display="flex" width="100%" bgColor={colors.slateSuperLight}>
            <Th width="25%">NOME</Th>
            <Th width="25%">EMAIL</Th>
            <Th width="25%" display="flex" justifyContent="center">
              ATIVO
            </Th>
            <Th width="25%" display="flex" justifyContent="center">
              ADMIN
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {companyAgents?.map((agent: User) => (
            <Tr display="flex" width="100%" key={agent.id} bg={colors.white}>
              <TextTd width="25%" textForTooltip={agent.name}>
                <Flex align="center" gap="2">
                  <Avatar name={agent.name} size="sm" />
                  {agent.name}
                </Flex>
              </TextTd>
              <TextTd width="25%" textForTooltip={agent.email}>
                {agent.email}
              </TextTd>
              <Td
                width="25%"
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Switch
                  isChecked={agent.isActive}
                  isReadOnly
                  colorScheme="black"
                />
              </Td>
              <Td
                width="25%"
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Switch
                  isChecked={agent.canViewAllConversations}
                  isReadOnly
                  colorScheme="black"
                />
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default TableAttedants;
