import {
  Box,
  Button,
  Modal,
  Modal<PERSON>ody,
  Modal<PERSON>lose<PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>eader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import React from 'react';
import { QuickReply } from '../../../../../../types/Prisma';

interface QuickReplyDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedQuickReply: QuickReply | null;
}

export function QuickReplyDetailsModal({
  isOpen,
  onClose,
  selectedQuickReply,
}: QuickReplyDetailsModalProps): React.ReactElement {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Resposta Rápida</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Box display={'flex'} flexDirection={'column'} gap={'12px'}>
            <Box display={'flex'} flexDirection={'column'} gap={'4px'}>
              <Text as="b" size="sm">
                Título:
              </Text>
              <Text size="sm">{selectedQuickReply?.title}</Text>
            </Box>
            <Box display={'flex'} flexDirection={'column'} gap={'4px'}>
              <Text as="b" size="sm" mt={4}>
                Texto:
              </Text>
              <Text size="sm">{selectedQuickReply?.text}</Text>
            </Box>
          </Box>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" onClick={onClose}>
            Fechar
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
