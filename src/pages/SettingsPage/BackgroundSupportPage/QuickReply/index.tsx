import { Box, Button, Flex, Heading, Text } from '@chakra-ui/react';
import TableQuickReplies from './components';
import { useCrudQuickReplyModal } from '../../../../hooks/useCrudQuickReplyModal';

const QuickRepliesPage = () => {
  const { openCreateModal } = useCrudQuickReplyModal();

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Flex width="100%" justifyContent="space-between" paddingBottom={'16px'}>
        <Box>
          <Heading size="lg" mb={1}>
            Respostas Rápidas
          </Heading>
          <Text color="gray.500" mb={8}>
            Mensagens predefinidas para agilizar o atendimento
          </Text>
        </Box>
        <Button variant="primary" onClick={openCreateModal}>
          + Resposta Rápida
        </Button>
      </Flex>
      <TableQuickReplies />
    </Box>
  );
};

export default QuickRepliesPage;
