import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useBoolean,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { CompaniesService } from '../../../../services/companies.service';
import * as yup from 'yup';
import { colors } from '../../../../constants/colors';
import { useEffect } from 'react';

const schema = yup
  .object({
    optoutMessage: yup.string(),
  })
  .required();

const OptOutPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [isLoading, setIsLoading] = useBoolean();
  const toast = useToast();

  useEffect(() => {
    CompaniesService.getCompanyDetails().then(({ data: company }) => {
      setValue('optoutMessage', company.optoutMessage || 'STOP');
    });
  }, []);

  async function onSubmit(data: any) {
    setIsLoading.on();
    CompaniesService.updateCompany(data)
      .then((res) => {
        toast({
          title: 'Palavra de bloqueio salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .finally(() => {
        setIsLoading.off();
      });
  }

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Box>
        <Heading size="lg" mb={1}>
          Configurações de mensagens
        </Heading>
        <Text color="gray.500" mb={8}>
          Configure opções de bloqueio e mensagens
        </Text>
      </Box>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={6}>
          <FormControl>
            <FormLabel>Personalizar palavra de bloqueio</FormLabel>
            <Input
              placeholder="Personalizar palavra de bloqueio"
              {...register('optoutMessage')}
              isInvalid={errors.optoutMessage?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.optoutMessage?.message}
            </Text>
          </FormControl>

          <Divider />

          <Flex justify="flex-end">
            <Button
              width="30%"
              isLoading={isLoading}
              color={colors.white}
              bgColor={colors.primary}
              type="submit"
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default OptOutPage;
