import {
  useBoolean,
  Box,
  Heading,
  Stack,
  FormControl,
  FormLabel,
  Divider,
  Flex,
  Button,
  Text,
  useToast,
  Textarea,
} from '@chakra-ui/react';
import { colors } from '../../../../constants/colors';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { CompaniesService } from '../../../../services/companies.service';
import { useEffect, useRef } from 'react';
import { omit } from 'lodash';
import { TemplateParametersEnum } from '../../../../types/TemplateParametersEnum';

const WELCOME_MESSAGE_PARAMETERS = [TemplateParametersEnum.CUSTOMER_NAME];

const schema = yup
  .object({
    firstContactMessage: yup.string(),
  })
  .required();

const FormWelcomeMessage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [isLoading, setIsLoading] = useBoolean();
  const toast = useToast();
  const contentRef =
    useRef() as React.MutableRefObject<HTMLTextAreaElement | null>;

  useEffect(() => {
    CompaniesService.getCompanyDetails().then(({ data: company }) => {
      setValue('firstContactMessage', company.firstContactMessage);
    });
  }, []);

  const handleClickParameter = (parameter: string) => {
    if (!contentRef.current) return;
    const contentEl = contentRef.current;

    const startPosition = contentEl.selectionStart;
    const endPosition = contentEl.selectionEnd;
    const currentValue = contentEl.value;

    const newValue =
      currentValue.substring(0, startPosition) +
      parameter +
      currentValue.substring(endPosition);

    setValue('firstContactMessage', newValue);
    contentRef.current.focus();
    contentRef.current.setSelectionRange(
      startPosition + parameter.length + 1,
      startPosition + parameter.length + 1,
    );
  };

  async function onSubmit(data: any) {
    setIsLoading.on();
    CompaniesService.updateCompany(data)
      .then((res) => {
        toast({
          title: 'Mensagem inicial salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .finally(() => {
        setIsLoading.off();
      });
  }

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Heading size="lg" mb={1}>
        Mensagem de boas vindas
      </Heading>
      <Text color="gray.500" mb={8}>
        Esta mensagem será enviada quando um cliente iniciar uma conversa com
        sua empresa dentro do horário de atendimento
      </Text>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={6}>
          <FormControl>
            <FormLabel>Mensagem inicial</FormLabel>
            <Textarea
              placeholder="Mensagem inicial"
              {...omit(register('firstContactMessage'), 'ref')}
              ref={(e) => {
                register('firstContactMessage').ref(e);
                contentRef.current = e;
              }}
              isInvalid={errors.firstContactMessage?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.firstContactMessage?.message}
            </Text>

            <Box mt={3}>
              <Text>Adicionar parâmetro</Text>
              <Flex gap={2} alignItems="center" flexWrap={'wrap'} maxW="100%">
                {WELCOME_MESSAGE_PARAMETERS.map((parameter) => (
                  <Button
                    key={parameter}
                    fontSize="xs"
                    color="gray.500"
                    mt={2}
                    mb={2}
                    variant="outline"
                    onClick={() => handleClickParameter(parameter)}
                  >
                    {parameter}
                  </Button>
                ))}
              </Flex>
            </Box>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              width="30%"
              isLoading={isLoading}
              color={colors.white}
              bgColor={colors.primary}
              type="submit"
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default FormWelcomeMessage;
