import { Box, Flex, Heading, Text } from '@chakra-ui/react';
import { PushNotificationSettings } from './components/PushNotificationsSettings';

const PushNotificationsPage = () => {
  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Flex width="100%" justifyContent="space-between" paddingBottom={'16px'}>
        <Box>
          <Heading size="lg" mb={1}>
            Notificações Push em segundo plano
          </Heading>
          <Text color="gray.500" mb={8}>
            Ative as notificações push para receber alertas na sua tela, caso
            haja novas mensagens, quando a aplicação estiver em segundo plano ou
            fechada
          </Text>
        </Box>
      </Flex>
      <PushNotificationSettings />
    </Box>
  );
};

export default PushNotificationsPage;
