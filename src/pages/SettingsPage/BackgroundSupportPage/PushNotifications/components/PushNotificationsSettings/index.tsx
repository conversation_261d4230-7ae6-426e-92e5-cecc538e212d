import React from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Icon,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
} from '@chakra-ui/react';
import {
  FiBell,
  FiBellOff,
  FiCheckCircle,
  FiXCircle,
  FiSend,
} from 'react-icons/fi';
import { usePushNotifications } from '../../../../../../hooks/usePushNotifications';

export const PushNotificationSettings: React.FC = () => {
  const {
    isSupported,
    isEnabled,
    permission,
    isLoading,
    enablePushNotifications,
    disablePushNotifications,
    sendTestPushNotificationMutation,
  } = usePushNotifications();

  const toast = useToast();

  const handleEnableNotifications = async (): Promise<void> => {
    try {
      await enablePushNotifications();
      toast({
        title: 'Notificações ativadas',
        description:
          'Você receberá notificações push quando houver novas mensagens.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Erro ao ativar notificações',
        description:
          err instanceof Error ? err.message : 'Tente novamente mais tarde.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleDisableNotifications = async (): Promise<void> => {
    try {
      await disablePushNotifications();
      toast({
        title: 'Notificações desativadas',
        description: 'Você não receberá mais notificações push.',
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Erro ao desativar notificações',
        description:
          err instanceof Error ? err.message : 'Tente novamente mais tarde.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleSendTestNotification = async (): Promise<void> => {
    try {
      await sendTestPushNotificationMutation.mutateAsync();
      toast({
        title: 'Notificação de teste enviada',
        description: 'A notificação de teste foi enviada com sucesso.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Erro ao enviar notificação de teste',
        description: 'Tente novamente mais tarde.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  if (!isSupported) {
    return (
      <Alert status="warning">
        <AlertIcon />
        <Box>
          <AlertTitle>Navegador não suportado</AlertTitle>
          <AlertDescription>
            Seu navegador não suporta notificações push. Tente usar um navegador
            mais recente.
          </AlertDescription>
        </Box>
      </Alert>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      <Box>
        <Text fontWeight="medium" mb={2}>
          Status das Notificações
        </Text>
        <HStack spacing={2}>
          {isLoading ? (
            <Spinner size="sm" />
          ) : isEnabled ? (
            <HStack spacing={2} color="green.500">
              <Icon as={FiCheckCircle} boxSize={4} />
              <Text fontSize="sm">Ativadas</Text>
            </HStack>
          ) : (
            <HStack spacing={2} color="red.500">
              <Icon as={FiXCircle} boxSize={4} />
              <Text fontSize="sm">Desativadas</Text>
            </HStack>
          )}
        </HStack>
      </Box>

      <Box>
        <Text fontWeight="medium" mb={2}>
          Permissão do Navegador
        </Text>
        <Text fontSize="sm" color="gray.600">
          {permission === 'granted' && 'Permitido'}
          {permission === 'denied' && 'Negado'}
          {permission === 'default' && 'Não solicitado'}
        </Text>
      </Box>

      <VStack spacing={3} align="stretch">
        {isEnabled ? (
          <Button
            leftIcon={<FiBellOff />}
            colorScheme="red"
            variant="outline"
            onClick={handleDisableNotifications}
            isLoading={isLoading}
            loadingText="Desativando..."
          >
            Desativar notificações neste navegador
          </Button>
        ) : (
          <Button
            leftIcon={<FiBell />}
            colorScheme="blue"
            onClick={handleEnableNotifications}
            isLoading={isLoading}
            loadingText="Ativando..."
            isDisabled={permission === 'denied'}
          >
            Ativar notificações neste navegador
          </Button>
        )}

        {isEnabled && (
          <Button
            leftIcon={<Icon as={FiSend} />}
            colorScheme="blue"
            onClick={handleSendTestNotification}
            isLoading={isLoading}
            isDisabled={!isEnabled}
          >
            Enviar Notificação de Teste
          </Button>
        )}
      </VStack>

      {permission === 'denied' && (
        <Alert status="warning">
          <AlertIcon />
          <Box>
            <AlertTitle>Permissão negada</AlertTitle>
            <AlertDescription>
              Para receber notificações, você precisa permitir notificações nas
              configurações do seu navegador.
            </AlertDescription>
          </Box>
        </Alert>
      )}
    </VStack>
  );
};
