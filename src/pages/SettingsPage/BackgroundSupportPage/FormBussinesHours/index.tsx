import {
  Box,
  Flex,
  <PERSON>ing,
  Divider,
  Button,
  useToast,
  FormLabel,
  Textarea,
  Text,
  Checkbox,
  HStack,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useMutation, useQuery } from 'react-query';
import LoadingScreen from '../../../../components/LoadingScreen';
import { apiRoutes } from '../../../../constants/api-routes';
import {
  CompanyBusinessHoursPreferencesDto,
  CompaniesService,
  CompanyBusinessHours,
} from '../../../../services/companies.service';
import BusinessHoursTable from './components/BusinessHoursTable';
import { colors } from '../../../../constants/colors';

const schema = yup
  .object({
    businessHours: yup
      .array()
      .required('Horários de atendimento são obrigatórios'),
    afterHoursMessage: yup.string().nullable(),
    calculateTicketMetricsByBusinessHours: yup.boolean().required(),
  })
  .required();

type FormData = CompanyBusinessHoursPreferencesDto;

const FormBusinessHours = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { isDirty },
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
  });

  const businessHours = watch('businessHours') || [];

  const {
    data: getCompanyBusinessHoursResponse,
    isLoading,
    refetch,
  } = useQuery(
    apiRoutes.getCompanyBusinessHoursPreferences(),
    CompaniesService.getCompanyBusinessHoursPreferences,
    {
      onSuccess: (response) => {
        setValue('businessHours', response.data.businessHours);
        setValue('afterHoursMessage', response.data.afterHoursMessage);
        setValue(
          'calculateTicketMetricsByBusinessHours',
          response.data.calculateTicketMetricsByBusinessHours || false,
        );
      },
    },
  );

  const updateCompanyBusinessHoursMutation = useMutation(
    CompaniesService.updateCompanyBusinessHoursPreferences,
    {
      onSuccess: () => {
        toast({
          title: 'Sucesso',
          description: 'Horário de atendimento salvo com sucesso.',
          status: 'success',
        });
        refetch();
      },
    },
  );

  const onSubmit = async (data: FormData) => {
    updateCompanyBusinessHoursMutation.mutateAsync(data);
  };

  const handleBusinessHoursDataChange = (data: CompanyBusinessHours[]) => {
    setValue('businessHours', data, { shouldDirty: true });
  };

  const handleCancelClick = () => {
    if (!isDirty) return;
    setValue(
      'businessHours',
      getCompanyBusinessHoursResponse?.data.businessHours || [],
    );
    setValue(
      'afterHoursMessage',
      getCompanyBusinessHoursResponse?.data.afterHoursMessage || null,
    );
    setValue(
      'calculateTicketMetricsByBusinessHours',
      getCompanyBusinessHoursResponse?.data
        .calculateTicketMetricsByBusinessHours || false,
    );
  };

  return (
    <LoadingScreen isLoading={isLoading}>
      <Box
        bg="white"
        borderRadius="md"
        border="1px solid"
        borderColor="gray.200"
        p={8}
      >
        <Heading>Horário de Atendimento</Heading>
        <Text color="gray.500" mb={8}>
          Configure os horários de disponibilidade para atendimento
        </Text>
        <Divider orientation="horizontal" mt={2} />
        <form onSubmit={handleSubmit(onSubmit)}>
          <Flex direction="column" gap={8}>
            <BusinessHoursTable
              businessHours={businessHours}
              onChange={handleBusinessHoursDataChange}
            />
            <Box>
              <FormLabel>Resposta automática</FormLabel>
              <Textarea
                placeholder="Digite aqui uma mensagem aqui"
                {...register('afterHoursMessage')}
              />
              <Text color={colors.middleGrey} size="sm" mt={2}>
                Esta mensagem será enviada automaticamente quando um cliente
                entrar em contato fora do horário de atendimento.
              </Text>
            </Box>
            <Box>
              <FormLabel>Calculo de métricas de atendimento</FormLabel>
              <HStack>
                <Checkbox
                  {...register('calculateTicketMetricsByBusinessHours')}
                />
                <Text>Usar horário de atendimento para calcular métricas</Text>
              </HStack>
              <Text color={colors.middleGrey} size="sm" mt={2}>
                As métricas de atendimento serão calculadas considerando somente
                as horas de atendimento.
              </Text>
            </Box>

            <Flex gap={2} w="100%" justifyContent="space-between">
              <Button
                variant="outline"
                onClick={handleCancelClick}
                disabled={!isDirty}
                width="30%"
                type="button"
                isDisabled={
                  !isDirty || updateCompanyBusinessHoursMutation.isLoading
                }
              >
                Cancelar
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={!isDirty}
                width="30%"
                isLoading={updateCompanyBusinessHoursMutation.isLoading}
              >
                Salvar
              </Button>
            </Flex>
          </Flex>
        </form>
      </Box>
    </LoadingScreen>
  );
};

export default FormBusinessHours;
