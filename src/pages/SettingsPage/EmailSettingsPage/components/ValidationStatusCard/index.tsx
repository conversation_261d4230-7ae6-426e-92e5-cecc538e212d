import { CopyIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Card,
  CardBody,
  Flex,
  Heading,
  Icon,
  IconButton,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Toast,
  Tr,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { CiServer } from 'react-icons/ci';
import { IoIosCheckmarkCircleOutline } from 'react-icons/io';
import { IoReload } from 'react-icons/io5';
import { MdOutlinePendingActions } from 'react-icons/md';
import { TbCircleOff } from 'react-icons/tb';
import { VscError } from 'react-icons/vsc';
import { useQueryClient } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { colors } from '../../../../../constants/colors';
import { EmailDomainsService } from '../../../../../services/email-domains.service';
import { EmailDomain } from '../../../../../types/Prisma';

export interface ValidationStatusCardProps {
  emailDomain: EmailDomain;
}

interface DnsRecord {
  name: string;
  value: string;
}

interface DnsRecords {
  mxRecords: DnsRecord[];
  txtRecords: DnsRecord[];
  cnameRecords: DnsRecord[];
}

const ValidationStatusCard = ({ emailDomain }: ValidationStatusCardProps) => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const [showDnsRecords, setShowDnsRecords] = useState(
    emailDomain.domainVerificationStatus !== 'success',
  );

  const getIconByStatus = () => {
    switch (emailDomain.domainVerificationStatus) {
      case 'success':
        return (
          <IoIosCheckmarkCircleOutline size={30} color={colors.greenMedium} />
        );
      case 'pending':
        return (
          <MdOutlinePendingActions size={30} color={colors.yellowMedium} />
        );
      case 'failed':
        return <VscError size={30} color={colors.red} />;
      default:
        return null;
    }
  };

  const textByStatusEnum = {
    success: 'Verificado',
    pending: 'Pendente',
    failed: 'Falhou',
  };

  const getDomainDetails = () => {
    return (
      <Box marginTop={5} fontSize={13}>
        <Text>Domínio: {emailDomain.domain}</Text>
        <Text>Endereço: {emailDomain.address}</Text>
        <Text>Subdomínio de envio: {emailDomain.mailFromDomain}</Text>
      </Box>
    );
  };

  async function handleDomainCheckClick() {
    await EmailDomainsService.checkEmailDomainStatus();
    queryClient.invalidateQueries(apiRoutes.listEmailDomains());
  }

  const getButtonByStatus = () => {
    if (emailDomain.domainVerificationStatus === 'pending') {
      return (
        <Button
          onClick={() => handleDomainCheckClick()}
          bgColor={colors.primary}
          color={colors.white}
        >
          <Flex gap={2} alignItems="center">
            <IoReload size={16} />
            Verificar
          </Flex>
        </Button>
      );
    }

    if (emailDomain.domainVerificationStatus === 'failed') {
      return (
        <Button
          bgColor={colors.primary}
          color={colors.white}
          as="a"
          href="https://wa.me/+5511935025879"
          target={'_blank'}
        >
          Falar com Suporte
        </Button>
      );
    }

    return (
      <Button
        bgColor={colors.primary}
        color={colors.white}
        onClick={() => setShowDnsRecords(!showDnsRecords)}
      >
        {showDnsRecords
          ? 'Ocultar Registros de DNS'
          : 'Mostrar Registros de DNS'}
      </Button>
    );
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copiado para área de transferência',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  const DnsRecordTable = ({
    title,
    records,
  }: {
    title: string;
    records: DnsRecord[];
  }) => (
    <Box mt={6}>
      <Text fontSize="md" fontWeight="medium" mb={3}>
        {title}
      </Text>
      <Card variant="outline">
        <CardBody p={0}>
          <Table variant="simple" size={'sm'}>
            <Thead>
              <Tr>
                <Th>Nome</Th>
                <Th>Valor</Th>
                <Th>Proxy</Th>
                <Th>TTL</Th>
              </Tr>
            </Thead>
            <Tbody>
              {records.map((record, index) => (
                <Tr key={index}>
                  <Td>
                    <Flex align="center" gap={2}>
                      {record.name}
                      <IconButton
                        aria-label="Copy name"
                        icon={<Icon as={CopyIcon} boxSize={4} />}
                        size="sm"
                        variant="ghost"
                        onClick={() => handleCopy(record.name)}
                      />
                    </Flex>
                  </Td>
                  <Td>
                    <Flex align="center" gap={2}>
                      {record.value}
                      <IconButton
                        aria-label="Copy value"
                        icon={<Icon as={CopyIcon} boxSize={4} />}
                        size="sm"
                        variant="ghost"
                        onClick={() => handleCopy(record.value)}
                      />
                    </Flex>
                  </Td>
                  <Td>
                    <Flex gap={2} alignItems="center">
                      <TbCircleOff /> Desativado
                    </Flex>
                  </Td>
                  <Td>Auto</Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </CardBody>
      </Card>
    </Box>
  );

  return (
    <Box>
      <Heading size="md" mb={5}>
        Status da Validação
      </Heading>
      <Box>
        <Flex justifyContent={'space-between'}>
          <Flex gap={2} alignItems="center">
            {getIconByStatus()}
            <Text fontWeight={'bold'}>
              {textByStatusEnum[emailDomain.domainVerificationStatus]}
            </Text>
          </Flex>
          {getButtonByStatus()}
        </Flex>
        {getDomainDetails()}
        {showDnsRecords && (
          <Box mt={8}>
            <Flex align="center" gap={2} mb={4}>
              <Icon as={CiServer} boxSize={5} color="gray.500" />
              <Heading size="md">Registros de DNS</Heading>
            </Flex>
            <Text color="gray.500" mb={6}>
              Adicione esses registros de DNS ao seu provedor de domínio para
              verificar a propriedade do domínio.
            </Text>

            {emailDomain.dnsRecords.mxRecords.length > 0 && (
              <DnsRecordTable
                title="Registros MX"
                records={emailDomain.dnsRecords.mxRecords}
              />
            )}
            <DnsRecordTable
              title="Registros TXT"
              records={emailDomain.dnsRecords.txtRecords}
            />
            <DnsRecordTable
              title="Registros CNAME"
              records={emailDomain.dnsRecords.cnameRecords}
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ValidationStatusCard;
