import { QuestionIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Center,
  Container,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  InputGroup,
  InputRightAddon,
  Spinner,
  Stack,
  Text,
  Tooltip,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { PiSmileySadLight } from 'react-icons/pi';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as yup from 'yup';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import { CompaniesService } from '../../../services/companies.service';
import {
  CreateEmailDomainDto,
  EmailDomainsService,
} from '../../../services/email-domains.service';
import ValidationStatusCard from './components/ValidationStatusCard';

const schema = yup
  .object()
  .shape({
    domain: yup
      .string()
      .matches(
        /^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/,
        'Domínio inválido, por favor, insira um domínio válido como: userevi.com',
      )
      .required('O domínio é obrigatório'),
    address: yup
      .string()
      .matches(/^[a-zA-Z0-9._%+-]+$/, 'Endereço inválido')
      .required('O endereço de e-mail é obrigatório'),
    mailFromDomain: yup
      .string()
      .matches(/^[a-zA-Z0-9-]+$/, 'Domínio inválido')
      .required('O domínio do remetente é obrigatório'),
  })
  .required();

const EmailSettingsPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    resolver: yupResolver(schema),
  });
  const toast = useToast();
  const queryClient = useQueryClient();

  const { data: company } = useQuery(
    apiRoutes.getCompanyDetails(),
    async () => {
      const { data } = await CompaniesService.getCompanyDetails();
      return data;
    },
  );

  const { data: emailDomain, isFetching } = useQuery(
    apiRoutes.listEmailDomains(),
    async () => {
      const { data } = await EmailDomainsService.listEmailDomains();
      return data[0];
    },
  );

  const createEmailDomain = useMutation(
    (createEmailDomainDto: CreateEmailDomainDto) =>
      EmailDomainsService.createEmailDomain(createEmailDomainDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(apiRoutes.listEmailDomains());
        toast({
          title: 'Domínio de email criado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    data.domain = data.domain.replace(/(http:\/\/|https:\/\/|www\.)/, '');
    data.address = data.address + '@' + data.domain;
    data.mailFromDomain = data.mailFromDomain + '.' + data.domain;

    const emailDomainData = {
      domain: data.domain,
      address: data.address,
      mailFromDomain: data.mailFromDomain,
    } as CreateEmailDomainDto;

    try {
      await createEmailDomain.mutateAsync(emailDomainData);
    } catch {
      toast({
        title: 'Erro ao criar domínio de email',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }

  return isFetching ? (
    <Center width="100%" height="100%">
      <Spinner />
    </Center>
  ) : (
    <Box>
      <Container minWidth={'100%'}>
        <Heading size="lg" mb={8}>
          Domínio de Email
        </Heading>
        {company?.monthlyEmailLimit ? (
          emailDomain ? (
            <ValidationStatusCard emailDomain={emailDomain} />
          ) : (
            <Stack width={'fit-content'}>
              <Box paddingBottom={5}>
                <Text>
                  Para fazer campanhas de email você deve configurar o domínio
                  de envio
                </Text>
              </Box>
              <form onSubmit={handleSubmit(onSubmit)}>
                <Stack spacing={5}>
                  <FormControl>
                    <FormLabel>Domínio</FormLabel>
                    <Input
                      placeholder="Domínio"
                      {...register('domain')}
                      isInvalid={errors.domain?.message}
                    />
                    <Text color={colors.danger} fontSize="xs">
                      {errors.domain?.message}
                    </Text>
                  </FormControl>
                  <FormControl>
                    <FormLabel>
                      <Flex gap={2} alignItems="center">
                        Endereço
                        <Tooltip
                          label={'De onde os seus emails serão enviados'}
                          placement="right"
                        >
                          <QuestionIcon boxSize={3} />
                        </Tooltip>
                      </Flex>
                    </FormLabel>
                    <InputGroup>
                      <Input
                        {...register('address')}
                        placeholder="Endereço"
                        isInvalid={errors.address?.message}
                      />
                      <InputRightAddon>
                        @{watch('domain') || 'seudominio.com'}
                      </InputRightAddon>
                    </InputGroup>
                    <Text color={colors.danger} fontSize="xs">
                      {errors.address?.message}
                    </Text>
                  </FormControl>
                  <FormControl>
                    <FormLabel>
                      <Flex gap={2} alignItems="center">
                        Subdomínio de envio
                        <Tooltip
                          label={
                            'Usado pelos provedores de email para identificar o remetente'
                          }
                          placement="right"
                        >
                          <QuestionIcon boxSize={3} />
                        </Tooltip>
                      </Flex>
                    </FormLabel>
                    <InputGroup>
                      <Input
                        {...register('mailFromDomain')}
                        placeholder="Endereço"
                        isInvalid={errors.mailFromDomain?.message}
                      />
                      <InputRightAddon>
                        .{watch('domain') || 'seudominio.com'}
                      </InputRightAddon>
                    </InputGroup>
                    <Text color={colors.danger} fontSize="xs">
                      {errors.mailFromDomain?.message}
                    </Text>
                  </FormControl>
                  <Flex justify={'flex-end'}>
                    <Button
                      width="30%"
                      color={colors.white}
                      bgColor={colors.primary}
                      type="submit"
                    >
                      Salvar
                    </Button>
                  </Flex>
                </Stack>
              </form>
            </Stack>
          )
        ) : (
          <Box>
            <Flex flexDir="column" gap={4}>
              <Flex gap={2} alignItems="start">
                <Box marginTop={'5px'}>
                  <PiSmileySadLight size={22} />
                </Box>
                <Text>
                  Parece que você ainda não tem o envio de emails ativo, fale
                  com nosso suporte.
                </Text>
              </Flex>
              <Button
                mr={4}
                bgColor={colors.primary}
                color={colors.white}
                as="a"
                href="https://wa.me/+5511935025879?text=Olá Juliana, gostaria de usar o disparador de emails da Revi"
                target={'_blank'}
                width="fit-content"
              >
                Falar com um especialista
              </Button>
            </Flex>
          </Box>
        )}
      </Container>
    </Box>
  );
};

export default EmailSettingsPage;
