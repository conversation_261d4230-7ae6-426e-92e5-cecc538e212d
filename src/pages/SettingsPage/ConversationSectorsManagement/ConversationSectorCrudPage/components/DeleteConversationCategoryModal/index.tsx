import {
  <PERSON>ton,
  Flex,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>lose<PERSON><PERSON>on,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>eader,
  ModalOverlay,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQueryClient } from 'react-query';
import {
  ConversationCategory,
  ConversationSector,
} from '../../../../../../types/Prisma';
import { apiRoutes } from '../../../../../../constants/api-routes';
import { ConversationCategoriesService } from '../../../../../../services/conversation-categories.service';
import { colors } from '../../../../../../constants/colors';

interface DeleteConversationCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationSector: ConversationSector;
  conversationCategory: ConversationCategory;
  conversationCount?: number;
}

export const DeleteConversationCategoryModal = ({
  isOpen,
  onClose,
  conversationSector,
  conversationCategory,
  conversationCount = 0,
}: DeleteConversationCategoryModalProps) => {
  const toast = useToast();
  const queryClient = useQueryClient();

  const deleteConversationCategoryMutation = useMutation(
    (conversationCategoryId: string) =>
      ConversationCategoriesService.deleteConversationCategory(
        conversationCategoryId,
      ),
    {
      onSuccess: async () => {
        toast({
          title: 'Categoria deletada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(
          apiRoutes.getConversationSector(conversationSector.id),
        );
        queryClient.invalidateQueries(apiRoutes.listConversationSectors());
        queryClient.invalidateQueries(apiRoutes.listConversationCategories());
        onClose();
      },
    },
  );

  const handleClickSave = async () => {
    const conversationCategoryId = conversationCategory.id;

    deleteConversationCategoryMutation.mutate(conversationCategoryId);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Deletar categoria</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text paddingBottom={4}>
            {conversationCount > 0 && (
              <>
                Esta conversa contem <b>{conversationCount}</b> conversas
                associadas! Elas não serão apagadas, mas serão movidas para o
                setor <b>Geral</b>.
              </>
            )}
          </Text>
          <Text paddingBottom={4}>
            Tem certeza que deseja deletar a categoria{' '}
            <b>{conversationCategory.name}</b>?
          </Text>
        </ModalBody>

        <ModalFooter>
          <Flex width="100%" justify="space-between" gap={4}>
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              isLoading={deleteConversationCategoryMutation.isLoading}
              variant="primary"
              onClick={handleClickSave}
              color={colors.white}
              bgColor={colors.red}
            >
              Deletar
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
