import {
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  Button,
  useToast,
} from '@chakra-ui/react';
import { ComponentProps, useRef } from 'react';
import { useMutation } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { ConversationSectorsService } from '../../../../../../services/conversation-sectors.service';
import { appPaths } from '../../../../../../constants/app-paths';
import { ConversationSector } from '../../../../../../types/Prisma';

interface DeleteConversationSectorDialogProps
  extends Pick<ComponentProps<typeof AlertDialog>, 'isOpen' | 'onClose'> {
  conversationSector: ConversationSector;
}

export const DeleteConversationSectorDialog = ({
  conversationSector,
  isOpen,
  onClose,
}: DeleteConversationSectorDialogProps) => {
  const toast = useToast();
  const navigate = useNavigate();
  const leastDestructiveRef = useRef<HTMLButtonElement | null>(null);

  const deleteConversationSectorMutation = useMutation(
    () =>
      ConversationSectorsService.deleteConversationSector(
        conversationSector.id,
      ),
    {
      onSuccess: () => {
        toast({
          title: 'Setor de Conversa apagado com sucesso',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        navigate(appPaths.settings.conversationSectorsManagement.index());
      },
    },
  );

  const handleClickDelete = () => {
    deleteConversationSectorMutation.mutate();
  };

  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={leastDestructiveRef}
      onClose={onClose}
    >
      <AlertDialogOverlay>
        <AlertDialogContent>
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            Confirmação de Exclusão
          </AlertDialogHeader>

          <AlertDialogBody>
            Você tem certeza de que deseja excluir o setor de conversa{' '}
            <b>{conversationSector.name}</b>? Esta ação é irreversível!
          </AlertDialogBody>

          <AlertDialogFooter>
            <Button ref={leastDestructiveRef} onClick={onClose}>
              Cancelar
            </Button>
            <Button
              colorScheme="red"
              onClick={handleClickDelete}
              ml={3}
              isLoading={deleteConversationSectorMutation.isLoading}
            >
              Confirmar
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};
