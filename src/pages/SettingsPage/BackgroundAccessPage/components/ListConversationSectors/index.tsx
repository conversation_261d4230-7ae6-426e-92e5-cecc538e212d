import { Box } from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import SectorCard from '../CardSector';
import { ConversationSectorsService } from '../../../../../services/conversation-sectors.service';
import { ConversationCategoriesService } from '../../../../../services/conversation-categories.service';
import { SECTOR_GERAL } from '../../../../../types/Sectors';
import { useCrudConversationSectorModal } from '../../../../../hooks/useCrudConversationSectorModal';

const SectorsList = () => {
  const { currentSector } = useCrudConversationSectorModal();

  const { data: sectorsData = [] } = useQuery(
    [apiRoutes.listConversationSectors(), currentSector?.id],
    async () => {
      const { data } =
        await ConversationSectorsService.listConversationSectors();
      return data;
    },
    {
      keepPreviousData: true,
    },
  );

  const { data: categoriesData = [] } = useQuery(
    [apiRoutes.listConversationCategories()],
    async () => {
      const { data } =
        await ConversationCategoriesService.listConversationCategories();
      return data;
    },
  );
  const sectorIds = new Set(sectorsData.map((s) => s.id));

  const generalCategories = categoriesData.filter(
    (cat) =>
      !cat?.conversationSector?.id || !sectorIds.has(cat.conversationSector.id),
  );

  return (
    <Box mt={6}>
      {generalCategories.length > 0 && (
        <SectorCard
          key={SECTOR_GERAL}
          sector={{
            id: SECTOR_GERAL,
            name: 'Geral - Categorias sem Setor',
            userConversationSectors: [],
            categories: generalCategories.map((cat) => ({
              ...cat,
              _count: { conversations: 0 },
              createdAt: new Date(),
              updatedAt: new Date(),
              pos: 0,
              isDeleted: false,
              companyId: '',
              automaticSortingOptionMessageId: null,
            })),
            createdAt: new Date(),
            updatedAt: new Date(),
            pos: 0,
            isDeleted: false,
            companyId: '',
            automaticSortingOptionMessageId: null,
          }}
        />
      )}

      {sectorsData.map((sector) => (
        <SectorCard key={sector.id} sector={sector} />
      ))}
    </Box>
  );
};

export default SectorsList;
