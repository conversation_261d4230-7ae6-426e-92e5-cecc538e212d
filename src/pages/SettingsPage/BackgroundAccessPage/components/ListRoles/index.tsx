import { Box, Text } from '@chakra-ui/react';
import RoleCard from '../CardRole';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { RoleWithIncludes } from '../../../../../types/Role';
import { RolesService } from '../../../../../services/roles.service';

const RolesList = () => {
  const {
    data: rolesData,
    isLoading,
    error,
  } = useQuery(apiRoutes.listRoles(), async (): Promise<RoleWithIncludes[]> => {
    const { data } = await RolesService.listRoles();
    return data;
  });

  return (
    <Box mt={6}>
      {rolesData && rolesData.length > 0
        ? rolesData.map((role, index) => <RoleCard key={index} role={role} />)
        : !isLoading && !error && <Text>Nenhum cargo encontrado.</Text>}
    </Box>
  );
};

export default RolesList;
