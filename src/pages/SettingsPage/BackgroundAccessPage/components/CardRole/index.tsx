import { RoleWithIncludes } from '../../../../../types/Role';
import { t } from 'i18next';
import { Avatar, Box, Flex, Text } from '@chakra-ui/react';
import EntityCard from '../../../../../components/CardEntity';
import { useCrudRoleModal } from '../../../../../hooks/useCrudRoleModal';
import { getModuleIcon } from '../../utils/icons';

interface RoleCardProps {
  role: RoleWithIncludes;
}

const RoleCard = ({ role }: RoleCardProps) => {
  const { openEditRoleModal, openDeleteRoleAlert } = useCrudRoleModal();

  const permissionTags =
    role.appModulePermissions && role.appModulePermissions.length > 0
      ? role.appModulePermissions.map((permission) => ({
          label: t(`enums.AppModules.${permission}`),
          icon: getModuleIcon(t(`enums.AppModules.${permission}`)),
          bgColor: 'purple.50',
          color: 'purple.700',
        }))
      : [];

  const userDetailsContent = (
    <>
      {role.users.map((user, index) => (
        <Flex
          key={index}
          align="center"
          justify="space-between"
          py={2}
          borderBottom={index < role.users.length - 1 ? '1px solid' : 'none'}
          borderColor="gray.100"
        >
          <Flex align="center">
            <Avatar
              size="sm"
              name={user.name || user.email}
              mr={3}
              bg="blue.100"
              color="blue.500"
            />
            <Box>
              <Text fontWeight="medium">{user.name || 'Sem nome'}</Text>
              <Text fontSize="sm" color="gray.500">
                {user.email}
              </Text>
            </Box>
          </Flex>
        </Flex>
      ))}
    </>
  );

  return (
    <EntityCard
      title={role.name}
      subtitle={`${role.users.length} usuário(s) • ${role.appModulePermissions?.length || 0} módulo(s)`}
      iconLetter={role.name.charAt(0)}
      iconBgColor="blue.100"
      iconColor="blue.500"
      tags={permissionTags}
      detailsTitle="Usuários com este cargo"
      detailsContent={userDetailsContent}
      onEdit={() => openEditRoleModal(role.id)}
      onDelete={() => openDeleteRoleAlert(role.id)}
    />
  );
};

export default RoleCard;
