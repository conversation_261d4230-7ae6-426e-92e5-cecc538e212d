import { Box, Grid } from '@chakra-ui/react';
import { appModules, AppModulesEnum } from '../../../../../types/AppModule';
import { t } from 'i18next';
import { getModuleIcon } from '../../utils/icons';
import { useEffect, useState } from 'react';
import CardModule from '../../../../../components/CardModule';

interface ModulesPermissionSelectorProps {
  value: string[];
  onChange: (newValue: string[]) => void;
}

const ModulesPermissionSelector = ({
  value = [],
  onChange,
}: ModulesPermissionSelectorProps) => {
  const [internalValue, setInternalValue] = useState<string[]>(value);

  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleModuleChange = (module: string, isChecked: boolean) => {
    const newValue = isChecked
      ? [...internalValue, module]
      : internalValue.filter((val) => val !== module);

    setInternalValue(newValue);
    onChange(newValue);
  };

  return (
    <Box>
      <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4}>
        {appModules
          .filter((module) => module !== AppModulesEnum.DEBUG_TOOLS)
          .map((module: AppModulesEnum) => {
            return (
              <CardModule
                key={module}
                icon={getModuleIcon(t(`enums.AppModules.${module}`))}
                label={t(`enums.AppModules.${module}`)}
                isChecked={internalValue.includes(module)}
                onChange={(e) => handleModuleChange(module, e.target.checked)}
              />
            );
          })}
      </Grid>
    </Box>
  );
};

export default ModulesPermissionSelector;
