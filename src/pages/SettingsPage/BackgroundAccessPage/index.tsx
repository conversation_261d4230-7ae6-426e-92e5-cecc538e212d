import {
  <PERSON>,
  <PERSON>ton,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON>b,
  <PERSON>b<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from '@chakra-ui/react';
import { colors } from '../../../constants/colors';
import RolesList from './components/ListRoles';
import SectorsList from './components/ListConversationSectors';
import { useCrudRoleModal } from '../../../hooks/useCrudRoleModal';
import { useCrudConversationSectorModal } from '../../../hooks/useCrudConversationSectorModal';

const BackgroundAccessPage = () => {
  const { openCreateRoleModal } = useCrudRoleModal();
  const { openCreateConversationSectorModal } =
    useCrudConversationSectorModal();
  return (
    <Box>
      <Tabs variant="unstyled">
        <TabList borderBottom="1px solid" borderColor="gray.200" mb={4}>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Gerenciar Cargos
          </Tab>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Gerenciar Setores de Conversa
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0}>
            <Flex
              width="100%"
              justifyContent="space-between"
              alignItems="flex-start"
              mb={6}
            >
              <Flex direction="column">
                <Heading>Gerenciar Cargos</Heading>
                <Text mt={2} color={colors.fontlightGrey} fontWeight="normal">
                  Defina cargos e permissões para sua equipe de cada tela da
                  Revi.
                </Text>
              </Flex>
              <Button variant="primary" onClick={openCreateRoleModal}>
                + Novo Cargo
              </Button>
            </Flex>

            <RolesList />
          </TabPanel>

          <TabPanel px={0}>
            <Flex
              width="100%"
              justifyContent="space-between"
              alignItems="flex-start"
            >
              <Flex direction="column">
                <Heading>Gerenciar Setores de Conversa</Heading>
                <Text mt={2} color={colors.fontlightGrey} fontWeight="normal">
                  Defina setores de conversas para sua equipe de cada tela da
                  Revi.
                </Text>
              </Flex>
              <Button
                variant="primary"
                onClick={openCreateConversationSectorModal}
              >
                + Criar Setor de Conversa
              </Button>
            </Flex>

            <SectorsList />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default BackgroundAccessPage;
