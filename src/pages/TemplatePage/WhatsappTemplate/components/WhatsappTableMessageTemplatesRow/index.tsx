import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Badge,
  Button,
  IconButton,
  Td,
  Text,
  Tr,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FaEdit, FaRegCopy, FaTrashAlt } from 'react-icons/fa';
import { FiSend } from 'react-icons/fi';
import { useMutation, useQueryClient } from 'react-query';
import ButtonIcon from '../../../../../components/ButtonIcon';
import { colors } from '../../../../../constants/colors';
import {
  MessageTemplate,
  MessageTemplateStatus,
} from '../../../../../types/MessageTemplate';
import MessageTemplateStatusEnum from '../../../../../types/MessageTemplateStatusEnum';
import { MessageTemplatesService } from '../../../../../services/message-templates.service';
import { apiRoutes } from '../../../../../constants/api-routes';
import { MessageTemplateTypeTextEnum } from '../../../../../types/MessageTemplateType';
import PopoverTestMessageTemplate from '../../../../../components/PopoverTestMessageTemplate';
import { TableMessageTemplatesRowProps } from '../../../components/TableMessageTemplates';

const WhatsappTableMessageTemplatesRow = ({
  template,
  onClickEditTemplate,
  onClickCloneTemplate,
}: TableMessageTemplatesRowProps) => {
  const { t } = useTranslation();
  const isDeleting =
    template.status !== MessageTemplateStatusEnum.deleted && template.isDeleted;

  const {
    isOpen: isTestTemplatePopoverOpen,
    onOpen: onOpenTestTemplatePopover,
    onClose: onCloseTestTemplatePopover,
  } = useDisclosure();
  const {
    isOpen: isMessageTemplateDeleteConfirmationOpen,
    onOpen: onOpenMessageTemplateDeleteConfirmation,
    onClose: onCloseMessageTemplateDeleteConfirmation,
  } = useDisclosure();
  const queryClient = useQueryClient();
  const toast = useToast();
  const cancelMessageTemplateDeleteRef = React.useRef<HTMLButtonElement | null>(
    null,
  );

  const deleteMessageTemplate = useMutation(
    (templateId: string) =>
      MessageTemplatesService.deleteMessageTemplate(templateId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(apiRoutes.listMessageTemplates());
        onCloseMessageTemplateDeleteConfirmation();
        toast({
          title: 'Template excluído com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
      onError: (err: any) => {
        toast({
          title: err.message,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleClickedDeleteTemplate() {
    await deleteMessageTemplate.mutateAsync(template.id);
  }

  function getColorScheme(status: MessageTemplateStatus): string {
    const statusData: Record<MessageTemplateStatus, string> = {
      approved: colors.status.approved,
      pending: colors.status.pending,
      rejected: colors.status.rejected,
      deleted: colors.status.deleted,
      disabled: colors.status.disabled,
      paused: colors.status.paused,
    };
    return statusData[isDeleting ? MessageTemplateStatusEnum.pending : status];
  }

  const closeTestTemplatePopover = useCallback(() => {
    onCloseTestTemplatePopover();
  }, [onCloseTestTemplatePopover]);

  function getMenuButtons(): JSX.Element[] {
    return isDeleting || template.status === MessageTemplateStatusEnum.deleted
      ? []
      : [
          <ButtonIcon
            icon={<FaEdit fontSize="20px" color={colors.darkGrey} />}
            onClick={() => onClickEditTemplate(template)}
          />,
          <ButtonIcon
            icon={<FaRegCopy fontSize="20px" color={colors.darkGrey} />}
            onClick={() => onClickCloneTemplate(template)}
          />,
          <PopoverTestMessageTemplate
            isOpen={isTestTemplatePopoverOpen}
            onOpen={onOpenTestTemplatePopover}
            onClose={onCloseTestTemplatePopover}
            template={template}
          >
            <IconButton
              aria-label="Send message"
              icon={<FiSend fontSize="20px" color={colors.darkGrey} />}
              backgroundColor={'transparent'}
            />
          </PopoverTestMessageTemplate>,

          <ButtonIcon
            icon={<FaTrashAlt fontSize="20px" color={colors.danger} />}
            onClick={onOpenMessageTemplateDeleteConfirmation}
          ></ButtonIcon>,
        ];
  }

  function getStatusLabel(): string {
    return isDeleting
      ? 'Deletando...'
      : t(`enums.MessageTemplateStatus.${template.status}`);
  }

  return (
    <Tr
      key={template.id}
      color={
        isDeleting || template.status === MessageTemplateStatusEnum.deleted
          ? colors.middleGrey
          : colors.black
      }
    >
      <Td fontWeight="bold">{template.name}</Td>
      <Td>
        <Text display="block" whiteSpace={'pre-wrap'}>
          {template.templateText}
        </Text>
      </Td>
      <Td>
        <Text noOfLines={1} maxW={'300px'} display="block">
          {MessageTemplateTypeTextEnum[template.type]}
        </Text>
      </Td>
      <Td>
        <Text noOfLines={1} maxW={'300px'} display="block">
          <Badge colorScheme={getColorScheme(template.status)}>
            {getStatusLabel()}
          </Badge>
        </Text>
      </Td>
      <Td>
        {getMenuButtons()}
        <AlertDialog
          isOpen={isMessageTemplateDeleteConfirmationOpen}
          leastDestructiveRef={cancelMessageTemplateDeleteRef}
          onClose={onCloseMessageTemplateDeleteConfirmation}
        >
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Confirmação de Exclusão
              </AlertDialogHeader>

              <AlertDialogBody>
                {`Você tem certeza de que deseja excluir o template de mensagem
                ${template.name}?\n`}
                <p>Atenção: Esta ação é irreversível</p>
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  ref={cancelMessageTemplateDeleteRef}
                  onClick={onCloseMessageTemplateDeleteConfirmation}
                >
                  Cancelar
                </Button>
                <Button
                  colorScheme="red"
                  onClick={handleClickedDeleteTemplate}
                  ml={3}
                  isLoading={deleteMessageTemplate.isLoading}
                >
                  Confirmar
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Td>
    </Tr>
  );
};

export default WhatsappTableMessageTemplatesRow;
