import {
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Stack,
  Text,
  Input,
  Select,
  Flex,
  Button,
  Wrap,
  HStack,
  Heading,
  Divider,
  WrapItem,
  Tooltip,
  VStack,
  useColorModeValue,
  Toast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import EmailEditor, { EditorRef, EmailEditorProps } from 'react-email-editor';
import {
  CreateEmailTemplateDto,
  EmailTemplatesService,
  ValidateEmailTemplateDto,
} from '../../../../../services/email-templates.service';
import { emailUnsubscribeURL } from '../../../../../constants/email-unsubscribe-url';
import { colors } from '../../../../../constants/colors';
import { useLocation } from 'react-router-dom';
import { <PERSON><PERSON>lertCircle, FiRefreshCw, FiUser } from 'react-icons/fi';
import { LuCheckCircle2 } from 'react-icons/lu';
import { BiXCircle } from 'react-icons/bi';
import { useMutation } from 'react-query';
import { unlayerProjectId } from '../../../../../constants/unlayer-project-id';
import { protocol } from '../../../../../constants/protocol';
import { frontEndDomain } from '../../../../../constants/frontend-domain';
import { EmailTemplate } from '../../../../../types/Prisma';
import { EmailTemplateTypeEnum } from '../../../../../types/EmailTemplateTypeEnum';
import { useTranslation } from 'react-i18next';

const schema = yup.object().shape({
  name: yup.string().required('Nome do template é obrigatório'),
  subject: yup.string().required('Assunto do email é obrigatório'),
  type: yup
    .string()
    .oneOf(Object.values(EmailTemplateTypeEnum), 'Tipo inválida')
    .required('Tipo é obrigatória'),
});

interface EmailTemplateFormProps {
  emailTemplate?: EmailTemplate;
  handleOnSubmit: (data: CreateEmailTemplateDto) => void;
}

type ValidationStatus = 'pending' | 'success' | 'failed' | 'warning';

interface ValidationResult {
  name: string;
  status: ValidationStatus;
  message: string;
  description: string;
}

const EmailTemplateForm = ({
  emailTemplate,
  handleOnSubmit,
}: EmailTemplateFormProps) => {
  const { t } = useTranslation();
  const emailEditorRef = useRef<EditorRef | null>(null);
  const [preview, setPreview] = useState(false);

  const unsubscribeLinkTool = `${protocol}://${frontEndDomain}/unsubscribeLinkTool.js`;

  const location = useLocation();
  const baseEmailTemplate = (
    location.state as { emailTemplate?: EmailTemplate }
  )?.emailTemplate;

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    getValues,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: emailTemplate?.name || '',
      subject: emailTemplate?.subject || '',
      type: emailTemplate?.type || EmailTemplateTypeEnum.MARKETING,
      html: emailTemplate?.html || '',
      text: emailTemplate?.text || '',
      unlayerDesign:
        emailTemplate?.unlayerDesign || baseEmailTemplate?.unlayerDesign || {},
    } as any,
  });

  const subjectRef = useRef<HTMLInputElement | null>(null);
  const { ref: subjectRegisterRef, ...subjectRegister } = register('subject');

  const insertConsumerNameTag = (parameter: string) => {
    const input = subjectRef.current!;
    const start = input.selectionStart ?? 0;
    const end = input.selectionEnd ?? 0;
    const subject = input.value;
    const newSubject = subject.slice(0, start) + parameter + subject.slice(end);

    setValue('subject', newSubject);

    input.focus();
    input.setSelectionRange(
      start + parameter.length + 1,
      start + parameter.length + 1,
    );
  };

  const saveDesign = () => {
    const unlayer = emailEditorRef.current?.editor;

    return new Promise<void>((resolve, reject) => {
      if (unlayer) {
        unlayer?.saveDesign((design: object) => {
          setValue('unlayerDesign', design);
          resolve();
        });
      } else {
        reject(new Error('Unlayer editor is not initialized'));
      }
    });
  };

  const exportHtml = () => {
    const unlayer = emailEditorRef.current?.editor;

    return new Promise<void>((resolve, reject) => {
      if (unlayer) {
        unlayer.exportHtml((data) => {
          const { design, html } = data;
          setValue('html', html);
          resolve();
        });
      } else {
        reject(new Error('Unlayer editor is not initialized'));
      }
    });
  };

  const exportText = () => {
    const unlayer = emailEditorRef.current?.editor;

    return new Promise<void>((resolve, reject) => {
      if (unlayer) {
        unlayer.exportPlainText((data) => {
          const { design, text } = data;
          setValue('text', text);
          resolve();
        });
      } else {
        reject(new Error('Unlayer editor is not initialized'));
      }
    });
  };

  const togglePreview = () => {
    const unlayer = emailEditorRef.current?.editor;

    if (preview) {
      unlayer?.hidePreview();
      setPreview(false);
    } else {
      unlayer?.showPreview({ device: 'desktop' });
      setPreview(true);
    }
  };

  const onReady: EmailEditorProps['onReady'] = (unlayer) => {
    if (emailTemplate) {
      unlayer.loadDesign(emailTemplate.unlayerDesign);
    }

    if (baseEmailTemplate) {
      unlayer.loadDesign(baseEmailTemplate.unlayerDesign);
    }
  };

  async function onSubmit(data: any) {
    await exportHtml();
    await exportText();
    await saveDesign();

    if (!getValues('html') || getValues('text') === '') {
      alert('Seu template está sem conteúdo');
      return;
    }

    await handleValidate();

    data['unlayerDesign'] = JSON.stringify(getValues('unlayerDesign'));
    data['html'] = getValues('html');
    data['text'] = getValues('text');

    handleOnSubmit?.(data);
  }

  const [isValidating, setIsValidating] = useState(false);
  const [validations, setValidations] = useState<ValidationResult[]>([
    {
      name: 'Link de Cancelar Inscrição',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O template precisa ter um link de cancelamento de inscrição',
    },
    {
      name: 'Tamanho do Email',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O email não deve ser muito grande para atender padrões dos provedores de email',
    },
    {
      name: 'Links',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description: 'Os links do email devem estar funcionando',
    },
    {
      name: 'Domínios Seguros',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O email não deve ter encurtadores de URL pois podem ser considerados como Spam',
    },
    {
      name: 'Texto Alternativo nas Imagens',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'Todas as imagens precisam ter textos alternativos para acessibilidade',
    },
    {
      name: 'Palavras de Spam',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O email não deve conter muitas palavras/frases que podem ser associadas a Spam, como "100% grátis" ou "promoção imperdível"',
    },
    {
      name: 'Proporção Texto-Imagem',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description: 'O email deve ter textos e imagens em proporções adequadas',
    },
    {
      name: 'Tags Proibidas',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O template não deve conter tags HTML proibidas (script, iframe, object, embed, applet, form, meta, base)',
    },
    {
      name: 'Eventos JavaScript',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O template não deve conter eventos JavaScript (onMouseOver, onClick, etc)',
    },
    {
      name: 'Links JavaScript',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description: 'O template não deve conter links com JavaScript',
    },
    {
      name: 'Tags Meta',
      status: 'pending',
      message: 'Pendente, clique para validar',
      description:
        'O template não pode conter meta tags que redirecionam ou definem cookies',
    },
  ]);

  const validateEmailTemplate = useMutation(
    async (data: ValidateEmailTemplateDto) => {
      return await (
        await EmailTemplatesService.validateEmailTemplate(data)
      ).data;
    },
    {
      onSuccess: (data) => {
        setValidations(data);
        setIsValidating(false);
      },
      onError: (error) => {
        Toast({
          title: 'Erro ao validar template',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        setIsValidating(false);
      },
    },
  );

  const handleValidate = async () => {
    setIsValidating(true);
    await exportHtml();

    await validateEmailTemplate.mutate({
      html: getValues('html'),
      type: getValues('type'),
    });
  };

  const getStatusIcon = (status: ValidationStatus) => {
    const iconColor = {
      success: 'green.500',
      failed: 'red.500',
      pending: 'gray.400',
      warning: 'yellow.500',
    }[status];

    switch (status) {
      case 'success':
        return (
          <LuCheckCircle2
            size={16}
            color={useColorModeValue(iconColor, `${iconColor}.300`)}
          />
        );
      case 'failed':
        return (
          <BiXCircle
            size={16}
            color={useColorModeValue(iconColor, `${iconColor}.300`)}
          />
        );
      default:
        return (
          <FiAlertCircle
            size={16}
            color={useColorModeValue(iconColor, `${iconColor}.300`)}
          />
        );
    }
  };

  const getStatusColor = (status: ValidationStatus) =>
    ({
      success: {
        bg: 'green.50',
        border: 'green.200',
        hover: 'green.100',
      },
      failed: {
        bg: 'red.50',
        border: 'red.200',
        hover: 'red.100',
      },
      pending: {
        bg: 'gray.50',
        border: 'gray.200',
        hover: 'gray.100',
      },
      warning: {
        bg: 'yellow.50',
        border: 'yellow.200',
        hover: 'yellow.100',
      },
    })[status];

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Stack spacing={5}>
        <Card>
          <CardBody>
            <FormControl>
              <FormLabel>Nome do Template</FormLabel>
              <Input
                placeholder="Escreva o nome do template"
                {...register('name')}
                isInvalid={!!errors.name}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <FormControl>
              <FormLabel>Assunto do Email</FormLabel>
              <VStack align="stretch" spacing={2} gap={4}>
                <Input
                  placeholder="Escreva o assunto do email"
                  {...register('subject')}
                  ref={(e) => {
                    subjectRegisterRef(e);
                    subjectRef.current = e;
                  }}
                  isInvalid={!!errors.subject}
                />
                <Button
                  leftIcon={<FiUser />}
                  size="sm"
                  onClick={() => {
                    insertConsumerNameTag('{{nome do consumidor}}');
                  }}
                  alignSelf="flex-start"
                >
                  Nome do consumidor
                </Button>
              </VStack>
              <Text color={colors.danger} fontSize="xs">
                {errors.subject?.message}
              </Text>
            </FormControl>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <FormControl>
              <FormLabel>Tipo</FormLabel>
              <Controller
                name="type"
                control={control}
                defaultValue={EmailTemplateTypeEnum.MARKETING}
                render={({ field }) => (
                  <Select onChange={field.onChange} value={field.value}>
                    {Object.values(EmailTemplateTypeEnum).map((type) => (
                      <option key={type} value={type}>
                        {t(`enums.EmailTemplateTypes.${type}`)}
                      </option>
                    ))}
                  </Select>
                )}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.type?.message}
              </Text>
            </FormControl>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <FormLabel>Template</FormLabel>
            <EmailEditor
              style={{ minHeight: '600px', height: '80vh' }}
              ref={emailEditorRef}
              onLoad={(editor) => {
                emailEditorRef.current = { editor };
              }}
              onReady={onReady}
              options={
                {
                  locale: 'pt-BR',
                  projectId: unlayerProjectId,
                  customJS: [unsubscribeLinkTool],
                  tools: {
                    'custom#unsubscribe_link': {
                      data: {
                        url: emailUnsubscribeURL,
                      },
                    },
                    column: {
                      position: 0,
                    },
                    menu: {
                      position: 1,
                    },
                    divider: {
                      position: 2,
                    },
                    heading: {
                      position: 3,
                    },
                    text: {
                      position: 4,
                    },
                    image: {
                      position: 5,
                    },
                    button: {
                      position: 6,
                    },
                    video: {
                      position: 7,
                    },
                    social: {
                      position: 8,
                    },
                    html: {
                      position: 9,
                    },
                  },
                  displayMode: 'email',
                  mergeTags: {
                    first_name: {
                      name: 'Nome do consumidor',
                      value: '{{nome do consumidor}}',
                      sample: '{{nome do consumidor}}',
                    },
                    company_name: {
                      name: 'Nome da empresa',
                      value: '{{nome da empresa}}',
                      sample: '{{nome da empresa}}',
                    },
                    custom_text: {
                      name: 'Texto personalizado',
                      value: '{{texto personalizado}}',
                      sample: '{{texto personalizado}}',
                    },
                  },
                } as EmailEditorProps
              }
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.html?.message || errors.text?.message}
            </Text>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Flex align="center" mb={3}>
              <HStack spacing={2}>
                <Heading size="sm" color="gray.700">
                  Validação do Template
                </Heading>
                <Divider orientation="vertical" h="20px" />
                <Button
                  onClick={handleValidate}
                  isLoading={isValidating}
                  loadingText="Validando"
                  spinner={<FiRefreshCw className="animate-spin" size={14} />}
                  size="sm"
                  leftIcon={
                    !isValidating ? <FiRefreshCw size={14} /> : undefined
                  }
                >
                  Validar
                </Button>
              </HStack>
            </Flex>
            <Text fontSize="sm" color="gray.500" mb={3}>
              Verifique se seu template está seguindo boas práticas de email
              marketing
            </Text>

            <Wrap spacing={2}>
              {validations.map((validation) => {
                const statusColor = getStatusColor(validation.status);

                return (
                  <WrapItem key={validation.name}>
                    <Tooltip
                      label={
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="medium">{validation.name}</Text>
                          <Text fontSize="xs" color="gray.300">
                            {validation.description}
                          </Text>
                          <Text
                            fontSize="xs"
                            color={
                              validation.status === 'success'
                                ? 'green.300'
                                : validation.status === 'failed'
                                  ? 'red.300'
                                  : 'gray.300'
                            }
                          >
                            {validation.message}
                          </Text>
                        </VStack>
                      }
                      placement="top"
                      hasArrow
                    >
                      <Flex
                        align="center"
                        gap={2}
                        px={3}
                        py={2}
                        rounded="md"
                        border="1px"
                        borderColor={statusColor.border}
                        bg={statusColor.bg}
                        _hover={{ bg: statusColor.hover }}
                        transition="background 0.2s"
                      >
                        {getStatusIcon(validation.status)}
                        <Text
                          fontSize="sm"
                          fontWeight="medium"
                          color="gray.700"
                        >
                          {validation.name}
                        </Text>
                      </Flex>
                    </Tooltip>
                  </WrapItem>
                );
              })}
            </Wrap>
          </CardBody>
        </Card>
        <Flex justify={'flex-end'}>
          <Button
            width="30%"
            color={colors.white}
            bgColor={colors.primary}
            type="submit"
          >
            Salvar
          </Button>
        </Flex>
      </Stack>
    </form>
  );
};

export default EmailTemplateForm;
