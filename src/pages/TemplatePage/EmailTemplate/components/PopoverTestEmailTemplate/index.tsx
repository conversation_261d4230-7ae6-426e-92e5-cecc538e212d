import React, { <PERSON>actNode, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>overTrigger,
  PopoverArrow,
  PopoverCloseButton,
  PopoverContent,
  Text,
} from '@chakra-ui/react';
import {
  Stack,
  FormControl,
  FormLabel,
  Input,
  ButtonGroup,
  Button,
} from '@chakra-ui/react';
import { FocusLock } from '@chakra-ui/react';
import { useMutation } from 'react-query';
import { EmailTemplate } from '../../../../../types/Prisma';
import { MessageTemplateUtils } from '../../../../../utils/message-templates.utils';
import { StringUtils } from '../../../../../utils/string.utils';
import { colors } from '../../../../../constants/colors';
import {
  EmailService,
  SendEmailTemplateTestDto,
} from '../../../../../services/email.service';

const MESSAGE_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  NONE: null,
} as const;

type EmailStatusType = (typeof MESSAGE_STATUS)[keyof typeof MESSAGE_STATUS];

export interface PopoverTestEmailTemplateProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  template: EmailTemplate;
  children?: ReactNode;
}

const PopoverTestEmailTemplate = ({
  isOpen,
  onOpen,
  onClose,
  template,
  children,
}: PopoverTestEmailTemplateProps) => {
  const [recipientName, setRecipientName] = useState<string>('');
  const [recipientEmail, setRecipientEmail] = useState<string>('');
  const [messageStatus, setEmailStatus] = useState<EmailStatusType>(
    MESSAGE_STATUS.NONE,
  );
  const sendEmailTemplateTestMutation = useMutation(
    (sendEmailTemplateByPhoneDto: SendEmailTemplateTestDto) =>
      EmailService.sendEmailTemplateTest(sendEmailTemplateByPhoneDto),
    {
      onSuccess: (res: any) => {
        setEmailStatus(MESSAGE_STATUS.SUCCESS);
      },
      onError: (error: any) => {
        setEmailStatus(MESSAGE_STATUS.ERROR);
      },
    },
  );

  useEffect(() => {
    if (isOpen) {
      setRecipientName('');
      setRecipientEmail('');
      setEmailStatus(MESSAGE_STATUS.NONE);
    }
  }, [isOpen]);

  useEffect(() => {
    if (messageStatus === MESSAGE_STATUS.SUCCESS) {
      const oneSecondInMilliseconds = 1000;
      const timeoutId = setTimeout(() => onClose(), oneSecondInMilliseconds);

      return () => clearTimeout(timeoutId);
    }
  }, [messageStatus, onClose]);

  async function handleSubmit() {
    const parameters = MessageTemplateUtils.getAllParametersInText(
      template.text,
    );

    const templateArgs: Record<string, string> = {};
    parameters.forEach((parameter) => {
      let formatted = parameter.replace(/[\[|\]]/g, '');
      formatted = StringUtils.toCamelCase(formatted);
      templateArgs[formatted] = parameter;
    });

    await sendEmailTemplateTestMutation.mutateAsync({
      recipientName,
      emailTemplateId: template.id,
      recipientEmail,
      templateArgs,
    });
  }

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      placement="right"
      closeOnBlur={true}
    >
      <PopoverTrigger>{children}</PopoverTrigger>
      <PopoverContent p={5}>
        <FocusLock persistentFocus={false}>
          <PopoverArrow />
          <PopoverCloseButton />
          <Stack>
            <FormControl>
              <FormLabel>Nome</FormLabel>
              <Input
                value={recipientName}
                onChange={(e) => setRecipientName(e.target.value)}
              />
            </FormControl>
            <FormControl>
              <FormLabel>Email</FormLabel>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={recipientEmail}
                onChange={(e) => setRecipientEmail(e.target.value)}
              />
            </FormControl>
            {messageStatus === MESSAGE_STATUS.SUCCESS ? (
              <Text color="green.500">Mensagem enviada com sucesso</Text>
            ) : messageStatus === MESSAGE_STATUS.ERROR ? (
              <Text color="red.500">Erro ao enviar mensagem</Text>
            ) : null}
            <ButtonGroup display="flex" justifyContent="flex-end">
              <Button variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button
                color={colors.white}
                bgColor={colors.primary}
                onClick={handleSubmit}
                isLoading={sendEmailTemplateTestMutation.isLoading}
                isDisabled={!recipientName || !recipientEmail}
              >
                Enviar
              </Button>
            </ButtonGroup>
          </Stack>
        </FocusLock>
      </PopoverContent>
    </Popover>
  );
};

export default PopoverTestEmailTemplate;
