import { Box, Heading, Toast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from 'react-query';
import {
  CreateEmailTemplateDto,
  EmailTemplatesService,
} from '../../../../services/email-templates.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import { useNavigate } from 'react-router-dom';
import EmailTemplateForm from '../components/EmailTemplateForm';

interface CreateEmailTemplatePageProps {
  onCreateEmailTemplate?: (messageTemplateId: string) => void;
}

const CreateEmailTemplatePage = ({
  onCreateEmailTemplate,
}: CreateEmailTemplatePageProps) => {
  const navigate = useNavigate();

  const queryClient = useQueryClient();
  const createEmailTemplate = useMutation(
    (createEmailTemplateDto: CreateEmailTemplateDto) =>
      EmailTemplatesService.createEmailTemplate(createEmailTemplateDto),
    {
      onSuccess: (data) => {
        MixpanelService.track('create-email-template', {
          messageTemplateSuggestionId: data.data.messageTemplateSuggestionId,
        });
        Toast({
          title: 'Template de Email criado com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listEmailTemplates());
        if (onCreateEmailTemplate) {
          onCreateEmailTemplate(data.data.id);
        } else {
          navigate(appPaths.messageTemplates.email.index());
        }
      },
    },
  );

  async function onSubmit(data: CreateEmailTemplateDto) {
    await createEmailTemplate.mutate(data);
  }

  return (
    <Box margin={16}>
      <Heading size="lg" marginBottom={5}>
        Criar Template de Email
      </Heading>
      <EmailTemplateForm handleOnSubmit={onSubmit} />
    </Box>
  );
};

export default CreateEmailTemplatePage;
