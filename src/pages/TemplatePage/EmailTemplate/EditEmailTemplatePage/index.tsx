import { Box, Heading, Toast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  CreateEmailTemplateDto,
  EmailTemplatesService,
} from '../../../../services/email-templates.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import { useNavigate, useParams } from 'react-router-dom';
import EmailTemplateForm from '../components/EmailTemplateForm';
import { request } from '../../../../constants/request';
import LoadingScreen from '../../../../components/LoadingScreen';

const EditEmailTemplatePage = ({}) => {
  const emailTemplateId = useParams().emailTemplateId;
  const navigate = useNavigate();

  const { data: emailTemplate, isFetching } = useQuery(
    emailTemplateId ? apiRoutes.showEmailTemplate(emailTemplateId) : '',
    async () => {
      if (!emailTemplateId) return;
      const res = await request.get(
        apiRoutes.showEmailTemplate(emailTemplateId),
      );
      return res.data;
    },
    {
      enabled: !!emailTemplateId,
    },
  );

  const queryClient = useQueryClient();
  const createEmailTemplate = useMutation(
    async (data: {
      emailTemplateId: string;
      createEmailTemplateDto: CreateEmailTemplateDto;
    }) => {
      await EmailTemplatesService.updateEmailTemplate(
        data.emailTemplateId,
        data.createEmailTemplateDto,
      );
    },
    {
      onSuccess: () => {
        // TODO-AS: Should implement the code below?
        MixpanelService.track('create-email-template', {});

        Toast({
          title: 'Template de Email atualizado com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listEmailTemplates());

        navigate(appPaths.messageTemplates.email.index());
      },
    },
  );

  async function onSubmit(data: CreateEmailTemplateDto) {
    if (!emailTemplateId) {
      return;
    }

    await createEmailTemplate.mutate({
      emailTemplateId,
      createEmailTemplateDto: data,
    });
  }

  return (
    <Box margin={16}>
      <Heading size="lg" marginBottom={5}>
        Editar Template de Email
      </Heading>
      <LoadingScreen isLoading={isFetching}>
        <EmailTemplateForm
          handleOnSubmit={onSubmit}
          emailTemplate={emailTemplate}
        />
      </LoadingScreen>
    </Box>
  );
};

export default EditEmailTemplatePage;
