import {
  <PERSON>,
  Flex,
  <PERSON>ing,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import EmailTemplates from './EmailTemplate';
import MessageTemplates from './WhatsappTemplate';
import SmsTemplates from './SmsTemplate';

interface TemplatesPageProps {
  tab?: 'mensagem' | 'email' | 'sms';
}

const TemplatesPage = ({ tab = 'mensagem' }: TemplatesPageProps) => {
  const getInitialTabIndex = (tabValue: string) => {
    switch (tabValue) {
      case 'mensagem':
        return 0;
      case 'email':
        return 1;
      case 'sms':
        return 2;
      default:
        return 0;
    }
  };

  const [tabIndex, setTabIndex] = useState(getInitialTabIndex(tab));

  useEffect(() => {
    setTabIndex(getInitialTabIndex(tab));
  }, [tab]);

  return (
    <Box padding="20px">
      <Flex width="100%" justifyContent="space-between">
        <Heading mb={5}>Templates</Heading>
      </Flex>
      <Tabs index={tabIndex} onChange={setTabIndex}>
        <TabList>
          <Tab>WhatsApp</Tab>
          <Tab>Email</Tab>
          <Tab>SMS</Tab>
        </TabList>
        <TabPanels>
          <TabPanel>
            <MessageTemplates />
          </TabPanel>
          <TabPanel>
            <EmailTemplates />
          </TabPanel>
          <TabPanel>
            <SmsTemplates />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default TemplatesPage;
