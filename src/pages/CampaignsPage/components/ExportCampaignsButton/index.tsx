import {
  useDisclosure,
  useToast,
  useBoolean,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Stack,
  Input,
  RadioGroup,
  Radio,
  Text,
} from '@chakra-ui/react';
import {
  startOfDay,
  subDays,
  startOfWeek,
  startOfMonth,
  format,
  endOfDay,
} from 'date-fns';
import { useState } from 'react';
import { TbTableExport } from 'react-icons/tb';
import ConfirmCancelButtons from '../../../../components/ConfirmCancelButtons';
import useDownloadFile from '../../../../hooks/useDownloadFile';
import { colors } from '../../../../constants/colors';

const MAX_CAMPAIGNS_TO_EXPORT = 30;

enum SelectedPeriodEnum {
  LAST_7_DAYS = 'Últimos 7 dias',
  LAST_30_DAYS = 'Últimos 30 dias',
  CURRENT_WEEK = 'Semana atual',
  PREVIOUS_WEEK = 'Semana anterior',
  CURRENT_MONTH = 'Mês atual',
}

const CURRENT_DATE = new Date();

const PREDEFINED_END_DATE = format(CURRENT_DATE, 'yyyy-MM-dd');

type PredefinedOption = {
  label: SelectedPeriodEnum;
  value: {
    startDate: string;
    endDate: string;
  };
};

const PREDEFINED_OPTIONS: Record<SelectedPeriodEnum, PredefinedOption> = {
  [SelectedPeriodEnum.LAST_7_DAYS]: {
    label: SelectedPeriodEnum.LAST_7_DAYS,
    value: {
      startDate: format(startOfDay(subDays(CURRENT_DATE, 7)), 'yyyy-MM-dd'),
      endDate: PREDEFINED_END_DATE,
    },
  },
  [SelectedPeriodEnum.LAST_30_DAYS]: {
    label: SelectedPeriodEnum.LAST_30_DAYS,
    value: {
      startDate: format(startOfDay(subDays(CURRENT_DATE, 30)), 'yyyy-MM-dd'),
      endDate: PREDEFINED_END_DATE,
    },
  },
  [SelectedPeriodEnum.CURRENT_WEEK]: {
    label: SelectedPeriodEnum.CURRENT_WEEK,
    value: {
      startDate: format(startOfWeek(CURRENT_DATE), 'yyyy-MM-dd'),
      endDate: PREDEFINED_END_DATE,
    },
  },
  [SelectedPeriodEnum.PREVIOUS_WEEK]: {
    label: SelectedPeriodEnum.PREVIOUS_WEEK,
    value: {
      startDate: format(startOfWeek(subDays(CURRENT_DATE, 7)), 'yyyy-MM-dd'),
      endDate: PREDEFINED_END_DATE,
    },
  },
  [SelectedPeriodEnum.CURRENT_MONTH]: {
    label: SelectedPeriodEnum.CURRENT_MONTH,
    value: {
      startDate: format(startOfMonth(CURRENT_DATE), 'yyyy-MM-dd'),
      endDate: PREDEFINED_END_DATE,
    },
  },
};

export interface ExportCampaignsParams {
  campaignIds?: string[];
  startDate?: string;
  endDate?: string;
}

interface ExportCampaignsButtonProps {
  selectedCampaignsToExport: Record<string, boolean>;
  isOpen: boolean;
  isLoading: boolean;
  onOpen: () => void;
  onClose: () => void;
  onExport: (exportParams: ExportCampaignsParams) => void;
}

const ExportSmsCampaignsButton = ({
  selectedCampaignsToExport,
  isOpen,
  isLoading,
  onOpen,
  onClose,
  onExport,
}: ExportCampaignsButtonProps) => {
  const toast = useToast();

  const selectedCampaignsIds = Object.keys(selectedCampaignsToExport).filter(
    (campaignId) => selectedCampaignsToExport[campaignId],
  );
  const selectedCampaignsCount = selectedCampaignsIds.length;
  const hasSelectedCampaigns = selectedCampaignsCount > 0;

  const [exportBy, setExportBy] = useState<'selectedCampaigns' | 'dateRange'>();
  const [selectedPredefinedOption, setSelectedPredefinedOption] =
    useState<PredefinedOption | null>(
      PREDEFINED_OPTIONS[SelectedPeriodEnum.LAST_7_DAYS],
    );
  const [selectedDateRange, setSelectedDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>(PREDEFINED_OPTIONS[SelectedPeriodEnum.LAST_7_DAYS].value);

  const exportParams =
    exportBy === 'selectedCampaigns'
      ? {
          campaignIds: selectedCampaignsIds,
        }
      : {
          startDate: startOfDay(
            new Date(selectedDateRange.startDate),
          ).toISOString(),
          endDate: endOfDay(new Date(selectedDateRange.endDate)).toISOString(),
        };

  const handleOpen = () => {
    if (selectedCampaignsCount > MAX_CAMPAIGNS_TO_EXPORT) {
      toast({
        title: 'Limite de exportação excedido',
        description: `Você pode exportar no máximo ${MAX_CAMPAIGNS_TO_EXPORT} campanhas por vez.`,
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setExportBy(hasSelectedCampaigns ? 'selectedCampaigns' : 'dateRange');
    onOpen();
  };

  const handleDateRangeChange = (
    dateRange: {
      startDate: string;
      endDate: string;
    },
    predefinedOption: PredefinedOption | null,
  ) => {
    setSelectedDateRange(dateRange);
    setSelectedPredefinedOption(predefinedOption);
  };

  const handleClickExportCampaigns = () => {
    onExport(exportParams);
  };

  const handleClose = () => {
    setSelectedDateRange(
      PREDEFINED_OPTIONS[SelectedPeriodEnum.LAST_7_DAYS].value,
    );
    setSelectedPredefinedOption(
      PREDEFINED_OPTIONS[SelectedPeriodEnum.LAST_7_DAYS],
    );
    onClose();
  };

  return (
    <>
      <Button
        bgColor={colors.primary}
        color={colors.white}
        leftIcon={<TbTableExport size={20} />}
        onClick={handleOpen}
        borderRadius="md"
        px={5}
        py={2}
      >
        Exportar
        {hasSelectedCampaigns && ` (${selectedCampaignsCount})`}
      </Button>

      <Modal isOpen={isOpen} onClose={handleClose} isCentered size="md">
        <ModalOverlay />
        <ModalContent borderRadius="lg">
          <ModalHeader fontWeight="bold" fontSize="lg">
            Exportar dados de campanhas
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Tabs
              isFitted
              variant="unstyled"
              onChange={(index) =>
                setExportBy(index === 0 ? 'selectedCampaigns' : 'dateRange')
              }
            >
              <TabList mb={4} gap={2}>
                {hasSelectedCampaigns && (
                  <Tab
                    fontWeight="semibold"
                    _selected={{
                      borderBottom: '2px solid',
                      borderColor: 'blue.500',
                      color: 'blue.600',
                    }}
                  >
                    Exportar selecionadas
                  </Tab>
                )}
                <Tab
                  fontWeight="semibold"
                  _selected={{
                    borderBottom: '2px solid',
                    borderColor: 'blue.500',
                    color: 'blue.600',
                  }}
                >
                  Exportar por data
                </Tab>
              </TabList>

              <TabPanels>
                {hasSelectedCampaigns && (
                  <TabPanel>
                    <Stack
                      spacing={3}
                      borderWidth="1px"
                      borderRadius="md"
                      p={4}
                      bg="gray.50"
                    >
                      <Text fontWeight="medium" fontSize="sm" color="green.600">
                        ✅ {selectedCampaignsCount} campanha(s) selecionada(s)
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        Os dados serão exportados em formato XLSX.
                      </Text>
                    </Stack>

                    <ConfirmCancelButtons
                      onConfirmClick={handleClickExportCampaigns}
                      onCancelClick={handleClose}
                      isLoading={isLoading}
                    />
                  </TabPanel>
                )}

                <TabPanel>
                  <Stack spacing={5}>
                    <Stack direction="row" spacing={4}>
                      <Stack flex={1}>
                        <Text fontWeight="medium" fontSize="sm">
                          Data inicial
                        </Text>
                        <Input
                          type="date"
                          value={selectedDateRange.startDate || ''}
                          onChange={(e) =>
                            handleDateRangeChange(
                              {
                                ...selectedDateRange,
                                startDate: e.target.value,
                              },
                              null,
                            )
                          }
                          bg="white"
                          borderColor="gray.300"
                          _focus={{
                            borderColor: 'blue.500',
                            boxShadow: '0 0 0 1px #3182ce',
                          }}
                        />
                      </Stack>
                      <Stack flex={1}>
                        <Text fontWeight="medium" fontSize="sm">
                          Data final
                        </Text>
                        <Input
                          type="date"
                          value={selectedDateRange.endDate || ''}
                          onChange={(e) =>
                            handleDateRangeChange(
                              {
                                ...selectedDateRange,
                                endDate: e.target.value,
                              },
                              null,
                            )
                          }
                          bg="white"
                          borderColor="gray.300"
                          _focus={{
                            borderColor: 'blue.500',
                            boxShadow: '0 0 0 1px #3182ce',
                          }}
                        />
                      </Stack>
                    </Stack>

                    <RadioGroup
                      onChange={(value: SelectedPeriodEnum) =>
                        handleDateRangeChange(
                          PREDEFINED_OPTIONS[value].value,
                          PREDEFINED_OPTIONS[value],
                        )
                      }
                      value={selectedPredefinedOption?.label}
                    >
                      <Stack spacing={2}>
                        {Object.values(PREDEFINED_OPTIONS).map(({ label }) => (
                          <Radio
                            key={label}
                            value={label}
                            colorScheme="blue"
                            _checked={{
                              bg: 'blue.100',
                              borderColor: 'blue.500',
                            }}
                          >
                            <Text fontSize="sm">{label}</Text>
                          </Radio>
                        ))}
                      </Stack>
                    </RadioGroup>

                    <ConfirmCancelButtons
                      onConfirmClick={handleClickExportCampaigns}
                      onCancelClick={handleClose}
                      isLoading={isLoading}
                    />
                  </Stack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default ExportSmsCampaignsButton;
