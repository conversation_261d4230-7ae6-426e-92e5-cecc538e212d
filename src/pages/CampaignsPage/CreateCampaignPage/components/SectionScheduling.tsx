import {
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Card<PERSON>eader,
  <PERSON>lex,
  <PERSON><PERSON>,
  Tooltip,
  useBoolean,
} from '@chakra-ui/react';
import React, { useEffect } from 'react';
import ReactDatePicker from 'react-datepicker';
import { CiClock2 } from 'react-icons/ci';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { colors } from '../../../../constants/colors';
import { setScheduledExecutionTime } from '../../../../state/campaignCreationSlice';
import { RootState } from '../../../../state/store';

const SectionScheduling = () => {
  const [isScheduling, setIsScheduling] = useBoolean();
  const { scheduledExecutionTime, selectedTemplate } = useSelector(
    (state: RootState) => state.campaignCreation,
  );
  const dispatcher = useDispatch();
  const isTemplatePending = selectedTemplate?.status === 'pending';

  useEffect(() => {
    if (isTemplatePending) {
      handleClickSchedule();
    }
  }, [selectedTemplate?.id]);

  function handleChangeScheduledExecutionTime(date: Date | null) {
    dispatcher(setScheduledExecutionTime(date ? date.toISOString() : null));
  }

  function handleClickSendNow() {
    dispatcher(setScheduledExecutionTime(null));
    setIsScheduling.off();
  }
  function handleClickSchedule() {
    dispatcher(setScheduledExecutionTime(new Date().toISOString()));
    setIsScheduling.on();
  }

  return (
    <Card>
      <CardHeader
        paddingBottom={0}
        display="flex"
        justifyContent={'space-between'}
      >
        <Heading size="md" display={'flex'} gap={3} alignItems="center">
          <CiClock2 size={20} />
          Horário de envio
        </Heading>
        <Flex alignItems="center">
          <Tooltip
            label={
              isTemplatePending
                ? 'Não é possível enviar a campanha agora, pois o template selecionado está com status "pendente"'
                : ''
            }
          >
            <Button
              borderRadius={0}
              color={!isScheduling ? colors.white : colors.primary}
              bgColor={!isScheduling ? colors.primary : colors.white}
              borderColor={!isScheduling ? colors.white : colors.primary}
              borderStyle={!isScheduling ? 'none' : 'solid'}
              borderWidth="1px"
              isDisabled={isTemplatePending}
              onClick={handleClickSendNow}
            >
              Enviar agora
            </Button>
          </Tooltip>
          <Button
            borderRadius={0}
            color={isScheduling ? colors.white : colors.primary}
            bgColor={isScheduling ? colors.primary : colors.white}
            borderColor={isScheduling ? colors.white : colors.primary}
            borderStyle={isScheduling ? 'none' : 'solid'}
            borderWidth="1px"
            onClick={handleClickSchedule}
          >
            Agendar para
          </Button>
        </Flex>
      </CardHeader>
      <CardBody display="flex" justifyContent={'space-between'}>
        <div />
        <div>
          {isScheduling && (
            <ReactDatePicker
              selected={
                scheduledExecutionTime ? new Date(scheduledExecutionTime) : null
              }
              onChange={handleChangeScheduledExecutionTime}
              minDate={new Date()}
              dateFormat="dd/MM/yyyy HH:mm"
              showTimeSelect
              isClearable
              timeIntervals={10}
            />
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default SectionScheduling;
