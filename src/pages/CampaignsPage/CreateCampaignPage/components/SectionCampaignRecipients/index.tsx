import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  IconButton,
  Text,
  useBoolean,
  VStack,
} from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import { FaEdit } from 'react-icons/fa';
import { IoPeopleOutline } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import InputNumber from '../../../../../components/InputNumber';
import TableFilters from '../../../../../components/TableFilters';
import { appPaths } from '../../../../../constants/app-paths';
import { colors } from '../../../../../constants/colors';
import { MAX_WHATSAPP_CAMPAIGN_RECIPIENTS } from '../../../../../constants/max-whatsapp-campaign-recipients';
import { queryStringDelimiter } from '../../../../../constants/query-string-delimiter';
import {
  finishCampaignCreation,
  setShowSelectCustomerRows,
} from '../../../../../state/campaignCreationSlice';
import { RootState } from '../../../../../state/store';
import { CommunicationChannelEnum } from '../../../../../types/CommunicationChannelEnum';
import { CustomerFiltersEnum } from '../../../../../types/CustomerFiltersEnum';
import { UrlUtils } from '../../../../../utils/url.utils';

const DEFAULT_MIN_DAYS_SINCE_LAST_PURCHASE = 21;
const DEFAULT_MIN_DAYS_SINCE_LAST_CAMPAIGN = 14;
const DEFAULT_MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN = 0;

const SectionCampaignRecipients = () => {
  const navigate = useNavigate();
  const {
    selectedCustomerRows,
    filterCriteria,
    selectedTemplate,
    selectedEmailTemplate,
    variants,
    campaignRecommendation,
    communicationChannel,
  } = useSelector((state: RootState) => state.campaignCreation);
  const maxCampaignRecipients =
    communicationChannel !== CommunicationChannelEnum.EMAIL
      ? MAX_WHATSAPP_CAMPAIGN_RECIPIENTS
      : Object.keys(selectedCustomerRows).length;
  const selectedCustomerIds = Object.keys(selectedCustomerRows).slice(
    0,
    maxCampaignRecipients,
  );
  const dispatch = useDispatch();
  const [isFillingForm, setIsFillingForm] = useBoolean(false);
  const [minDaysSinceLastPurchase, setMinDaysSinceLastPurchase] =
    useState<number>(DEFAULT_MIN_DAYS_SINCE_LAST_PURCHASE);
  const [minDaysSinceLastCampaign, setMinDaysSinceLastCampaign] =
    useState<number>(DEFAULT_MIN_DAYS_SINCE_LAST_CAMPAIGN);
  const [minDaysSinceLastEmailCampaign, setMinDaysSinceLastEmailCampaign] =
    useState<number>(DEFAULT_MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN);
  const shouldFinishCampaignCreationOnUnmountRef = useRef(true);

  useEffect(() => {
    shouldFinishCampaignCreationOnUnmountRef.current = true;
    if (campaignRecommendation) {
      const recommendationFilters = UrlUtils.convertQueryStringToObject(
        campaignRecommendation.data.filterCriteria,
      );
      setMinDaysSinceLastCampaign(
        recommendationFilters[
          CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_CAMPAIGN
        ] ?? DEFAULT_MIN_DAYS_SINCE_LAST_CAMPAIGN,
      );
      setMinDaysSinceLastEmailCampaign(
        recommendationFilters[
          CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN
        ] ?? DEFAULT_MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN,
      );
      setMinDaysSinceLastPurchase(
        recommendationFilters[
          CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PURCHASE
        ] ?? DEFAULT_MIN_DAYS_SINCE_LAST_PURCHASE,
      );
    }
  }, []);

  useEffect(() => {
    return () => {
      if (shouldFinishCampaignCreationOnUnmountRef.current) {
        dispatch(finishCampaignCreation());
      }
    };
  }, [shouldFinishCampaignCreationOnUnmountRef, dispatch]);

  function handleClickSelectCustomers() {
    setIsFillingForm.on();
  }

  function handleClickEditSelectedCustomers() {
    shouldFinishCampaignCreationOnUnmountRef.current = false;
    dispatch(setShowSelectCustomerRows(true));
    if (communicationChannel !== CommunicationChannelEnum.EMAIL) {
      const excludedTemplateIds = [
        selectedTemplate!.id,
        ...variants.map((variant) => variant.templateId),
      ].join(queryStringDelimiter);

      navigate({
        pathname: appPaths.customers.index(),
        search: filterCriteria
          ? filterCriteria
          : `excludedTemplateIds=${excludedTemplateIds}`,
      });
    }

    navigate({
      pathname: appPaths.customers.index(),
      search: filterCriteria
        ? filterCriteria
        : `excludedEmailTemplateIds=${selectedEmailTemplate!.id}`,
    });
  }

  function getExcludedTemplateIds() {
    if (communicationChannel === CommunicationChannelEnum.WHATSAPP) {
      return [
        selectedTemplate!.id,
        ...variants.map((variant) => variant.templateId),
      ].join(queryStringDelimiter);
    }
  }

  function handleClickContinue() {
    shouldFinishCampaignCreationOnUnmountRef.current = false;
    let filters = {};

    if (campaignRecommendation) {
      filters = UrlUtils.convertQueryStringToObject(
        campaignRecommendation.data.filterCriteria,
      );
    }

    const excludedTemplateIds = getExcludedTemplateIds();

    filters = {
      ...filters,
      [CustomerFiltersEnum.HAS_EMAIL]:
        communicationChannel === CommunicationChannelEnum.EMAIL,
      [CustomerFiltersEnum.HAS_PHONE_NUMBER_ID]:
        communicationChannel !== CommunicationChannelEnum.EMAIL,
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_CAMPAIGN]:
        minDaysSinceLastCampaign,
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN]:
        minDaysSinceLastEmailCampaign,
      [CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PURCHASE]:
        minDaysSinceLastPurchase,
      [CustomerFiltersEnum.SELECTED_CAMPAIGN_CHANNEL]: communicationChannel,
      excludedTemplateIds,
      excludedEmailTemplateIds: selectedEmailTemplate?.id,
    };

    const queryString = UrlUtils.convertObjectToQueryString(filters);
    dispatch(setShowSelectCustomerRows(true));
    navigate({
      pathname: appPaths.customers.index(),
      search: `${queryString}`,
    });
  }

  function handleOnChangeMinDaysSinceLastEmailOrWhatsAppCampaign(
    value: number,
  ) {
    if (communicationChannel === CommunicationChannelEnum.EMAIL) {
      setMinDaysSinceLastEmailCampaign(value);
    } else {
      setMinDaysSinceLastCampaign(value);
    }
  }

  useEffect(() => {
    if (communicationChannel === CommunicationChannelEnum.EMAIL) {
      setMinDaysSinceLastCampaign(0);
      setMinDaysSinceLastEmailCampaign(0);
    } else {
      setMinDaysSinceLastEmailCampaign(0);
      setMinDaysSinceLastCampaign(DEFAULT_MIN_DAYS_SINCE_LAST_CAMPAIGN);
    }
  }, [communicationChannel]);

  return (
    <Card>
      <CardHeader
        paddingBottom={0}
        display="flex"
        justifyContent={'space-between'}
      >
        <Heading size="md" display={'flex'} gap={3} alignItems="center">
          <IoPeopleOutline size={20} />
          Destinatários
        </Heading>
        {isFillingForm ? null : selectedCustomerIds.length === 0 ? (
          <Button
            onClick={handleClickSelectCustomers}
            isDisabled={!selectedTemplate && !selectedEmailTemplate}
          >
            Selecionar audiência
          </Button>
        ) : (
          <Flex alignItems={'center'}>
            <Text>{selectedCustomerIds.length} clientes selecionados</Text>
            <IconButton
              aria-label="Alterar clientes selecionados"
              variant={'ghost'}
              onClick={handleClickEditSelectedCustomers}
              icon={<FaEdit fontSize={16} />}
            />
          </Flex>
        )}
      </CardHeader>
      <CardBody>
        {filterCriteria && <TableFilters filterCriteria={filterCriteria} />}
        {isFillingForm && (
          <Flex flexDir={'column'} gap={5}>
            {campaignRecommendation && (
              <Alert
                status="info"
                variant="subtle"
                width="100%"
                maxWidth="500px"
                p={4}
                mb={4}
                borderRadius="md"
                boxShadow="sm"
                aria-live="polite"
              >
                <AlertIcon />
                <Box>
                  <AlertTitle fontSize="sm" fontWeight="medium">
                    Recomendação ativada: {campaignRecommendation.title}
                  </AlertTitle>
                  <AlertDescription fontSize="xs" mt={1}>
                    Aplicamos filtros automáticos com base nesta recomendação.
                    Revise-os e ajuste conforme necessário.
                  </AlertDescription>
                </Box>
              </Alert>
            )}
            <FormControl isRequired>
              <FormLabel fontSize={'sm'}>
                Ocultar clientes que <u>receberam campanha</u> nos últimos
              </FormLabel>
              <InputNumber
                size="sm"
                rightAddon="dias"
                value={
                  communicationChannel === CommunicationChannelEnum.EMAIL
                    ? minDaysSinceLastEmailCampaign
                    : minDaysSinceLastCampaign
                }
                onChange={(value) => {
                  handleOnChangeMinDaysSinceLastEmailOrWhatsAppCampaign(value);
                }}
                maxWidth="600px"
              />
            </FormControl>
            <FormControl>
              <FormLabel fontSize={'sm'}>
                Ocultar clientes que <u>compraram</u> nos últimos
              </FormLabel>
              <InputNumber
                size="sm"
                rightAddon="dias"
                value={minDaysSinceLastPurchase}
                onChange={(value) => setMinDaysSinceLastPurchase(value)}
                maxWidth="600px"
              />
            </FormControl>
            <Flex width="100%" justifyContent={'space-between'}>
              <div />
              <Button
                onClick={handleClickContinue}
                color={colors.white}
                bgColor={colors.primary}
              >
                Continuar
              </Button>
            </Flex>
          </Flex>
        )}
      </CardBody>
    </Card>
  );
};

export default SectionCampaignRecipients;
