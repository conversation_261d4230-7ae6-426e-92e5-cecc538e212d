import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ody,
  Card<PERSON>eader,
  <PERSON>lex,
  <PERSON><PERSON>,
  IconButton,
  Text,
} from '@chakra-ui/react';
import { useEffect, useReducer } from 'react';
import { FaEdit } from 'react-icons/fa';
import { MdOutlineEmail } from 'react-icons/md';
import { useDispatch, useSelector } from 'react-redux';
import {
  resetTemplateArgs,
  updateSelectedEmailTemplate,
  updateTemplateArg,
} from '../../../../../state/campaignCreationSlice';
import { RootState } from '../../../../../state/store';
import { CompanyDefinedField } from '../../../../../types/CompanyDefinedField';
import { EmailTemplate } from '../../../../../types/Prisma';
import EmailTemplateParameterEditor from './components/EmailTemplateParameterEditor';
import EmailTemplateSelector from './components/EmailTemplateSelector';

interface SelectionState {
  status: 'selecting' | 'selected' | 'notSelected';
}

const initialSelectionState: SelectionState = {
  status: 'notSelected',
};

enum SelectionActionTypes {
  NOT_SELECTED = 'NOT_SELECTED',
  SELECTING = 'SELECTING',
  SELECTED = 'SELECTED',
}

interface SelectionAction {
  type: SelectionActionTypes;
}

const selectionReducer = (
  state: SelectionState,
  action: SelectionAction,
): SelectionState => {
  switch (action.type) {
    case SelectionActionTypes.SELECTING:
      return { ...state, status: 'selecting' };
    case SelectionActionTypes.SELECTED:
      return { ...state, status: 'selected' };
    case SelectionActionTypes.NOT_SELECTED:
      return { ...state, status: 'notSelected' };
    default:
      return state;
  }
};

interface SectionSelectEmailTemplateProps {
  emailTemplates: EmailTemplate[];
  companyDefinedFields: CompanyDefinedField[];
  refetchTemplates: () => void;
}

const SectionSelectEmailTemplate = ({
  emailTemplates,
  companyDefinedFields,
  refetchTemplates,
}: SectionSelectEmailTemplateProps) => {
  const [selectionState, dispatchSelectionState] = useReducer(
    selectionReducer,
    initialSelectionState,
  );
  const { selectedEmailTemplate, templateArgs, communicationChannel } =
    useSelector((state: RootState) => state.campaignCreation);
  const dispatch = useDispatch();

  useEffect(() => {
    if (selectedEmailTemplate) {
      dispatchSelectionState({
        type: SelectionActionTypes.SELECTED,
      });
    }
  }, [selectedEmailTemplate]);

  function clearSelectedTemplate() {
    dispatch(updateSelectedEmailTemplate(null));
    dispatch(resetTemplateArgs);
  }

  function handleClickTemplate(template: EmailTemplate) {
    dispatchSelectionState({
      type: SelectionActionTypes.SELECTED,
    });
    dispatch(updateSelectedEmailTemplate(template));
  }

  function handleClickEditTemplate() {
    dispatchSelectionState({
      type: SelectionActionTypes.SELECTING,
    });
    clearSelectedTemplate();
  }

  function handleClickSelectTemplate() {
    dispatchSelectionState({
      type: SelectionActionTypes.SELECTING,
    });
  }

  function handleChangeTemplateArgs(args: Record<string, string>) {
    dispatch(updateTemplateArg(args));
  }

  return (
    <Card>
      <CardHeader display="flex" justifyContent="space-between">
        <Heading size="md" display="flex" gap={3} alignItems="center">
          <MdOutlineEmail size={18} />
          Email
        </Heading>
        {selectionState.status === 'selected' ? (
          <Flex alignItems={'center'} gap={2}>
            <Text>{selectedEmailTemplate?.name}</Text>
            <IconButton
              aria-label="Alterar template selecionado"
              variant={'ghost'}
              onClick={handleClickEditTemplate}
              icon={<FaEdit fontSize={16} />}
            />
          </Flex>
        ) : selectionState.status === 'notSelected' ? (
          <Button onClick={handleClickSelectTemplate}>
            Selecionar template
          </Button>
        ) : null}
      </CardHeader>
      <CardBody paddingTop={0}>
        {selectionState.status === 'selecting' ? (
          <EmailTemplateSelector
            emailTemplates={emailTemplates}
            onClickTemplate={handleClickTemplate}
            selectedEmailTemplate={selectedEmailTemplate}
            communicationChannel={communicationChannel}
          />
        ) : selectedEmailTemplate && selectionState.status === 'selected' ? (
          <EmailTemplateParameterEditor
            emailTemplate={selectedEmailTemplate}
            onChangeTemplateArgs={handleChangeTemplateArgs}
            emailTemplateArgs={templateArgs}
            companyDefinedFields={companyDefinedFields}
          />
        ) : null}
      </CardBody>
    </Card>
  );
};

export default SectionSelectEmailTemplate;
