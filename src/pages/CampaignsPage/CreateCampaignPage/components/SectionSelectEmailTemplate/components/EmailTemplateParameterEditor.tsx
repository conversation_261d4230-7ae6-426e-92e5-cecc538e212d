import {
  Box,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Switch,
  Text,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import EmailTemplatePreview from '../../../../../../components/EmailTemplatePreview';
import { CompanyDefinedField } from '../../../../../../types/CompanyDefinedField';
import { EmailTemplate } from '../../../../../../types/Prisma';
import { EmailTemplateUtils } from '../../../../../../utils/email-template.utils';

interface EmailTemplateParameterEditorProps {
  emailTemplate: EmailTemplate;
  onChangeTemplateArgs: (args: Record<string, string>) => void;
  emailTemplateArgs: Record<string, string>;
  companyDefinedFields: CompanyDefinedField[];
}

const EmailTemplateParameterEditor = ({
  emailTemplate,
  onChangeTemplateArgs,
  emailTemplateArgs,
  companyDefinedFields,
}: EmailTemplateParameterEditorProps) => {
  const [dynamicInputFields, setDynamicInputFields] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    Object.entries(emailTemplateArgs).map(([key, value]) => {
      if (value.startsWith('@')) {
        setDynamicInputFields((prev) => {
          return {
            ...prev,
            [key]: true,
          };
        });
      }
    });
  }, [emailTemplateArgs]);

  useEffect(() => {
    return () => {
      setDynamicInputFields({});
    };
  }, []);

  function handleChangeDynamicInputField(param: string) {
    onChangeTemplateArgs({
      [param]: '',
    });
    setDynamicInputFields((prev) => {
      return {
        ...prev,
        [param]: !prev[param],
      };
    });
  }

  function handleChangeTemplateParameter(parameter: string, value: string) {
    onChangeTemplateArgs({
      [parameter]: value,
    });
  }

  const customParameters = EmailTemplateUtils.getCustomParametersInText(
    emailTemplate?.text || '',
  );

  return (
    <Flex gap={5}>
      <Box>
        <EmailTemplatePreview emailTemplate={emailTemplate} />
      </Box>
      <Box flex={1}>
        <Text fontWeight={'bold'}>Parâmetros customizados</Text>
        {customParameters.length === 0 ? (
          <Box>
            <Text>Não há parâmetros customizados</Text>
          </Box>
        ) : (
          <Box>
            {customParameters.map((param) => (
              <FormControl isRequired>
                <FormLabel>
                  {param.replaceAll('{{', '').replaceAll('}}', '')}
                </FormLabel>
                <FormControl display="flex" alignItems="center" mb={1}>
                  <FormLabel mb={0} fontSize={'12px'}>
                    Usar valor da coluna?
                  </FormLabel>
                  <Switch
                    isChecked={dynamicInputFields[param]}
                    size="sm"
                    onChange={() => handleChangeDynamicInputField(param)}
                  />
                </FormControl>
                {dynamicInputFields[param] ? (
                  <Select
                    placeholder="Selecione uma coluna"
                    onChange={(e) =>
                      handleChangeTemplateParameter(param, e.target.value)
                    }
                    defaultValue={emailTemplateArgs[param]}
                  >
                    {companyDefinedFields.map((companyDefinedField) => (
                      <option
                        key={companyDefinedField.id}
                        value={`@${companyDefinedField.name}`}
                      >
                        {companyDefinedField.name}
                      </option>
                    ))}
                  </Select>
                ) : (
                  <Input
                    onChange={(e) =>
                      handleChangeTemplateParameter(param, e.target.value)
                    }
                    value={emailTemplateArgs[param] || ''}
                    required
                  />
                )}
              </FormControl>
            ))}
          </Box>
        )}
      </Box>
    </Flex>
  );
};

export default EmailTemplateParameterEditor;
