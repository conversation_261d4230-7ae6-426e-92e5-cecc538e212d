import { Box, HStack, Input } from '@chakra-ui/react';
import React, { useState } from 'react';
import { EmailTemplate } from '../../../../../../types/Prisma';
import { CommunicationChannelEnum } from '../../../../../../types/CommunicationChannelEnum';
import EmailTemplatePreview from '../../../../../../components/EmailTemplatePreview';

interface EmailTemplateSelectorProps {
  emailTemplates: EmailTemplate[];
  selectedEmailTemplate: EmailTemplate | null;
  onClickTemplate: (template: EmailTemplate) => void;
  communicationChannel: CommunicationChannelEnum;
  showSearch?: boolean;
}

const EmailTemplateSelector = ({
  emailTemplates,
  selectedEmailTemplate,
  onClickTemplate,
  showSearch = true,
}: EmailTemplateSelectorProps) => {
  const [searchQuery, setSearchQuery] = useState('');

  function handleChangeSearchQuery(event: React.ChangeEvent<HTMLInputElement>) {
    setSearchQuery(event.target.value);
  }

  const filteredTemplates = emailTemplates.filter((template) => {
    return template.name.includes(searchQuery);
  });

  return (
    <Box width="-webkit-fill-available">
      {showSearch && (
        <Input
          placeholder="Pesquisar por nome"
          onChange={handleChangeSearchQuery}
          value={searchQuery}
          marginBottom={4}
        />
      )}
      <HStack spacing={2} overflowX="scroll">
        {filteredTemplates.map((template) => {
          return (
            <EmailTemplatePreview
              key={template.id}
              emailTemplate={template}
              onSelect={onClickTemplate}
              selectedEmailTemplate={selectedEmailTemplate}
            />
          );
        })}
      </HStack>
    </Box>
  );
};

export default EmailTemplateSelector;
