import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Switch,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { addMinutes } from 'date-fns';
import { useEffect, useState } from 'react';
import { FiSend } from 'react-icons/fi';
import { useQuery } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import PopoverTestMessageTemplate from '../../../../../components/PopoverTestMessageTemplate';
import WhatsappTemplatePreview from '../../../../../components/WhatsappTemplatePreview';
import { apiRoutes } from '../../../../../constants/api-routes';
import { GUPSHUP_TEMPLATE_CREATION_DELAY_IN_MINUTES } from '../../../../../constants/gupshup-template-creation-delay-in-minutes';
import useCountdown from '../../../../../hooks/useCountdown';
import {
  ListMessageTemplateItem,
  MessageTemplatesService,
} from '../../../../../services/message-templates.service';
import { updateSelectedMessageTemplateStatus } from '../../../../../state/campaignCreationSlice';
import { RootState } from '../../../../../state/store';
import { CompanyDefinedField } from '../../../../../types/CompanyDefinedField';
import { MessageTemplateUtils } from '../../../../../utils/message-templates.utils';

interface TemplateParameterEditorProps {
  template: ListMessageTemplateItem;
  onChangeTemplateArgs: (args: Record<string, string>) => void;
  templateArgs: Record<string, string>;
  companyDefinedFields: CompanyDefinedField[];
}

const TemplateParameterEditor = ({
  template,
  onChangeTemplateArgs,
  templateArgs,
  companyDefinedFields,
}: TemplateParameterEditorProps) => {
  const [dynamicInputFields, setDynamicInputFields] = useState<
    Record<string, boolean>
  >({});
  const {
    isOpen: isTestTemplatePopoverOpen,
    onOpen: onOpenTestTemplatePopover,
    onClose: onCloseTestTemplatePopover,
  } = useDisclosure();
  const dispatch = useDispatch();
  const { selectedTemplate } = useSelector(
    (state: RootState) => state.campaignCreation,
  );

  const tartgetCountdownDate = selectedTemplate?.createdAt
    ? addMinutes(
        new Date(selectedTemplate.createdAt),
        GUPSHUP_TEMPLATE_CREATION_DELAY_IN_MINUTES,
      ).toISOString()
    : null;
  const timeLeft = useCountdown(tartgetCountdownDate);

  useQuery(
    apiRoutes.showMessageTemplate(selectedTemplate!.id),
    async () => {
      const { data } = await MessageTemplatesService.showMessageTemplate(
        selectedTemplate!.id,
      );
      return data;
    },
    {
      enabled: !!selectedTemplate,
      refetchInterval: selectedTemplate?.status === 'pending' ? 15000 : false,
      onSuccess: (data) => {
        if (selectedTemplate) {
          dispatch(updateSelectedMessageTemplateStatus(data.status));
        }
      },
    },
  );

  useEffect(() => {
    Object.entries(templateArgs).map(([key, value]) => {
      if (value.startsWith('@')) {
        setDynamicInputFields((prev) => {
          return {
            ...prev,
            [key]: true,
          };
        });
      }
    });
  }, [templateArgs]);

  useEffect(() => {
    return () => {
      setDynamicInputFields({});
    };
  }, []);

  function handleChangeDynamicInputField(param: string) {
    onChangeTemplateArgs({
      [param]: '',
    });
    setDynamicInputFields((prev) => {
      return {
        ...prev,
        [param]: !prev[param],
      };
    });
  }

  function handleChangeTemplateParameter(parameter: string, value: string) {
    onChangeTemplateArgs({
      [parameter]: value,
    });
  }

  const customParameters = MessageTemplateUtils.getCustomParametersInText(
    template?.templateText || '',
  );

  return (
    <Flex gap={5}>
      <Box>
        <WhatsappTemplatePreview
          message={template.templateText}
          footer={template.footerText}
          buttons={template.messageTemplateButtons}
          fileUrl={template.mediaUrl}
          height={'60vh'}
        />
        <PopoverTestMessageTemplate
          isOpen={isTestTemplatePopoverOpen}
          onOpen={onOpenTestTemplatePopover}
          onClose={onCloseTestTemplatePopover}
          template={template}
        >
          <Button
            mt={3}
            leftIcon={<FiSend />}
            isDisabled={selectedTemplate?.status === 'pending'}
          >
            Testar envio
          </Button>
        </PopoverTestMessageTemplate>
        {selectedTemplate?.status === 'pending' && (
          <Text fontSize={'sm'} mt={2} color="red.400">
            * Aguarde a aprovação do template para testar o envio.
            <br />
            {timeLeft && `Aprovação em ${timeLeft.minutes}min ${timeLeft.seconds}s`}
          </Text>
        )}
      </Box>
      <Box flex={1}>
        <Text fontWeight={'bold'}>Parâmetros customizados</Text>
        {customParameters.length === 0 ? (
          <Box>
            <Text>Não há parâmetros customizados</Text>
          </Box>
        ) : (
          <Box>
            {customParameters.map((param) => (
              <FormControl isRequired>
                <FormLabel>
                  {param.replaceAll('[', '').replaceAll(']', '')}
                </FormLabel>
                <FormControl display="flex" alignItems="center" mb={1}>
                  <FormLabel mb={0} fontSize={'12px'}>
                    Usar valor da coluna?
                  </FormLabel>
                  <Switch
                    isChecked={dynamicInputFields[param]}
                    size="sm"
                    onChange={() => handleChangeDynamicInputField(param)}
                  />
                </FormControl>
                {dynamicInputFields[param] ? (
                  <Select
                    placeholder="Selecione uma coluna"
                    onChange={(e) =>
                      handleChangeTemplateParameter(param, e.target.value)
                    }
                    defaultValue={templateArgs[param]}
                  >
                    {companyDefinedFields.map((companyDefinedField) => (
                      <option
                        key={companyDefinedField.id}
                        value={`@${companyDefinedField.name}`}
                      >
                        {companyDefinedField.name}
                      </option>
                    ))}
                  </Select>
                ) : (
                  <Input
                    onChange={(e) =>
                      handleChangeTemplateParameter(param, e.target.value)
                    }
                    value={templateArgs[param] || ''}
                    required
                  />
                )}
              </FormControl>
            ))}
          </Box>
        )}
      </Box>
    </Flex>
  );
};

export default TemplateParameterEditor;
