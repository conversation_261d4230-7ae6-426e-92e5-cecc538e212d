import { Box, HStack, Input, Text, useDisclosure } from '@chakra-ui/react';
import React, { useState } from 'react';
import WhatsappTemplatePreview from '../../../../../components/WhatsappTemplatePreview';
import { ListMessageTemplateItem } from '../../../../../services/message-templates.service';
import { CommunicationChannelEnum } from '../../../../../types/CommunicationChannelEnum';
import CardCreateMessageTemplate from './CardCreateMessageTemplate';

interface TemplateSelectorProps {
  templates: ListMessageTemplateItem[];
  selectedTemplate: ListMessageTemplateItem | null;
  onClickTemplate: (template: ListMessageTemplateItem) => void;
  communicationChannel: CommunicationChannelEnum;
  showSearch?: boolean;
  refetchTemplates?: () => void;
  showCreateMessageTemplate?: boolean;
}

const TemplateSelector = ({
  templates,
  refetchTemplates,
  selectedTemplate,
  onClickTemplate,
  communicationChannel,
  showSearch = true,
  showCreateMessageTemplate,
}: TemplateSelectorProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const {
    isOpen: isOpenModalCreateMessageTemplate,
    onOpen: onOpenModalCreateMessageTemplate,
    onClose: onCloseModalCreateMessageTemplate,
  } = useDisclosure();

  function handleChangeSearchQuery(event: React.ChangeEvent<HTMLInputElement>) {
    setSearchQuery(event.target.value);
  }

  async function onCreateMessageTemplate(messageTemplateId: string) {
    if (refetchTemplates) {
      await refetchTemplates();
    }
    onCloseModalCreateMessageTemplate();
  }

  const filteredTemplates = templates.filter((template) => {
    return (
      template.name.includes(searchQuery) &&
      template.communicationChannel === communicationChannel
    );
  });

  return (
    <Box width="-webkit-fill-available">
      {showSearch && (
        <Input
          placeholder="Pesquisar por nome"
          onChange={handleChangeSearchQuery}
          value={searchQuery}
          marginBottom={4}
        />
      )}
      <HStack spacing={2} overflowX="scroll">
        {showCreateMessageTemplate && (
          <CardCreateMessageTemplate
            onOpenModalCreateMessageTemplate={onOpenModalCreateMessageTemplate}
            isOpenModalCreateMessageTemplate={isOpenModalCreateMessageTemplate}
            onCloseModalCreateMessageTemplate={
              onCloseModalCreateMessageTemplate
            }
            onCreateMessageTemplate={onCreateMessageTemplate}
          />
        )}
        {filteredTemplates?.map((template) => {
          const isSelected = selectedTemplate?.id === template.id;
          return (
            <Box
              key={template.id}
              onClick={() => onClickTemplate(template)}
              cursor="pointer"
              borderWidth={isSelected ? '2px' : '0'}
              borderRadius="md"
              borderColor={isSelected ? 'blue.500' : 'gray.200'}
              _hover={{
                transform: 'scale(1.01)',
                boxShadow: 'lg',
              }}
            >
              <Text fontWeight={'bold'}>{template.name}</Text>
              <WhatsappTemplatePreview
                message={template.templateText}
                footer={template.footerText}
                buttons={template.messageTemplateButtons}
                fileUrl={template.mediaUrl}
                height={'60vh'}
              />
            </Box>
          );
        })}
      </HStack>
    </Box>
  );
};

export default TemplateSelector;
