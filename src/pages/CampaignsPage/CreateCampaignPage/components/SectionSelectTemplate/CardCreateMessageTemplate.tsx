import { Box, Icon, Text, VStack } from '@chakra-ui/react';
import React from 'react';
import { RiChatNewLine } from 'react-icons/ri';
import ModalCreateMessageTemplate from '../../../../../components/ModalCreateMessageTemplate';

interface CardCreateMessageTemplateProps {
  onOpenModalCreateMessageTemplate: () => void;
  onCloseModalCreateMessageTemplate: () => void;
  isOpenModalCreateMessageTemplate: boolean;
  onCreateMessageTemplate: (messageTemplteId: string) => void;
}

const CardCreateMessageTemplate = ({
  onOpenModalCreateMessageTemplate,
  onCreateMessageTemplate,
  onCloseModalCreateMessageTemplate,
  isOpenModalCreateMessageTemplate,
}: CardCreateMessageTemplateProps) => {
  const bgColor = 'white';
  const borderColor = 'gray.200';
  const hoverBg = 'gray.50';

  return (
    <>
      <Box
        mt={5}
        border="2px dashed"
        borderColor={borderColor}
        borderRadius="lg"
        height="60vh"
        minWidth="300px"
        bg={bgColor}
        transition="all 0.3s"
        _hover={{
          borderColor: 'blue.400',
          bg: hoverBg,
          transform: 'scale(1.01)',
          boxShadow: 'lg',
        }}
        cursor="pointer"
        display="flex"
        alignItems="center"
        justifyContent="center"
        textAlign={'center'}
        padding="20px"
        onClick={onOpenModalCreateMessageTemplate}
      >
        <VStack spacing={4}>
          <Icon as={RiChatNewLine} w={12} h={12} color={borderColor} />
          <Text fontSize="lg" fontWeight="medium" color={'gray.400'}>
            Criar Novo Template
          </Text>
          <Text fontSize="sm" color={'gray.500'}>
            Clique aqui para criar um novo template de mensagem
          </Text>
        </VStack>
      </Box>
      <ModalCreateMessageTemplate
        onCreateMessageTemplate={onCreateMessageTemplate}
        onClose={onCloseModalCreateMessageTemplate}
        isOpen={isOpenModalCreateMessageTemplate}
      />
    </>
  );
};

export default CardCreateMessageTemplate;
