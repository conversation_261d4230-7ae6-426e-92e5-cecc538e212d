import { TableContainer, Table, Thead, Tr, Th, Tbody, Td, Box, Flex, <PERSON>ing, Divider, Button } from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { WhatsappCampaignsService } from '../../../../../services/whatsapp-campaigns.service';
import useDownloadFile from '../../../../../hooks/useDownloadFile';
import { colors } from '../../../../../constants/colors';
import { TbTableExport } from 'react-icons/tb';
import { apiRoutes } from '../../../../../constants/api-routes';

interface TableCustomersOptOut {
  campaignId: string;
}

interface CustomerOptOutItem {
  name: string;
  phoneNumberId: string;
  email: string;
  reason: string;
}

const TableCustomersOptOut = ({ campaignId }: TableCustomersOptOut) => {
  const { downloadExcelFromJson } = useDownloadFile();

  const { data: customersOptOut } = useQuery<CustomerOptOutItem[]>(
    apiRoutes.listCustomersOptOut(campaignId),
    async () => {
      const { data } = await WhatsappCampaignsService.listCustomersOptOut(campaignId);
      return data;
    }
  )

  const formattedData = customersOptOut?.map((customer) => ({
    'Nome': customer.name,
    'Telefone': customer.phoneNumberId,
    'Email': customer.email,
    'Razão do bloqueio': customer.reason,
  }))

  function handleClickExportCustomersOptOut() {
    downloadExcelFromJson(formattedData!, 'clientes-bloqueados.xlsx')
  }

  return (
    <Box>
      <Flex justifyContent={'space-between'} mt={14} alignItems="center">
        <Heading mt={'50px'} >Clientes bloqueados</Heading>
        <Button
          bgColor={colors.primary}
          color={colors.white}
          leftIcon={<TbTableExport size={20} />}
          onClick={handleClickExportCustomersOptOut}
        >
          Exportar
        </Button>
      </Flex>
      <Divider orientation="horizontal" mt={10} />
      <TableContainer overflowX="visible">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Nome</Th>
              <Th>Telefone</Th>
              <Th>Email</Th>
              <Th>Razao do bloqueio</Th>
            </Tr>
          </Thead>
          <Tbody>
            {customersOptOut?.map((customer) => (
              <Tr key={customer.phoneNumberId}>
                <Td>{customer.name}</Td>
                <Td>{customer.phoneNumberId}</Td>
                <Td>{customer.email}</Td>
                <Td>{customer.reason}</Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default TableCustomersOptOut;