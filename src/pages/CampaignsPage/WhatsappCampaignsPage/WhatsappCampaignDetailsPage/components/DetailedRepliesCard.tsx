import {
  Box,
  Flex,
  Center,
  Heading,
  Tag,
  IconB<PERSON>on,
  Collapse,
  SimpleGrid,
} from '@chakra-ui/react';
import { useCallback, useMemo, useState } from 'react';
import { BiMessageAltCheck } from 'react-icons/bi';
import { colors } from '../../../../../constants/colors';
import ReplyCountCard from './ReplyCountCard';
import { FaCaretDown, FaCaretUp } from 'react-icons/fa';

const cardColors = [
  colors.primary,
  colors.secondary,
  colors.green,
  colors.danger,
  colors.purple,
  colors.yellow,
  colors.blueTurquose,
  colors.orange,
  colors.hotPink,
  colors.primaryLight,
  colors.greenMedium,
  colors.yellowMedium,
  colors.chartColors.lightGreen.primary,
  colors.chartColors.green.primary,
  colors.chartColors.lightBlue.primary,
  colors.chartColors.orange.primary,
  colors.chartColors.blue.primary,
  colors.chartColors.yellow.primary,
  colors.chartColors.lightYellow.primary,
  colors.chartColors.darkBlue.primary,
  colors.chartColors.red.primary,
  colors.chartColors.lightRed.primary,
  colors.chartColors.darkOrange.primary,
  colors.chartColors.grey.primary,
  colors.chartColors.darkGrey.primary,
  colors.chartColors.purple.primary,
  colors.chartColors.lightPurple.primary,
];

type ButtonText = string;
type ButtonClicks = number;

interface DetailedRepliesData {
  count: number;
  detailed_replies_count: Record<ButtonText, ButtonClicks>;
}

interface DetailedRepliesCardProps {
  detailedRepliesData: DetailedRepliesData;
}

const DetailedRepliesCard = ({
  detailedRepliesData,
}: DetailedRepliesCardProps) => {
  const [detailedResponsesIsOpen, setDetailedResponsesIsOpen] = useState(false);

  const handleToggleDetails = useCallback(() => {
    setDetailedResponsesIsOpen((prev) => !prev);
  }, []);

  const totalRepliesCount = detailedRepliesData.count || 0;

  const knownClicks = useMemo(() => {
    return Object.values(
      detailedRepliesData.detailed_replies_count || {},
    ).reduce((acc, curr) => (acc as number) + curr, 0);
  }, [detailedRepliesData.detailed_replies_count]);
  const otherClicks = detailedRepliesData.count - knownClicks;
  const otherClicksPercentage =
    totalRepliesCount > 0
      ? parseFloat(((otherClicks / totalRepliesCount) * 100).toFixed(1))
      : 0;

  return (
    <Box
      marginTop={12}
      display="flex"
      flexDirection="column"
      flex={1}
      boxShadow="lg"
      p="6"
      rounded="3xl"
      bg="white"
      gap={4}
      _hover={{
        backgroundColor: 'gray.50',
        cursor: 'pointer',
      }}
    >
      <Flex
        alignItems="center"
        justifyContent="space-between"
        onClick={handleToggleDetails}
        cursor="pointer"
        w="100%"
        pb="2"
      >
        <Flex alignItems="center" gap={3}>
          <Center
            color={colors.white}
            borderRadius="50%"
            bgColor={colors.blueTurquose}
            height="40px"
            width="40px"
            minWidth="40px"
          >
            <BiMessageAltCheck />
          </Center>
          <Heading fontSize={'24px'}>Cliques em Respostas Rápidas</Heading>
          <Tag
            size="md"
            variant="solid"
            colorScheme="blue"
            rounded="full"
            ml="2"
          >
            {totalRepliesCount} cliques
          </Tag>
        </Flex>
        <IconButton
          icon={detailedResponsesIsOpen ? <FaCaretUp /> : <FaCaretDown />}
          variant="ghost"
          aria-label="Toggle detailed responses"
          onClick={handleToggleDetails}
          _hover={{ bg: 'transparent' }}
        />
      </Flex>

      <Collapse in={detailedResponsesIsOpen} animateOpacity>
        <Box p="0" mt="4">
          <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing={4}>
            {detailedRepliesData.detailed_replies_count &&
              Object.entries(detailedRepliesData.detailed_replies_count).map(
                ([replyName, clicksCount], index) => {
                  const percentage =
                    totalRepliesCount > 0
                      ? parseFloat(
                          ((clicksCount / totalRepliesCount) * 100).toFixed(1),
                        )
                      : 0;

                  const color = cardColors[index % cardColors.length];

                  return (
                    <ReplyCountCard
                      key={`${replyName}-${index}`}
                      replyName={replyName}
                      clicksCount={clicksCount}
                      percentage={percentage}
                      color={color}
                    />
                  );
                },
              )}

            {otherClicks > 0 && (
              <ReplyCountCard
                replyName={'Outras'}
                clicksCount={otherClicks}
                percentage={otherClicksPercentage}
                color={colors.chartColors.lightPurple.primary}
                tooltipText="Contabiliza as primeiras respostas enviadas pelos usuários que não correspondem a cliques em botões de resposta rápida."
              />
            )}
          </SimpleGrid>
        </Box>
      </Collapse>
    </Box>
  );
};

export default DetailedRepliesCard;
