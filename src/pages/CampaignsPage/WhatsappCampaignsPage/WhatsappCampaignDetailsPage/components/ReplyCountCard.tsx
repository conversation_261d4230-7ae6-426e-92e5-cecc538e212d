import { Flex, Progress, Text, Box, Tooltip } from '@chakra-ui/react';
import { QuestionIcon } from '@chakra-ui/icons';

const ReplyCountCard = ({
  replyName,
  clicksCount,
  percentage,
  color,
  tooltipText,
}: {
  replyName: string;
  clicksCount: number;
  percentage: number;
  color: string;
  tooltipText?: string;
}) => {
  return (
    <Box
      p="4"
      borderLeft={`5px solid ${color}`}
      bg="white"
      rounded="md"
      boxShadow="sm"
      minW="200px"
    >
      <Flex justifyContent="space-between" alignItems="center" mb="2">
        <Flex alignItems="center">
          <Text fontSize="lg" fontWeight="bold" mb="1" mr={3}>
            {replyName}
          </Text>
          {tooltipText && (
            <Tooltip label={tooltipText}>
              <QuestionIcon boxSize={3} color="gray.500" />
            </Tooltip>
          )}
        </Flex>

        <Text fontSize="sm" fontWeight="bold" color="gray.700">
          {percentage}%
        </Text>
      </Flex>
      <Flex alignItems="center">
        <Text fontSize="2xl" color={color} mb="2" mr={1.5} fontWeight={'bold'}>
          {clicksCount}
        </Text>
        <Text fontSize="sm" color="gray.500" mb="2">
          respostas
        </Text>
      </Flex>
      <Progress
        value={percentage}
        size="xs"
        sx={{
          '& > div': {
            backgroundColor: color,
          },
        }}
      />
    </Box>
  );
};

export default ReplyCountCard;
