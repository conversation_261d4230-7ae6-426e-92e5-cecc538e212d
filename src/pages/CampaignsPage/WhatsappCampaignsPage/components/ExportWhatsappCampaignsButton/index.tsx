import { useDisclosure, useToast } from '@chakra-ui/react';
import { format } from 'date-fns';
import { useState } from 'react';
import { useQuery } from 'react-query';
import { ReportsService } from '../../../../../services/reports.service';
import useDownloadFile from '../../../../../hooks/useDownloadFile';
import ExportCampaignsButton, {
  ExportCampaignsParams,
} from '../../../components/ExportCampaignsButton';
import { apiRoutes } from '../../../../../constants/api-routes';

const ExportWhatsappCampaignsButton = ({
  selectedCampaignsToExport,
  deselectAllCampaigns,
}: {
  selectedCampaignsToExport: Record<string, boolean>;
  deselectAllCampaigns: () => void;
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const [exportParams, setExportParams] = useState<ExportCampaignsParams>();
  const [shouldExecuteQuery, setShouldExecuteQuery] = useState(false);

  const { downloadExcelReport } = useDownloadFile();
  const { isLoading } = useQuery(
    apiRoutes.exportCampaignSalesReport(
      exportParams?.startDate,
      exportParams?.endDate,
      exportParams?.campaignIds,
    ),
    async () => {
      if (!exportParams) return null;
      return await ReportsService.exportCampaignSalesReport(exportParams);
    },
    {
      enabled: shouldExecuteQuery,
      onSuccess: (data) => {
        if (!data) return;
        downloadExcelReport(
          data.data,
          `resultado-de-campanhas-whatsapp-${format(new Date(), 'yyyyMMddHHmmss')}.xlsx`,
        );
        toast({
          title: 'Exportação realizada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        deselectAllCampaigns();
        handleClose();
      },
      onSettled: () => {
        setShouldExecuteQuery(false);
      },
    },
  );

  const handleClickExportCampaigns = (exportParams: ExportCampaignsParams) => {
    setExportParams(exportParams);
    setShouldExecuteQuery(true);
  };

  const handleClose = () => {
    setShouldExecuteQuery(false);
    onClose();
  };

  return (
    <ExportCampaignsButton
      selectedCampaignsToExport={selectedCampaignsToExport}
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={handleClose}
      isLoading={isLoading}
      onExport={handleClickExportCampaigns}
    />
  );
};

export default ExportWhatsappCampaignsButton;
