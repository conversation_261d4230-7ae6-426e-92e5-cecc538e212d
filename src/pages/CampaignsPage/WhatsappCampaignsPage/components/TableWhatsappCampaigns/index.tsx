import {
  Box,
  Button,
  Checkbox,
  Flex,
  IconButton,
  InputGroup,
  InputRightElement,
  ListItem,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  OrderedList,
  Table,
  TableContainer,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BsExclamationTriangle } from 'react-icons/bs';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import AlertDialogBase from '../../../../../components/AlertDialog';
import LoadingScreen from '../../../../../components/LoadingScreen';
import Pagination from '../../../../../components/Pagination';
import { apiRoutes } from '../../../../../constants/api-routes';
import {
  appPaths,
  getCustomersPageQueryParams,
} from '../../../../../constants/app-paths';
import { MixpanelService } from '../../../../../services/mixpanel.service';
import { WhatsappCampaignsService } from '../../../../../services/whatsapp-campaigns.service';
import { EngagementActionsEnum } from '../../../../../types/EngagementActionsEnum';
import { WhatsappCampaignStatsData } from '../../../../../types/WhatsappCampaignsData';
import RowCampaign from './RowCampaign';
import RowExperiment from './RowExperiment';
import InputSelect, {
  SelectOption,
} from '../../../../../components/InputSelect';
import { SearchIcon } from '@chakra-ui/icons';
import { MessageTemplatesService } from '../../../../../services/message-templates.service';

interface TableWhatsappCampaignsProps {
  selectedWhatsappCampaigns: Record<string, boolean>;
  toggleSelectCampaign: (campaignId: string) => void;
  toggleSelectAllCampaigns: (campaignIds: string[]) => void;
  deselectAllCampaigns: () => void;
}

const TableWhatsappCampaigns = ({
  selectedWhatsappCampaigns,
  toggleSelectCampaign,
  toggleSelectAllCampaigns,
  deselectAllCampaigns,
}: TableWhatsappCampaignsProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    isOpen: isAlertOpen,
    onClose: onCloseAlert,
    onOpen: onOpenAlert,
  } = useDisclosure();
  const {
    isOpen: isInterruptedModalOpen,
    onClose: onCloseInterruptedModal,
    onOpen: onOpenInterruptedModal,
  } = useDisclosure();
  const toast = useToast();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(20);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(
    null,
  );
  const [pendingTemplateNames, setPendingTemplateNames] = useState<
    SelectOption[]
  >([]);
  const [selectedTemplateNames, setSelectedTemplateNames] = useState<
    SelectOption[]
  >([]);

  const selectedTemplateParam = selectedTemplateNames
    .map((template) => template.value)
    .join(',');

  const {
    data: tableData,
    isLoading: isLoadingWhatsappCampaigns,
    refetch: refetchWhatsappCampaigns,
  } = useQuery(
    apiRoutes.listWhatsappCampaigns(
      currentPage,
      rowsPerPage,
      selectedTemplateParam,
    ),
    async () => {
      const res = await WhatsappCampaignsService.listWhatsappCampaigns(
        currentPage,
        rowsPerPage,
        selectedTemplateParam,
      );
      return res.data;
    },
    {
      refetchInterval: 10000,
      refetchOnWindowFocus: true,
    },
  );

  const { data: templateOptions } = useQuery(
    apiRoutes.listMessageTemplates({ usedInCampaigns: true }),
    async () => {
      const { data } = await MessageTemplatesService.listMessageTemplates({
        usedInCampaigns: true,
      });
      return data.map(
        (template): SelectOption => ({
          value: template.id,
          label: template.name,
        }),
      );
    },
    {
      refetchOnWindowFocus: true,
    },
  );

  const cancelWhatsappCampaign = useMutation(
    (campaignId: string) =>
      WhatsappCampaignsService.cancelWhatsappCampaign(campaignId),
    {
      onSuccess: () => {
        toast({
          title: 'Campanha cancelada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        refetchWhatsappCampaigns();
      },
    },
  );

  function handleClickEngagementAction(
    templateId: string,
    selectedEngagementActionTypes?: EngagementActionsEnum,
  ) {
    MixpanelService.track('view-campaign-details', {
      selectedEngagementActionTypes: selectedEngagementActionTypes,
    });
    navigate({
      pathname: appPaths.customers.index(),
      search: getCustomersPageQueryParams({
        selectedEngagementTemplateIds: templateId,
        selectedEngagementActionTypes,
      }),
    });
  }

  function groupByCampaignExperimentId(data: WhatsappCampaignStatsData[]) {
    const groupedData = data.reduce(
      (acc, item) => {
        const key = item.campaign_experiment_id || item.id;

        if (!acc[key]) {
          acc[key] = [];
        }

        acc[key].push(item);

        return acc;
      },
      {} as Record<string, WhatsappCampaignStatsData[]>,
    );

    return Object.values(groupedData);
  }

  function handleClickCancelCampaign(campaignId: string) {
    setSelectedCampaignId(campaignId);
    onOpenAlert();
  }

  function handleClickCampaignResults(campaignId: string) {
    navigate({
      pathname: appPaths.campaigns.whatsapp.details(campaignId),
    });
  }
  function handleClickInterruptCampaign() {
    onOpenInterruptedModal();
  }

  const groupedCampaigns = groupByCampaignExperimentId(tableData?.data || []);

  const allCampaignIds = tableData?.data.map((c) => c.id) || [];
  const selectedCount = Object.values(selectedWhatsappCampaigns).filter(
    Boolean,
  ).length;

  const isAllSelected =
    selectedCount === allCampaignIds.length && allCampaignIds.length > 0;
  const isIndeterminate =
    selectedCount > 0 && selectedCount < allCampaignIds.length;

  return (
    <LoadingScreen isLoading={isLoadingWhatsappCampaigns}>
      <Flex w="100%" justifyContent="flex-start" mb={4}>
        <Box flex="0 1 auto" position="relative" display="flex" width="auto">
          <InputGroup size="sm" w="auto" display="flex">
            <InputSelect
              placeholder="Buscar campanhas"
              options={templateOptions || []}
              isMulti
              value={pendingTemplateNames || []}
              onChange={(value) => {
                if (value.length === 0) {
                  deselectAllCampaigns();
                  setSelectedTemplateNames([]);
                  refetchWhatsappCampaigns();
                }
                setPendingTemplateNames(value);
              }}
              styles={{
                control: (base: any) => ({
                  ...base,
                  minHeight: '40px',
                  borderColor: '#ccc',
                  boxShadow: 'none',
                  width: '400px',
                  paddingRight: '40px',
                }),
                menuPortal: (base: any) => ({ ...base, zIndex: 9999 }),
              }}
            />
            <InputRightElement width="40px" height="100%" pointerEvents="auto">
              <IconButton
                aria-label="Buscar"
                icon={<SearchIcon />}
                size="sm"
                height="100%"
                width="100%"
                borderLeftRadius="0"
                borderTopLeftRadius="0"
                bg="transparent"
                _hover={{ bg: 'gray.100' }}
                onClick={() => {
                  if (pendingTemplateNames.length === 0) return;
                  setSelectedTemplateNames(pendingTemplateNames);
                  refetchWhatsappCampaigns();
                  deselectAllCampaigns();
                }}
              />
            </InputRightElement>
          </InputGroup>
        </Box>
      </Flex>
      <TableContainer overflowX="visible">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>
                <Checkbox
                  isChecked={isAllSelected}
                  isIndeterminate={isIndeterminate}
                  onChange={() => {
                    if (isAllSelected || isIndeterminate) {
                      deselectAllCampaigns();
                    } else {
                      toggleSelectAllCampaigns(allCampaignIds);
                    }
                  }}
                />
              </Th>
              <Th>Data de envio</Th>
              <Th>Template</Th>
              <Th># destinatários</Th>
              <Th># enviadas</Th>
              <Th># lidas</Th>
              <Th># cliques</Th>
              <Th># respostas</Th>
              <Th># erros</Th>
              <Th>Status</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>
            {groupedCampaigns.map((campaignsList) => {
              const isExperiment = campaignsList.some(
                (campaign) => !!campaign.campaign_experiment_id,
              );
              return isExperiment ? (
                <RowExperiment
                  whatsappCampaigns={campaignsList}
                  onClickEngagementAction={handleClickEngagementAction}
                  onClickCancelCampaign={handleClickCancelCampaign}
                  onClickCampaignResults={handleClickCampaignResults}
                  onClickInterruptCampaign={handleClickInterruptCampaign}
                  handleSelectWhatsappCampaign={toggleSelectCampaign}
                  selectedWhatsappCampaigns={selectedWhatsappCampaigns}
                />
              ) : (
                campaignsList.map((whatsappCampaign) => (
                  <RowCampaign
                    selectedWhatsappCampaigns={selectedWhatsappCampaigns}
                    whatsappCampaign={whatsappCampaign}
                    onClickEngagementAction={handleClickEngagementAction}
                    onClickCancelCampaign={handleClickCancelCampaign}
                    onClickCampaignResults={handleClickCampaignResults}
                    onClickInterruptCampaign={handleClickInterruptCampaign}
                    handleSelectWhatsappCampaign={toggleSelectCampaign}
                  />
                ))
              );
            })}
          </Tbody>
        </Table>
      </TableContainer>
      <Pagination
        initialPage={currentPage}
        onChangePage={(page) => setCurrentPage(page)}
        rowsPerPage={rowsPerPage}
        totalRows={tableData?.meta.totalItems || 0}
        onChangeRowsPerPage={(rowsPerPage) => {
          setRowsPerPage(rowsPerPage);
        }}
        itemsLabel="clientes"
      />
      <AlertDialogBase
        isOpen={isAlertOpen}
        onClose={() => {
          setSelectedCampaignId(null);
          onCloseAlert();
        }}
        title="Confirmar cancelamento"
        onConfirm={() => {
          cancelWhatsappCampaign.mutateAsync(selectedCampaignId!);
          onCloseAlert();
        }}
      >
        Tem certeza que deseja cancelar o agendamento desta campanha?
      </AlertDialogBase>
      <Modal isOpen={isInterruptedModalOpen} onClose={onCloseInterruptedModal}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader display={'flex'} alignItems="center" gap={2}>
            <BsExclamationTriangle size={20} />
            Campanha interrompida pela Meta
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text fontStyle={'italic'}>
              A campanha foi interrompida pela Meta devido ao baixo engajamento.
              Para continuar o envio para os clientes que não receberam a
              campanha, siga os passos abaixo:
            </Text>
            <OrderedList mt={2}>
              {[
                {
                  text: 'Crie um novo template',
                },
                {
                  text: 'Aguarde a aprovação do novo template',
                },
                {
                  text: 'Clique na aba de clientes',
                },
                {
                  text: 'Adicione os mesmos filtros do último envio',
                },
                {
                  text: 'Oculte clientes que receberam uma campanha nos últimos 2 dias para evitar de impactar o mesmo cliente',
                  fontWeight: 'bold',
                },
                {
                  text: 'Envie a campanha',
                },
              ].map(({ text, fontWeight }) => (
                <ListItem fontWeight={fontWeight}>{text}</ListItem>
              ))}
            </OrderedList>
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onCloseInterruptedModal}>
              Ok
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </LoadingScreen>
  );
};

export default TableWhatsappCampaigns;
