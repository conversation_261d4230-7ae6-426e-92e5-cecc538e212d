import { Box, Flex, Heading } from '@chakra-ui/react';
import { useState } from 'react';
import { BiConversation, BiSolidMessageX, BiPhone } from 'react-icons/bi';
import { FaWhatsapp } from 'react-icons/fa';
import { MdCampaign } from 'react-icons/md';
import { PiArrowsClockwiseBold } from 'react-icons/pi';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import CardStatisticContainer from '../../../components/CardStatisticContainer';
import Header from '../components/Header';
import TableWhatsappCampaigns from './components/TableWhatsappCampaigns';
import ExportWhatsappCampaignsButton from './components/ExportWhatsappCampaignsButton';

const WhatsappCampaignsPage = () => {
  const [selectedCampaignsToExport, setSelectedCampaignsToExport] = useState<
    Record<string, boolean>
  >({});

  const cardsData = [
    {
      icon: <BiPhone />,
      title: 'Qualidade do Nº',
      requestRoute: apiRoutes.showPhoneQualityRating(),
      valueFormatter: (value: { rating: string }) => {
        switch (value?.rating) {
          case 'high':
            return 'Alta';
          case 'medium':
            return 'Média';
          case 'low':
            return 'Baixa';
          default:
            return 'N/A';
        }
      },
      bgIconColor: getColorByPhoneQualityRating,
      tooltip:
        'Refere-se a qualidade do seu número na Meta (antigo Facebook); os valores possíveis são "Baixo", "Médio" e "Alto"; quando "Baixo" é preciso ter cuidado com o que é enviado em campanhas pois a Meta pode bloquear seu número por 30 dias',
      valueColor: getColorByPhoneQualityRating,
    },
    {
      icon: <MdCampaign />,
      title: 'Total de campanhas',
      requestRoute: apiRoutes.getTotalWhatsappCampaigns(new Date(), new Date()),
      bgIconColor: colors.primaryLight,
    },
    {
      icon: <BiConversation />,
      title: 'Total de disparos',
      requestRoute: apiRoutes.getTotalMesssagesSent(new Date(), new Date()),
      bgIconColor: colors.secondary,
      tooltip: 'Total de mensagens enviadas em campanhas',
    },
    {
      icon: <PiArrowsClockwiseBold />,
      title: 'Engajamento',
      requestRoute: apiRoutes.getMessagesEngagementRate(new Date(), new Date()),
      valueFormatter: (value: any) => `${(value || 0).toFixed(2)}%`,
      bgIconColor: colors.green,
      tooltip:
        'Percentual de mensagens que tiveram respostas ou cliques nos link',
    },
    {
      icon: <BiSolidMessageX />,
      title: 'Bounce Rate',
      requestRoute: apiRoutes.getMessagesBounceRate(new Date(), new Date()),
      valueFormatter: (value: any) => `${(value || 0).toFixed(2)}%`,
      bgIconColor: colors.red,
      tooltip: 'Percentual de mensagens que não foram entregues',
    },
  ];

  function getColorByPhoneQualityRating(value: { rating: string }) {
    switch (value?.rating) {
      case 'high':
        return colors.greenMedium;
      case 'medium':
        return colors.yellowMedium;
      case 'low':
        return colors.danger;
      default:
        return colors.lightGrey;
    }
  }

  function toggleSelectCampaign(campaignId: string) {
    setSelectedCampaignsToExport((prev) => ({
      ...prev,
      [campaignId]: !prev[campaignId],
    }));
  }

  function toggleSelectAllCampaigns(campaignIds: string[]) {
    setSelectedCampaignsToExport((prev) => {
      const allSelected = campaignIds.every((id) => prev[id]);

      if (allSelected) {
        return {};
      } else {
        const newState: Record<string, boolean> = { ...prev };
        campaignIds.forEach((id) => {
          newState[id] = true;
        });
        return newState;
      }
    });
  }

  const deselectAllCampaigns = () => {
    setSelectedCampaignsToExport({});
  };

  return (
    <Box padding="20px">
      <Header title="Campanhas de WhatsApp" icon={<FaWhatsapp />} />
      <Flex justifyContent="space-between" gap={3} alignItems="center">
        {cardsData.map(
          ({
            icon,
            requestRoute,
            title,
            bgIconColor,
            valueFormatter,
            tooltip,
            valueColor,
          }) => (
            <CardStatisticContainer
              key={title}
              icon={icon}
              title={title}
              requestRoute={requestRoute}
              valueFormatter={valueFormatter}
              bgIconColor={bgIconColor}
              tooltip={tooltip}
              valueColor={valueColor}
            />
          ),
        )}
      </Flex>
      <Flex justifyContent={'space-between'} mt={10} alignItems="center">
        <Heading size="md">Campanhas recentes</Heading>
        <ExportWhatsappCampaignsButton
          selectedCampaignsToExport={selectedCampaignsToExport}
          deselectAllCampaigns={deselectAllCampaigns}
        />
      </Flex>
      <TableWhatsappCampaigns
        toggleSelectCampaign={toggleSelectCampaign}
        selectedWhatsappCampaigns={selectedCampaignsToExport}
        toggleSelectAllCampaigns={toggleSelectAllCampaigns}
        deselectAllCampaigns={deselectAllCampaigns}
      />
    </Box>
  );
};

export default WhatsappCampaignsPage;
