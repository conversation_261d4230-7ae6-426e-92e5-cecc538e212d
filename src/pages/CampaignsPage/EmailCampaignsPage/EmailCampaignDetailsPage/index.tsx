import { QuestionIcon } from '@chakra-ui/icons';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Drawer,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>nt,
  DrawerHeader,
  DrawerOverlay,
  Flex,
  Grid,
  GridItem,
  Heading,
  Select,
  Text,
  Tooltip,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { BiSolidMessageX } from 'react-icons/bi';
import { BsFillEyeFill, BsPeopleFill } from 'react-icons/bs';
import { FaRegCopy, FaUsers } from 'react-icons/fa';
import { HiCursorClick } from 'react-icons/hi';
import {
  MdAttachMoney,
  MdMarkEmailRead,
  MdOutlineFilterAlt,
} from 'react-icons/md';
import { PiArrowsClockwiseBold } from 'react-icons/pi';
import { RiMailLockLine, RiSpam2Line } from 'react-icons/ri';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import BreadcrumbBase from '../../../../components/BreadcrumbBase';
import CardStatistic from '../../../../components/CardStatistic';
import CustomECharts from '../../../../components/CustomECharts';
import EmailTemplatePreview from '../../../../components/EmailTemplatePreview';
import LoadingScreen from '../../../../components/LoadingScreen';
import TableFilters from '../../../../components/TableFilters';
import { appPaths } from '../../../../constants/app-paths';
import { colors } from '../../../../constants/colors';
import { EmailCampaignsService } from '../../../../services/email-campaigns.service';
import { StatisticsService } from '../../../../services/statistics.service';
import { ChartUtils } from '../../../../utils/chart.utils';
import { CustomerFilterUtils } from '../../../../utils/customer-filter.utils';
import { MoneyUtils } from '../../../../utils/money.utils';
import { NumberUtils } from '../../../../utils/number.utils';

type TimeUnit = 'hour' | 'day';
type TempFunnelMetric = 'totalOrders' | 'totalOrdersValue';

const EmailCampaignDetailsPage = () => {
  const { emailCampaignId } = useParams();
  const [chartTimeUnit, setChartTimeUnit] = useState<TimeUnit>('day');
  const [funnelMetric, setFunnelMetric] =
    useState<TempFunnelMetric>('totalOrdersValue');
  const {
    isOpen: isDrawerOpen,
    onClose: onCloseDrawer,
    onOpen: onOpenDrawer,
  } = useDisclosure();
  const toast = useToast();
  const navigate = useNavigate();

  const {
    data: campaignPerformanceData = [],
    isLoading: isLoadingCampaignPerformanceData,
  } = useQuery(
    [
      'statistics-service',
      'get-chart-email-campaigns-performance',
      emailCampaignId,
      chartTimeUnit,
    ],
    async () => {
      const { data } = await StatisticsService.getChartEmailCampaignPerformance(
        emailCampaignId || '',
        chartTimeUnit,
      );
      return data;
    },
    {},
  );

  const { data: campaignSalesData, isLoading: isLoadingCampaignSalesData } =
    useQuery(
      ['statistics-service', 'email-campaign-sales', emailCampaignId],
      async () => {
        const { data } = await StatisticsService.getChartEmailCampaignSales(
          emailCampaignId || '',
        );
        return data;
      },
      {},
    );

  const { data: details, isLoading: isLoadingDetails } = useQuery(
    ['email-campaigns', 'get-campaign-details', emailCampaignId],
    async () => {
      return await EmailCampaignsService.getEmailCampaignDetails(
        emailCampaignId || '',
      );
    },
  );

  function copyToClipboard(value: string) {
    navigator.clipboard.writeText(value);
    toast({
      title: 'Filtros copiados para a área de transferência',
    });
  }

  const getTransformedFilterCriteria = async () => {
    if (!details?.filterCriteria) {
      return [];
    }

    return await CustomerFilterUtils.parseFilterCriteriaAsync(
      details?.filterCriteria,
    );
  };

  const isLoadingScreen =
    isLoadingCampaignPerformanceData ||
    isLoadingCampaignSalesData ||
    isLoadingDetails;

  return (
    <LoadingScreen isLoading={isLoadingScreen}>
      <Box>
        <Box paddingX={'100px'} paddingY="20px">
          <BreadcrumbBase
            items={[
              {
                path: appPaths.campaigns.email.index(),
                name: 'Campanhas',
              },
              {
                path: appPaths.campaigns.email.details(emailCampaignId || ''),
                name: details?.emailTemplateName || '',
              },
            ]}
          />
          <Flex justify="space-between">
            <Heading>Engajamento da campanha</Heading>
            <Button leftIcon={<MdOutlineFilterAlt />} onClick={onOpenDrawer}>
              Ver segmentação
            </Button>
          </Flex>
          <Flex gap={3} alignItems="baseline">
            <Text color={colors.darkGrey}></Text>
          </Flex>
          <CustomECharts
            chartWidth={'100%'}
            chartHeight="450px"
            marginTop={'50px'}
            topRightComponent={
              <Select
                defaultValue={chartTimeUnit}
                onChange={(e) => setChartTimeUnit(e.target.value as TimeUnit)}
              >
                <option value="hour">Por hora</option>
                <option value="day">Por dia</option>
              </Select>
            }
            option={{
              legend: {},
              dataZoom: [
                {
                  type: 'slider',
                  start: 0,
                  end: 40,
                },
              ],
              tooltip: {},
              color: [colors.primaryLight, colors.green, colors.purple],
              dataset: {
                dimensions: [
                  'datetime',
                  'read_count',
                  'clicked_count',
                  'total_orders_value',
                ],
                source: campaignPerformanceData,
              },
              xAxis: {
                type: 'category',
              },
              yAxis: [
                {
                  name: 'Mensagens',
                  nameLocation: 'middle',
                  splitLine: { show: false },
                  nameGap: 55,
                },
                {
                  name: 'Total em Vendas (R$)',
                  nameLocation: 'middle',
                  nameGap: 55,
                  splitLine: { show: false },
                },
              ],
              series: [
                { type: 'bar', name: 'Lidas', yAxisIndex: 0 },
                { type: 'bar', name: 'Cliques', yAxisIndex: 0 },
                {
                  type: 'line',
                  name: 'Total em vendas',
                  yAxisIndex: 1,
                  tooltip: {
                    trigger: 'item',
                    formatter: function (param: any) {
                      return `<strong>Total em vendas:</strong>  ${MoneyUtils.formatCurrency(
                        param.value.total_orders_value * 100,
                      )} <br/><strong>Total de pedidos: </strong> ${
                        param.value.order_count
                      }`;
                    },
                  },
                },
              ],
            }}
          />
          <Flex justifyContent="space-between" gap={3} mt={8} flexWrap="wrap">
            {[
              {
                icon: <BsPeopleFill />,
                title: 'Destinatários',
                value: details?.totalRecipients || '',
                bgIconColor: colors.secondary,
                tooltip: 'Quantidade de Destinatários',
              },
              {
                icon: <MdMarkEmailRead />,
                title: 'Taxa de Entrega',
                value: NumberUtils.getPercentValue(
                  details?.emailCampaignResult.totalDelivered,
                  details?.emailCampaignResult.totalSent,
                ),
                bgIconColor: colors.primary,
                tooltip: `${details?.emailCampaignResult.totalDelivered} entregues de ${details?.emailCampaignResult.totalSent} enviados`,
              },
              {
                icon: <BsFillEyeFill />,
                title: 'Taxa de Abertura',
                value: NumberUtils.getPercentValue(
                  details?.emailCampaignResult.totalReads,
                  details?.emailCampaignResult.totalDelivered,
                ),
                bgIconColor: colors.primaryLight,
                tooltip: `${details?.emailCampaignResult.totalReads} lidos de ${details?.emailCampaignResult.totalDelivered} entregues`,
              },
              {
                icon: <HiCursorClick />,
                title: 'Taxa de Cliques',
                value: NumberUtils.getPercentValue(
                  details?.emailCampaignResult.totalUniqueClicks,
                  details?.emailCampaignResult.totalReads,
                ),
                bgIconColor: colors.green,
                tooltip: `${details?.emailCampaignResult.totalUniqueClicks} emails com cliques de ${details?.emailCampaignResult.totalReads} abertos`,
              },
              {
                icon: <BiSolidMessageX />,
                title: 'Taxa de Bounce',
                value: NumberUtils.getPercentValue(
                  details?.emailCampaignResult.totalBounces,
                  details?.emailCampaignResult.totalSent,
                ),
                bgIconColor: colors.red,
                tooltip: `${details?.emailCampaignResult.totalBounces} bounces de ${details?.emailCampaignResult.totalSent} enviados`,
              },
              {
                icon: <RiSpam2Line />,
                title: 'Taxa de Spam',
                value: NumberUtils.getPercentValue(
                  details?.emailCampaignResult.totalSpamReports,
                  details?.emailCampaignResult.totalDelivered,
                ),
                bgIconColor: colors.yellowMedium,
                tooltip: `${details?.emailCampaignResult.totalSpamReports} marcados como spam de ${details?.emailCampaignResult.totalSent} enviados`,
              },
              {
                icon: <RiMailLockLine />,
                title: 'Taxa de Cancelamento',
                value: NumberUtils.getPercentValue(
                  details?.emailCampaignResult.totalUnsubscribes,
                  details?.emailCampaignResult.totalDelivered,
                ),
                bgIconColor: colors.black,
                tooltip: `${details?.emailCampaignResult.totalUnsubscribes} inscrições canceladas de ${details?.emailCampaignResult.totalSent} entregues`,
              },
            ].map(({ icon, title, value, bgIconColor, tooltip }) => (
              <CardStatistic
                value={value}
                key={title}
                icon={icon}
                title={title}
                bgIconColor={bgIconColor}
                tooltip={tooltip}
              />
            ))}
          </Flex>

          <Heading marginTop={'50px'}>
            Performance de vendas{' '}
            <Tooltip
              label={
                'Estimativa considerando compras realizadas até 7 dias após a campanha'
              }
            >
              <QuestionIcon boxSize={3} />
            </Tooltip>
          </Heading>
          <CustomECharts
            chartWidth={'90%'}
            chartHeight="450px"
            topRightComponent={
              <Select
                defaultValue={funnelMetric}
                onChange={(e) =>
                  setFunnelMetric(e.target.value as TempFunnelMetric)
                }
              >
                <option value="totalOrdersValue">Total em vendas</option>
                <option value="totalOrders">Total de pedidos</option>
                {/* <option value="roi">ROI</option> */}
              </Select>
            }
            option={{
              title: {
                text: 'ENGAJOU = CLICOU EM UM LINK',
                borderColor: '#d0d0d0',
                borderWidth: 1,
                textStyle: {
                  fontSize: 10,
                  color: '#d0d0d0',
                },
                left: '80%',
                top: '90%',
              },
              color: [colors.orange, colors.primaryLight, colors.green],
              series: [
                {
                  type: 'funnel',
                  name: 'Outer',
                  width: '70%',
                  label: {
                    show: true,
                    fontSize: 14,
                  },
                  labelLine: {
                    length: 15,
                    lineStyle: {
                      width: 2,
                      type: 'solid',
                    },
                  },
                  itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                  },
                  emphasis: {
                    disabled: true,
                  },
                  data: [
                    {
                      value: campaignSalesData?.received[funnelMetric],
                      name: `RECEBEU A MENSAGEM (${campaignSalesData?.received.count})`,
                    },
                    {
                      value: campaignSalesData?.read[funnelMetric],
                      name: `LEU A MENSAGEM (${campaignSalesData?.read.count})`,
                    },
                    {
                      value: campaignSalesData?.engaged[funnelMetric],
                      name: `ENGAJOU COM A MENSAGEM (${campaignSalesData?.engaged.count})`,
                    },
                  ],
                },
                {
                  type: 'funnel',
                  name: 'Inner',
                  width: '70%',
                  label: {
                    show: true,
                    position: 'inside',
                    fontSize: 14,
                  },
                  labelLine: {
                    length: 20,
                    lineStyle: {
                      width: 2,
                      type: 'solid',
                    },
                  },
                  itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                  },
                  emphasis: {
                    label: {
                      fontSize: 20,
                    },
                  },
                  data: [
                    {
                      value: campaignSalesData?.received[funnelMetric],
                      name: ChartUtils.getFormattedFunnelValue(
                        campaignSalesData?.received[funnelMetric],
                        funnelMetric,
                      ),
                    },
                    {
                      value: campaignSalesData?.read[funnelMetric],
                      name: ` ${ChartUtils.getFormattedFunnelValue(
                        campaignSalesData?.read[funnelMetric],
                        funnelMetric,
                      )} `, // blank space added to have different color on chart
                    },
                    {
                      value: campaignSalesData?.engaged[funnelMetric],
                      name: `  ${ChartUtils.getFormattedFunnelValue(
                        campaignSalesData?.engaged[funnelMetric],
                        funnelMetric,
                      )}  `, // blank space added to have different color on chart
                    },
                  ],
                },
              ],
            }}
          />
          <Flex gap={5} mt={5}>
            {[
              {
                icon: <MdAttachMoney />,
                title: 'Total em compras',
                value: ChartUtils.getFormattedFunnelValue(
                  campaignSalesData?.received.totalOrdersValue,
                  'totalOrdersValue',
                ),
                bgIconColor: colors.greenMedium,
                tooltip:
                  'Total em Compras de Clientes que Receberam a Mensagem',
              },
              // {
              //   icon: <GiPayMoney />,
              //   title: 'Custo da campanha',
              //   value: MoneyUtils.formatCurrency(
              //     campaignSalesData?.campaignCost || 0
              //   ),
              //   bgIconColor: colors.danger,
              //   tooltip: 'Total de Mensagens Entregues * Custo por Mensagem',
              // },
              // {
              //   icon: <AiOutlineAreaChart />,
              //   title: 'ROI',
              //   value: ChartUtils.getFormattedFunnelValue(
              //     campaignSalesData?.received.roi,
              //     'roi'
              //   ),
              //   bgIconColor: colors.primaryMedium,
              //   tooltip:
              //     'Total em Vendas / (Total de Mensagens Entregues * Custo por Mensagem)',
              // },
              {
                icon: <PiArrowsClockwiseBold />,
                title: 'Engajamento',
                value: `${NumberUtils.getPercentValue(
                  campaignSalesData?.engaged.count || 0,
                  campaignSalesData?.read.count || 1,
                )}`,
                bgIconColor: colors.green,
                tooltip: 'Engajamento = Cliques / Total de Emails Abertos',
              },
            ].map(({ icon, title, value, bgIconColor, tooltip }) => (
              <CardStatistic
                value={value}
                key={title}
                icon={icon}
                title={title}
                bgIconColor={bgIconColor}
                tooltip={tooltip}
              />
            ))}
          </Flex>
        </Box>
        <Drawer
          isOpen={isDrawerOpen}
          placement="right"
          onClose={onCloseDrawer}
          size={'xl'}
        >
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton />
            <DrawerHeader>Detalhes da campanha</DrawerHeader>

            <DrawerBody>
              <Grid
                height="auto"
                templateColumns="2fr 2fr"
                paddingTop="50px"
                paddingBottom="50px"
                paddingX="2px"
                alignItems="start"
                gap={4}
              >
                <Grid>
                  <GridItem>
                    <Text fontWeight="bold">Segmentação utilizada</Text>
                  </GridItem>
                  <GridItem>
                    <Box mt={4}>
                      <Flex justifyContent="space-between">
                        <Flex gap={2}>
                          <Button
                            size="xs"
                            leftIcon={<FaUsers />}
                            onClick={() => {
                              navigate({
                                pathname: appPaths.customers.index(),
                                search: details?.filterCriteria?.replace(
                                  /excludedTemplateIds=.+?(?=&|$)/g,
                                  '',
                                ),
                              });
                            }}
                          >
                            Aplicar segmentação
                          </Button>
                          <Button
                            size="xs"
                            leftIcon={<FaRegCopy />}
                            onClick={async () => {
                              const transformedCriteria =
                                await getTransformedFilterCriteria();
                              copyToClipboard(
                                transformedCriteria
                                  .map(
                                    (filter) =>
                                      `${filter.title}: ${filter.value}`,
                                  )
                                  .join(';\n'),
                              );
                            }}
                          >
                            Copiar filtros
                          </Button>
                        </Flex>
                      </Flex>
                      <TableFilters
                        filterCriteria={details?.filterCriteria || null}
                      />
                    </Box>
                  </GridItem>
                </Grid>
                <GridItem position={'sticky'} top={0} alignSelf="start">
                  {details?.emailTemplate && (
                    <EmailTemplatePreview
                      emailTemplate={details?.emailTemplate}
                    />
                  )}
                </GridItem>
              </Grid>
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      </Box>
    </LoadingScreen>
  );
};

export default EmailCampaignDetailsPage;
