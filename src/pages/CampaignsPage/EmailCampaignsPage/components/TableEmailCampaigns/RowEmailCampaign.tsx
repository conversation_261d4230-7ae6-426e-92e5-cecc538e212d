import { Badge, Checkbox, Link, Td, Text, Tooltip, Tr } from '@chakra-ui/react';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { MdFreeCancellation, MdOutlineRemoveRedEye } from 'react-icons/md';
import ButtonIcon from '../../../../../components/ButtonIcon';
import { colors } from '../../../../../constants/colors';
import { EmailCampaignData } from '../../../../../types/EmailCampaignData';
import { EngagementActionsEnum } from '../../../../../types/EngagementActionsEnum';
import {
  EmailCampaign,
  EmailCampaignStatus,
  WhatsappCampaignStatus,
} from '../../../../../types/Prisma';
import { NumberUtils } from '../../../../../utils/number.utils';

export interface RowEmailCampaignProps {
  emailCampaign: EmailCampaignData;
  selectedEmailCampaigns: Record<string, boolean>;
  toggleSelectCampaign: (emailCampaignId: string) => void;
  onClickEngagementAction: (
    templateId: string,
    action: EngagementActionsEnum,
  ) => void;
  onClickCancelCampaign: (emailCampaignId: string) => void;
  onClickCampaignResults: (emailCampaignId: string) => void;
  bgColor?: string;
  paddingLeft?: string;
  campaignIcon?: React.ReactNode;
}

const RowEmailCampaign = ({
  emailCampaign,
  selectedEmailCampaigns,
  toggleSelectCampaign,
  onClickEngagementAction,
  onClickCancelCampaign,
  onClickCampaignResults,
  bgColor,
  paddingLeft,
  campaignIcon,
}: RowEmailCampaignProps) => {
  const { t } = useTranslation();
  const result = emailCampaign.emailCampaignResult;

  function getRealCampaignStatus(
    campaign: EmailCampaign,
  ): WhatsappCampaignStatus {
    if (campaign.status === 'scheduled' && !campaign.scheduledExecutionTime) {
      return 'in_progress';
    }

    return campaign.status;
  }

  function getColorScheme(status: EmailCampaignStatus): string {
    const statusData: Record<EmailCampaignStatus, string> = {
      in_progress: colors.status.inProgress,
      completed: colors.status.completed,
      scheduled: colors.status.scheduled,
      interrupted: colors.status.interrupted,
      failed: colors.status.failed,
      canceled: colors.status.canceled,
    };
    return statusData[status];
  }

  return (
    <Tr
      bgColor={bgColor}
      key={emailCampaign.id}
      fontWeight={
        getRealCampaignStatus(emailCampaign) === 'scheduled'
          ? 'semibold'
          : 'light'
      }
    >
      <Td>
        <Checkbox
          isChecked={!!selectedEmailCampaigns?.[emailCampaign.id]}
          onChange={() => toggleSelectCampaign(emailCampaign.id)}
        />
      </Td>
      <Td paddingLeft={paddingLeft}>
        {format(
          new Date(
            emailCampaign.scheduledExecutionTime || emailCampaign.createdAt,
          ),
          'dd/MM/yyyy HH:mm',
        )}
      </Td>
      <Td>
        <Tooltip label={emailCampaign.emailTemplateName}>
          <Text display="inline-flex" alignItems="center">
            {emailCampaign.emailTemplateName}
            {campaignIcon}
          </Text>
        </Tooltip>
      </Td>

      <Td textAlign={'center'}>{emailCampaign.totalRecipients}</Td>
      <Td>
        {getRealCampaignStatus(emailCampaign) !== 'scheduled' && (
          <Tooltip
            label={`${result.totalDelivered} entregues de ${result.totalSent} enviados`}
            aria-label="A tooltip"
            placement="top"
            key={`${emailCampaign.id}-tooltip-delivered`}
          >
            <Link
              display={'flex'}
              alignItems={'center'}
              justifyContent="center"
              gap={3}
              color={colors.primaryLight}
              onClick={() =>
                onClickEngagementAction(
                  emailCampaign.emailTemplateId,
                  EngagementActionsEnum.RECEIVED,
                )
              }
            >
              {NumberUtils.getPercentValue(
                result.totalDelivered,
                result.totalSent,
              )}
            </Link>
          </Tooltip>
        )}
      </Td>
      <Td>
        {getRealCampaignStatus(emailCampaign) !== 'scheduled' && (
          <Tooltip
            label={`${result.totalReads} abertos de ${result.totalDelivered} entregues`}
            aria-label="A tooltip"
            placement="top"
            key={`${emailCampaign.id}-tooltip-reads`}
          >
            <Link
              display={'flex'}
              alignItems={'center'}
              justifyContent="center"
              gap={3}
              color={colors.primaryLight}
              onClick={() =>
                onClickEngagementAction(
                  emailCampaign.emailTemplateId,
                  EngagementActionsEnum.READ,
                )
              }
            >
              {NumberUtils.getPercentValue(
                result.totalReads,
                result.totalDelivered,
              )}
            </Link>
          </Tooltip>
        )}
      </Td>
      <Td>
        {getRealCampaignStatus(emailCampaign) !== 'scheduled' && (
          <Tooltip
            label={`${result.totalUniqueClicks} emails com cliques de ${result.totalReads} abertos`}
            aria-label="A tooltip"
            placement="top"
            key={`${emailCampaign.id}-tooltip-unique-clicks`}
          >
            <Link
              display={'flex'}
              alignItems={'center'}
              justifyContent="center"
              gap={3}
              color={colors.primaryLight}
              onClick={() =>
                onClickEngagementAction(
                  emailCampaign.emailTemplateId,
                  EngagementActionsEnum.CLICKED,
                )
              }
            >
              {NumberUtils.getPercentValue(
                result.totalUniqueClicks,
                result.totalReads,
              )}
            </Link>
          </Tooltip>
        )}
      </Td>
      <Td textAlign={'center'}>
        <Text noOfLines={1} maxW={'300px'} display="block">
          {result.totalFailures}
        </Text>
      </Td>
      <Td
        display={'flex'}
        width={'100px'}
        flexDir="column"
        justifyContent={'center'}
        alignItems="center"
      >
        <Badge
          colorScheme={getColorScheme(getRealCampaignStatus(emailCampaign))}
        >
          {t(
            `enums.WhatsappCampaignStatus.${getRealCampaignStatus(
              emailCampaign,
            )}`,
          )}
        </Badge>
      </Td>
      <Td>
        {getRealCampaignStatus(emailCampaign) === 'scheduled' ? (
          <ButtonIcon
            tooltipLabel="Cancelar agendamento"
            icon={<MdFreeCancellation color={colors.darkGrey} size={20} />}
            onClick={() => {
              onClickCancelCampaign(emailCampaign.id);
            }}
          />
        ) : ['completed', 'interrupted'].includes(
            getRealCampaignStatus(emailCampaign),
          ) ? (
          <ButtonIcon
            tooltipLabel="Ver resultados"
            icon={
              <MdOutlineRemoveRedEye color={colors.primaryLight} size={20} />
            }
            onClick={() => onClickCampaignResults(emailCampaign.id)}
          />
        ) : null}
      </Td>
    </Tr>
  );
};

export default RowEmailCampaign;
