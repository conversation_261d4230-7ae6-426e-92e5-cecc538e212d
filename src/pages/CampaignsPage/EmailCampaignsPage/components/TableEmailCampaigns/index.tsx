import {
  Checkbox,
  Divider,
  Flex,
  Heading,
  Table,
  TableContainer,
  Tbody,
  Th,
  Thead,
  Tr,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import AlertDialogBase from '../../../../../components/AlertDialog';
import LoadingScreen from '../../../../../components/LoadingScreen';
import Pagination from '../../../../../components/Pagination';
import { apiRoutes } from '../../../../../constants/api-routes';
import {
  appPaths,
  getCustomersPageQueryParams,
} from '../../../../../constants/app-paths';
import { EmailCampaignsService } from '../../../../../services/email-campaigns.service';
import { MixpanelService } from '../../../../../services/mixpanel.service';
import { EmailCampaignData } from '../../../../../types/EmailCampaignData';
import { EngagementActionsEnum } from '../../../../../types/EngagementActionsEnum';
import RowEmailCampaign from './RowEmailCampaign';
import ExportEmailCampaignsButton from '../ExportEmailCampaignsButton';

const TableEmailCampaigns = () => {
  const navigate = useNavigate();
  const {
    isOpen: isAlertOpen,
    onClose: onCloseAlert,
    onOpen: onOpenAlert,
  } = useDisclosure();
  const toast = useToast();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(20);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(
    null,
  );
  const [selectedEmailCampaigns, setSelectedEmailCampaigns] = useState<
    Record<string, boolean>
  >({});

  const {
    data: emailCampaignsData,
    isLoading: isLoadingEmailCampaigns,
    refetch: refetchEmailCampaigns,
  } = useQuery(
    apiRoutes.listEmailCampaigns(currentPage, rowsPerPage),
    async () => {
      const res = await EmailCampaignsService.listEmailCampaigns(
        currentPage,
        rowsPerPage,
      );
      return res.data;
    },
    {
      refetchInterval: 10000,
      refetchOnWindowFocus: true,
    },
  );

  const cancelEmailCampaign = useMutation(
    (emailCampaignId: string) =>
      EmailCampaignsService.cancelEmailCampaign(emailCampaignId),
    {
      onSuccess: () => {
        toast({
          title: 'Campanha cancelada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        refetchEmailCampaigns();
      },
    },
  );

  function handleClickEngagementAction(
    templateId: string,
    selectedEmailEngagementActionTypes?: EngagementActionsEnum,
  ) {
    MixpanelService.track('view-campaign-details', {
      selectedEngagementActionTypes: selectedEmailEngagementActionTypes,
    });
    navigate({
      pathname: appPaths.customers.index(),
      search: getCustomersPageQueryParams({
        selectedEngagementEmailTemplateIds: templateId,
        selectedEmailEngagementActionTypes,
      }),
    });
  }

  function handleClickCancelCampaign(emailCampaignId: string) {
    setSelectedCampaignId(emailCampaignId);
    onOpenAlert();
  }

  function handleClickCampaignResults(emailCampaignId: string) {
    navigate({
      pathname: appPaths.campaigns.email.details(emailCampaignId),
    });
  }

  const toggleSelectCampaign = (campaignId: string) => {
    setSelectedEmailCampaigns({
      ...selectedEmailCampaigns,
      [campaignId]: !selectedEmailCampaigns[campaignId],
    });
  };

  function toggleSelectAllCampaigns(campaignIds: string[]) {
    setSelectedEmailCampaigns((prev) => {
      const allSelected = campaignIds.every((id) => prev[id]);

      if (allSelected) return {};
      else {
        const newState: Record<string, boolean> = { ...prev };
        campaignIds.forEach((id) => {
          newState[id] = true;
        });
        return newState;
      }
    });
  }

  const deselectAllCampaigns = () => setSelectedEmailCampaigns({});

  const handleClickSelectAllCampaigns = () => {
    if (isAllSelected || isSomeSelected) {
      deselectAllCampaigns();
    } else {
      toggleSelectAllCampaigns(allCampaignIds);
    }
  };

  const allCampaignIds =
    emailCampaignsData?.emailCampaigns?.map((campaign) => campaign.id) || [];
  const selectedCount = Object.values(selectedEmailCampaigns).filter(
    Boolean,
  ).length;

  const isAllSelected =
    selectedCount === allCampaignIds.length && allCampaignIds.length > 0;
  const isSomeSelected =
    selectedCount > 0 && selectedCount < allCampaignIds.length;

  return (
    <LoadingScreen isLoading={isLoadingEmailCampaigns}>
      <Flex justifyContent={'space-between'} mt={10} alignItems="center">
        <Heading size="md">Campanhas recentes</Heading>
        <ExportEmailCampaignsButton
          selectedCampaignsToExport={selectedEmailCampaigns}
          deselectAllCampaigns={deselectAllCampaigns}
        />
      </Flex>
      <Divider orientation="horizontal" mt={2} />
      <TableContainer overflowX="visible">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>
                <Checkbox
                  isChecked={isAllSelected}
                  isIndeterminate={isSomeSelected}
                  onChange={handleClickSelectAllCampaigns}
                />
              </Th>
              <Th>Data de envio</Th>
              <Th>Template</Th>
              <Th># destinatários</Th>
              <Th>% entregas</Th>
              <Th>% aberturas</Th>
              <Th>% cliques</Th>
              <Th># erros</Th>
              <Th>Status</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>
            {emailCampaignsData?.emailCampaigns.map(
              (emailCampaign: EmailCampaignData, index: number) => {
                return (
                  <RowEmailCampaign
                    key={index}
                    emailCampaign={emailCampaign}
                    selectedEmailCampaigns={selectedEmailCampaigns}
                    toggleSelectCampaign={toggleSelectCampaign}
                    onClickEngagementAction={handleClickEngagementAction}
                    onClickCancelCampaign={handleClickCancelCampaign}
                    onClickCampaignResults={handleClickCampaignResults}
                  />
                );
              },
            )}
          </Tbody>
        </Table>
      </TableContainer>
      <Pagination
        initialPage={currentPage}
        onChangePage={(page) => setCurrentPage(page)}
        rowsPerPage={rowsPerPage}
        totalRows={emailCampaignsData?.totalItems || 0}
        onChangeRowsPerPage={(rowsPerPage) => {
          setRowsPerPage(rowsPerPage);
        }}
        itemsLabel="clientes"
      />
      <AlertDialogBase
        isOpen={isAlertOpen}
        onClose={() => {
          setSelectedCampaignId(null);
          onCloseAlert();
        }}
        title="Confirmar cancelamento"
        onConfirm={() => {
          cancelEmailCampaign.mutateAsync(selectedCampaignId!);
          onCloseAlert();
        }}
      >
        Tem certeza que deseja cancelar o agendamento desta campanha?
      </AlertDialogBase>
    </LoadingScreen>
  );
};

export default TableEmailCampaigns;
