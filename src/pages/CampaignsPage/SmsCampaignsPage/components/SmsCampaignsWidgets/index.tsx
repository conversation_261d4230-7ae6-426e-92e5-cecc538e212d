import { Box, SimpleGrid } from '@chakra-ui/react';
import CardStatisticContainer from '../../../../../components/CardStatisticContainer';
import { MdCampaign } from 'react-icons/md';
import { apiRoutes } from '../../../../../constants/api-routes';
import { colors } from '../../../../../constants/colors';
import { BiConversation } from 'react-icons/bi';
import { PiArrowsClockwiseBold } from 'react-icons/pi';

const SmsCampaignsWidgets = () => {
  const cardsData = [
    {
      icon: <MdCampaign />,
      title: 'Total de Campanhas',
      requestRoute: apiRoutes.getTotalSmsCampaigns(new Date(), new Date()),
      bgIconColor: colors.primaryLight,
    },
    {
      icon: <BiConversation />,
      title: 'Total de Disparos',
      requestRoute: apiRoutes.getTotalSmsSent(new Date(), new Date()),
      bgIconColor: colors.secondary,
      tooltip: 'Total de mensagens enviadas em campanhas',
    },
    {
      icon: <PiArrowsClockwiseBold />,
      title: 'Engajamento',
      requestRoute: apiRoutes.getSmsEngagementRate(new Date(), new Date()),
      valueFormatter: (value: any) => `${(value || 0).toFixed(2)}%`,
      bgIconColor: colors.green,
      tooltip: 'Percentual de mensagens que tiveram cliques nos link',
    },
  ];

  return (
    <Box mb={6}>
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
        {cardsData.map(
          ({
            icon,
            requestRoute,
            title,
            bgIconColor,
            valueFormatter,
            tooltip,
          }) => (
            <CardStatisticContainer
              key={title}
              icon={icon}
              title={title}
              requestRoute={requestRoute}
              valueFormatter={valueFormatter}
              bgIconColor={bgIconColor}
              tooltip={tooltip}
            />
          ),
        )}
      </SimpleGrid>
    </Box>
  );
};

export default SmsCampaignsWidgets;
