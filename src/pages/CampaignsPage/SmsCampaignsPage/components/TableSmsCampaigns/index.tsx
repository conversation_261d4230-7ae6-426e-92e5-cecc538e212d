import {
  Badge,
  Checkbox,
  Flex,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { format } from 'date-fns';
import { PiMagnifyingGlassPlus } from 'react-icons/pi';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import LoadingScreen from '../../../../../components/LoadingScreen';
import { apiRoutes } from '../../../../../constants/api-routes';
import {
  appPaths,
  getCustomersPageQueryParams,
} from '../../../../../constants/app-paths';
import { colors } from '../../../../../constants/colors';
import { MixpanelService } from '../../../../../services/mixpanel.service';
import { EngagementActionsEnum } from '../../../../../types/EngagementActionsEnum';
import { MdOutlineRemoveRedEye } from 'react-icons/md';
import { MdFreeCancellation } from 'react-icons/md';
import ButtonIcon from '../../../../../components/ButtonIcon';
import AlertDialogBase from '../../../../../components/AlertDialog';
import { useState } from 'react';
import { SmsCampaignStatus } from '../../../../../types/Prisma';
import { SmsCampaignsService } from '../../../../../services/sms-campaigns.service';
import { SmsCampaignStatsData } from '../../../../../types/SmsCampaignStatsData';
import { useTranslation } from 'react-i18next';
import Pagination from '../../../../../components/Pagination';
import { PaginatedResponse } from '../../../../../types/PaginatedResponse';
import ExportSmsCampaignsButton from '../ExportSmsCampaignsButton';

const TableSmsCampaigns = () => {
  const { t } = useTranslation();
  const {
    isOpen: isAlertOpen,
    onClose: onCloseAlert,
    onOpen: onOpenAlert,
  } = useDisclosure();
  const toast = useToast();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(
    null,
  );
  const [selectedSmsCampaigns, setSelectedSmsCampaigns] = useState<
    Record<string, boolean>
  >({});

  const {
    data: smsCampaigns,
    isLoading: isLoadingSmsCampaigns,
    refetch: refetchSmsCampaigns,
  } = useQuery<PaginatedResponse<SmsCampaignStatsData>>(
    apiRoutes.listSmsCampaigns(currentPage, rowsPerPage),
    async () => {
      const res = await SmsCampaignsService.listSmsCampaigns({
        page: currentPage,
        perPage: rowsPerPage,
      });
      return res.data;
    },
    {
      refetchInterval: 30000,
      refetchOnWindowFocus: true,
    },
  );

  const cancelSmsCampaign = useMutation(
    (campaignId: string) => SmsCampaignsService.cancelSmsCampaign(campaignId),
    {
      onSuccess: () => {
        toast({
          title: 'Campanha cancelada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        refetchSmsCampaigns();
      },
    },
  );

  function getRealCampaignStatus(
    campaign: SmsCampaignStatsData,
  ): SmsCampaignStatus {
    if (campaign.status === 'scheduled' && !campaign.scheduled_execution_time) {
      return 'in_progress';
    }

    return campaign.status;
  }

  function getColorScheme(status: SmsCampaignStatus): string {
    const statusData: Record<SmsCampaignStatus, string> = {
      in_progress: colors.status.inProgress,
      completed: colors.status.completed,
      scheduled: colors.status.scheduled,
      failed: colors.status.failed,
      canceled: colors.status.canceled,
    };
    return statusData[status];
  }

  const toggleSelectCampaign = (campaignId: string) => {
    setSelectedSmsCampaigns({
      ...selectedSmsCampaigns,
      [campaignId]: !selectedSmsCampaigns[campaignId],
    });
  };

  function toggleSelectAllCampaigns(campaignIds: string[]) {
    setSelectedSmsCampaigns((prev) => {
      const allSelected = campaignIds.every((id) => prev[id]);

      if (allSelected) return {};
      else {
        const newState: Record<string, boolean> = { ...prev };
        campaignIds.forEach((id) => {
          newState[id] = true;
        });
        return newState;
      }
    });
  }

  const deselectAllCampaigns = () => setSelectedSmsCampaigns({});

  const handleClickSelectAllCampaigns = () => {
    if (isAllSelected || isSomeSelected) {
      deselectAllCampaigns();
    } else {
      toggleSelectAllCampaigns(allCampaignIds);
    }
  };

  const allCampaignIds =
    smsCampaigns?.data?.map((campaign) => campaign.id) || [];
  const selectedCount =
    Object.values(selectedSmsCampaigns).filter(Boolean).length;

  const isAllSelected =
    selectedCount === allCampaignIds.length && allCampaignIds.length > 0;
  const isSomeSelected =
    selectedCount > 0 && selectedCount < allCampaignIds.length;

  return (
    <LoadingScreen isLoading={isLoadingSmsCampaigns}>
      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <Text fontSize="xl" fontWeight="semibold">
          Campanhas recentes
        </Text>
        <ExportSmsCampaignsButton
          selectedCampaignsToExport={selectedSmsCampaigns}
          deselectAllCampaigns={deselectAllCampaigns}
        />
      </Flex>
      <TableContainer overflowX="visible">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>
                <Checkbox
                  isChecked={isAllSelected}
                  isIndeterminate={isSomeSelected}
                  onChange={handleClickSelectAllCampaigns}
                />
              </Th>
              <Th>Data de envio</Th>
              <Th>Template</Th>
              <Th># destinatários</Th>
              <Th># enviados</Th>
              <Th># cliques</Th>
              <Th># erros</Th>
              <Th># pedidos</Th>
              <Th>Valor pedidos</Th>
              <Th>ROI</Th>
              <Th>Status</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>
            {smsCampaigns?.data?.map((smsCampaign: SmsCampaignStatsData) => {
              return (
                <Tr
                  key={smsCampaign.id}
                  fontWeight={
                    getRealCampaignStatus(smsCampaign) === 'scheduled'
                      ? 'semibold'
                      : 'light'
                  }
                >
                  <Td>
                    <Checkbox
                      isChecked={!!selectedSmsCampaigns[smsCampaign.id]}
                      onChange={() => toggleSelectCampaign(smsCampaign.id)}
                    />
                  </Td>
                  <Td>
                    {format(
                      new Date(
                        smsCampaign.scheduled_execution_time ||
                          smsCampaign.created_at,
                      ),
                      'dd/MM/yyyy HH:mm',
                    )}
                  </Td>
                  <Td>
                    <Tooltip label={smsCampaign.template_name}>
                      <Text noOfLines={1} maxW={'180px'} display="block">
                        {smsCampaign.template_name}
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td>
                    <Text noOfLines={1} maxW={'100px'} display="block">
                      {smsCampaign.total_recipients}
                    </Text>
                  </Td>
                  <Td>
                    <Tooltip label="Total de mensagens enviadas com sucesso">
                      <Text noOfLines={1} maxW={'100px'} display="block">
                        {smsCampaign.sent.toLocaleString()}
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td>
                    <Text noOfLines={1} maxW={'100px'} display="block">
                      {smsCampaign.clicks}
                    </Text>
                  </Td>
                  <Td>
                    <Tooltip label="Total de mensagens que falharam">
                      <Text noOfLines={1} maxW={'100px'} display="block">
                        {smsCampaign.failed.toLocaleString()}
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td>
                    <Tooltip label="Total de pedidos gerados">
                      <Text noOfLines={1} maxW={'100px'} display="block">
                        {smsCampaign.total_orders.toLocaleString()}
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td>
                    <Tooltip label="Valor total dos pedidos gerados">
                      <Text noOfLines={1} maxW={'100px'} display="block">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL',
                        }).format(smsCampaign.total_orders_value)}
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td>
                    <Tooltip label="Retorno sobre investimento">
                      <Text noOfLines={1} maxW={'100px'} display="block">
                        {new Intl.NumberFormat('pt-BR', {
                          minimumFractionDigits: 1,
                          maximumFractionDigits: 1,
                        }).format(smsCampaign.roi)}
                        X
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td
                    display={'flex'}
                    width={'100px'}
                    flexDir="column"
                    justifyContent={'center'}
                    alignItems="center"
                  >
                    <Badge
                      colorScheme={getColorScheme(
                        getRealCampaignStatus(smsCampaign),
                      )}
                    >
                      {t(
                        `enums.SmsCampaignStatus.${getRealCampaignStatus(
                          smsCampaign,
                        )}`,
                      )}
                    </Badge>
                  </Td>
                  <Td>
                    {getRealCampaignStatus(smsCampaign) === 'scheduled' ? (
                      <ButtonIcon
                        tooltipLabel="Cancelar agendamento"
                        icon={
                          <MdFreeCancellation
                            color={colors.darkGrey}
                            size={20}
                          />
                        }
                        onClick={() => {
                          setSelectedCampaignId(smsCampaign.id);
                          onOpenAlert();
                        }}
                      />
                    ) : null}
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </TableContainer>
      <Pagination
        initialPage={currentPage}
        onChangePage={(page) => setCurrentPage(page)}
        rowsPerPage={rowsPerPage}
        totalRows={smsCampaigns?.meta?.totalItems || 0}
        onChangeRowsPerPage={(rowsPerPage) => {
          setRowsPerPage(rowsPerPage);
        }}
        itemsLabel="campanhas"
      />
      <AlertDialogBase
        isOpen={isAlertOpen}
        onClose={() => {
          setSelectedCampaignId(null);
          onCloseAlert();
        }}
        title="Confirmar cancelamento"
        onConfirm={() => {
          cancelSmsCampaign.mutateAsync(selectedCampaignId!);
          onCloseAlert();
        }}
      >
        Tem certeza que deseja cancelar o agendamento desta campanha?
      </AlertDialogBase>
    </LoadingScreen>
  );
};

export default TableSmsCampaigns;
