import { Card, CardBody, CardHeader, Heading, Text } from '@chakra-ui/react';
import React from 'react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { StatisticsService } from '../../../../services/statistics.service';
import SectionWrapper from '../SectionWrapper';
import OrderCohortTable from './OrderCohortTable';

const SectionOrderCohortAnalysis = () => {
  const { data: cohortData = [], isFetching: isFetchingCohortData } = useQuery(
    apiRoutes.getOrdersCohortAnalysis(),
    async () => {
      const { data } = await StatisticsService.getOrdersCohortAnalysis();
      return data;
    },
    {
      staleTime: 1000 * 60 * 60,
    },
  );

  return (
    <SectionWrapper
      title="Análise de cohort de pedidos"
      description="Entenda como os pedidos estão se comportando ao longo do tempo."
      isLoading={isFetchingCohortData}
    >
      <OrderCohortTable data={cohortData} />
    </SectionWrapper>
  );
};

export default SectionOrderCohortAnalysis;
