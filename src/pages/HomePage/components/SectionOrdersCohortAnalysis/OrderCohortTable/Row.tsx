import { Box, Divider, Text, Tooltip, Tr, Td } from '@chakra-ui/react';

const getRetentionColor = (percent: number) => {
  if (percent >= 50) return 'green.700';
  if (percent >= 40) return 'green.600';
  if (percent >= 30) return 'green.500';
  if (percent >= 20) return 'green.400';
  if (percent >= 10) return 'green.300';
  if (percent >= 5) return 'green.200';
  return 'green.100';
};

interface RowProps {
  date: string;
  cohortMatrix: any;
  maxPeriod: number;
}

const Row = ({ date, cohortMatrix, maxPeriod }: RowProps) => {
  return (
    <Tr key={date}>
      <Td fontWeight="semibold" color="gray.700">
        {date.slice(0, 7).split('-').reverse().join('/')}
      </Td>
      {[...Array(maxPeriod + 1).keys()].map((period) => {
        const value = cohortMatrix[date][period];
        if (value === undefined)
          return (
            <Td key={period} textAlign="center">
              -
            </Td>
          );

        const base = cohortMatrix[date][0];
        const percent = period === 0 ? 100 : Math.round((value / base) * 100);
        const color = period === 0 ? 'gray.200' : getRetentionColor(percent);

        return (
          <Tooltip
            label={
              <Box>
                <Text>Mês {period}</Text>
                <Divider my={2} />
                <Text>
                  {value} clientes ({percent}%) que compraram pela primeira vez
                  em {date.slice(0, 7).split('-').reverse().join('/')} continuam
                  comprando após {period} meses
                </Text>
              </Box>
            }
          >
            <Td
              key={period}
              textAlign="center"
              bg={color}
              color="black"
              _hover={{
                transform: 'scale(1.05)',
                transition: 'all 0.2s ease-in-out',
                opacity: 0.8,
              }}
            >
              <Box>{percent}%</Box>
            </Td>
          </Tooltip>
        );
      })}
    </Tr>
  );
};

export default Row;
