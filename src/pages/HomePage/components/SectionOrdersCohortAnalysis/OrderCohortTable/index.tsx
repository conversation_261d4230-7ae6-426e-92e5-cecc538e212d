import React from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Tooltip,
  Divider,
} from '@chakra-ui/react';
import Row from './Row';

// Processar os dados para formar uma estrutura de matriz por coorte
const generateCohortMatrix = (data: CohortData[], key: keyof CohortData) => {
  const matrix: Record<string, Record<number, number>> = {};

  data.forEach((item) => {
    const date = new Date(item.cohort_date).toISOString().split('T')[0];

    if (!matrix[date]) {
      matrix[date] = {};
    }
    matrix[date][item.cohort_period] = item[key] as number;
  });

  return matrix;
};

interface CohortData {
  cohort_date: string;
  cohort_period: number;
  active_customers: number;
  total_orders_value: number;
  avg_order_value: number;
}

interface OrderCohortTableProps {
  data: CohortData[];
}

const OrderCohortTable = ({ data }: OrderCohortTableProps) => {
  const cohortMatrix: Record<
    string,
    Record<number, number>
  > = generateCohortMatrix(data, 'active_customers');
  const cohortDates = Object.keys(cohortMatrix);

  const maxPeriod =
    Math.max(
      ...cohortDates.map((date) =>
        Math.max(...Object.keys(cohortMatrix[date]).map(Number)),
      ),
    ) || 18;

  if (cohortDates.length === 0) {
    return <Text>Nenhum dado encontrado</Text>;
  }

  return (
    <Table size="sm" variant="simple">
      <Thead bg="gray.100">
        <Tr>
          <Th textAlign="left" fontWeight="bold" width={'100px'}>
            Cohort
          </Th>
          {[...Array(maxPeriod + 1).keys()].map((period) => (
            <Th
              key={period}
              fontWeight="bold"
              textAlign="center"
              minWidth={'80px'}
            >
              Mês {period}
            </Th>
          ))}
        </Tr>
      </Thead>
      <Tbody>
        {cohortDates.map((date) => (
          <Row
            key={date}
            date={date}
            cohortMatrix={cohortMatrix}
            maxPeriod={maxPeriod}
          />
        ))}
      </Tbody>
    </Table>
  );
};

export default OrderCohortTable;
