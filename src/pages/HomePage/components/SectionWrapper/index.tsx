import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Center,
  Heading,
  Spinner,
  Text,
} from '@chakra-ui/react';
import React from 'react';
import { scrollbarStyles } from '../../../../styles/scrollbar.styles';

interface SectionWrapperProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  rightComponent?: React.ReactNode;
  isLoading?: boolean;
  padding?: string;
  loadingComponent?: React.ReactNode;
}
const SectionWrapper = ({
  children,
  title,
  description,
  rightComponent,
  isLoading,
  padding = '30px',
  loadingComponent = (
    <Center height="100%">
      <Spinner />
    </Center>
  ),
}: SectionWrapperProps) => {
  return (
    <Box padding={padding}>
      <Card
        overflowX="scroll"
        width={'100%'}
        boxShadow={'lg'}
        css={scrollbarStyles({ height: '1px' })}
      >
        <CardHeader display="flex" justifyContent={'space-between'}>
          <Box>
            <Heading size="md">{title}</Heading>
            {description && (
              <Text fontSize="sm" color="gray.500">
                {description}
              </Text>
            )}
          </Box>
          {rightComponent && rightComponent}
        </CardHeader>
        <CardBody>{isLoading ? loadingComponent : children}</CardBody>
      </Card>
    </Box>
  );
};

export default SectionWrapper;
