import { Box, Flex, Table, Tbody, Text, Th, Thead, Tr } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { RANKED_RFM_GROUPS } from '../../../../../constants/ranked-rfm-groups';
import Row from './Row';

const generateTransitionMatrix = (data: RFMTransitionTableProps['data']) => {
  const matrix: Record<string, Record<string, number>> = {};
  RANKED_RFM_GROUPS.forEach((current) => {
    matrix[current] = {};
    RANKED_RFM_GROUPS.forEach((prev) => {
      matrix[current][prev] = 0;
    });
  });

  data.forEach(({ prev_rfm_group, current_rfm_group, count }) => {
    if (!prev_rfm_group) return;
    matrix[current_rfm_group][prev_rfm_group] += count;
  });

  const offDiagonalValues = RANKED_RFM_GROUPS.flatMap((current) =>
    RANKED_RFM_GROUPS.filter((prev) => prev !== current).map(
      (prev) => matrix[current][prev] || 0,
    ),
  );
  const maxValue = Math.max(...offDiagonalValues, 1);

  return { matrix, maxValue };
};

const CELL_WIDTH = '105px';
const CELL_HEIGHT = '20px';

interface RFMTransitionTableProps {
  data: {
    prev_rfm_group: string;
    current_rfm_group: string;
    count: number;
  }[];
}

const RFMTransitionTable = ({ data }: RFMTransitionTableProps) => {
  const { t } = useTranslation();
  const { matrix, maxValue } = generateTransitionMatrix(data);

  return (
    <Box overflowX="auto">
      <Flex gap={4} mb={4} fontSize="sm" justifyContent="flex-end">
        <Flex alignItems="center" gap={2}>
          <Box w={4} h={4} bg="green.100" borderRadius="sm" />
          <Text>Movimentação positiva</Text>
        </Flex>
        <Flex alignItems="center" gap={2}>
          <Box w={4} h={4} bg="red.100" borderRadius="sm" />
          <Text>Movimentação negativa</Text>
        </Flex>
      </Flex>
      <Table variant="simple" size="sm">
        <Thead>
          <Tr>
            <Th>Mês anterior →</Th>
            {RANKED_RFM_GROUPS.map((prev) => (
              <Th
                key={prev}
                textTransform="capitalize"
                width={CELL_WIDTH}
                height={CELL_HEIGHT}
              >
                {t(`enums.RFMGroup.${prev}`)}
              </Th>
            ))}
          </Tr>
        </Thead>
        <Tbody>
          {RANKED_RFM_GROUPS.map((current) => (
            <Row
              key={current}
              currentGroup={current}
              matrix={matrix}
              maxValue={maxValue}
            />
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default RFMTransitionTable;
