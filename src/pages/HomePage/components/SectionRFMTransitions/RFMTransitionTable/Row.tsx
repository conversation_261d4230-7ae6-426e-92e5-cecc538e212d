import { Box, Divider, Td, Text, Tooltip, Tr } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { RANKED_RFM_GROUPS } from '../../../../../constants/ranked-rfm-groups';
import { RFMGroupsEnum } from '../../../../../types/RFMGroupsEnum';

const getColor = ({
  value,
  maxValue,
  prevGroup,
  currentGroup,
}: {
  value: number;
  maxValue: number;
  prevGroup: string;
  currentGroup: string;
}): string => {
  const percentage = value / maxValue;
  if (value === 0 || prevGroup === currentGroup) return 'transparent';

  const isGoingUp =
    RANKED_RFM_GROUPS.indexOf(prevGroup as RFMGroupsEnum) >
    RANKED_RFM_GROUPS.indexOf(currentGroup as RFMGroupsEnum);
  if (isGoingUp) {
    if (percentage > 0.85) return 'green.600';
    if (percentage > 0.7) return 'green.500';
    if (percentage > 0.55) return 'green.400';
    if (percentage > 0.4) return 'green.300';
    if (percentage > 0.25) return 'green.200';
    if (percentage > 0.05) return 'green.100';
    return 'green.50';
  } else {
    if (percentage > 0.85) return 'red.600';
    if (percentage > 0.7) return 'red.500';
    if (percentage > 0.55) return 'red.400';
    if (percentage > 0.4) return 'red.300';
    if (percentage > 0.25) return 'red.200';
    if (percentage > 0.05) return 'red.100';
    return 'red.50';
  }
};

const CELL_WIDTH = '105px';
const CELL_HEIGHT = '20px';

interface RowProps {
  currentGroup: string;
  matrix: Record<string, Record<string, number>>;
  maxValue: number;
}

const Row = ({ currentGroup, matrix, maxValue }: RowProps) => {
  const { t } = useTranslation();
  return (
    <Tr key={currentGroup}>
      <Td fontWeight="bold" textTransform="capitalize">
        {t(`enums.RFMGroup.${currentGroup}`)}
      </Td>
      {RANKED_RFM_GROUPS.map((prevGroup) => {
        const value = matrix[currentGroup][prevGroup];
        const isDiagonal = currentGroup === prevGroup;
        const bgColor = isDiagonal
          ? 'gray.200'
          : getColor({
              value,
              maxValue,
              prevGroup: prevGroup,
              currentGroup: currentGroup,
            });

        return (
          <Tooltip
            label={
              <Box>
                <Text>
                  {t(`enums.RFMGroup.${prevGroup}`)} →{' '}
                  {t(`enums.RFMGroup.${currentGroup}`)}
                </Text>
                <Divider my={2} />
                <Text>
                  {value} clientes eram{' '}
                  <strong>{t(`enums.RFMGroup.${prevGroup}`)}</strong>e se
                  tornaram{' '}
                  <strong>{t(`enums.RFMGroup.${currentGroup}`)}</strong>
                </Text>
              </Box>
            }
          >
            <Td
              key={prevGroup}
              bg={bgColor}
              textAlign="center"
              width={CELL_WIDTH}
              height={CELL_HEIGHT}
              _hover={{
                transform: 'scale(1.05)',
                transition: 'all 0.2s ease-in-out',
                opacity: 0.8,
              }}
            >
              {value > 0 ? value : '-'}
            </Td>
          </Tooltip>
        );
      })}
    </Tr>
  );
};

export default Row;
