import { Card, CardBody, CardHeader, Heading, Text } from '@chakra-ui/react';
import React from 'react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { StatisticsService } from '../../../../services/statistics.service';
import SectionWrapper from '../SectionWrapper';
import RFMTransitionTable from './RFMTransitionTable';

const SectionRFMTransitions = () => {
  const { data: rfmChanges = [], isFetching: isFetchingRfmChanges } = useQuery(
    apiRoutes.getCustomersRfmGroupTransitions(),
    async () => {
      const { data } =
        await StatisticsService.getCustomersRfmGroupTransitions();
      return data;
    },
    {
      staleTime: 1000 * 60 * 60,
    },
  );

  return (
    <SectionWrapper
      title="Mudanças na RFM com base no mês anterior"
      description="Entenda como os clientes estão se movendo entre os grupos de RFM."
      isLoading={isFetchingRfmChanges}
    >
      <RFMTransitionTable data={rfmChanges} />
    </SectionWrapper>
  );
};

export default SectionRFMTransitions;
