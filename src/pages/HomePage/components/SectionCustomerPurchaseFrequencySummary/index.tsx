import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import { colors } from '../../../../constants/colors';
import { StatisticsService } from '../../../../services/statistics.service';
import SectionedFunnelChart, {
  SectionedFunnelChartData,
} from '../../../../components/SectionedFunnelChart';
import SectionWrapper from '../SectionWrapper';

const SectionCustomerPurchaseFrequencySummary = () => {
  const navigate = useNavigate();
  const {
    data: customerPurchaseFrequencySummary = [],
    isFetching: isFetchingCustomerPurchaseFrequencySummary,
  } = useQuery(
    apiRoutes.getCustomersPurchaseFrequencySummary(),
    async () => {
      const { data } =
        await StatisticsService.getCustomersPurchaseFrequencySummary();
      return data;
    },
    {
      staleTime: 1000 * 60 * 60,
    },
  );

  const data = customerPurchaseFrequencySummary.map(
    (item, index): SectionedFunnelChartData => {
      const nextItem = customerPurchaseFrequencySummary[index + 1];
      let sectionTitle = `+${item.order_number} pedidos`;
      if (item.order_number === 1) {
        sectionTitle = '1 pedido';
      }
      return {
        sectionTitle,
        barValue: item.total_customers,
        funnelValue: nextItem?.median_days_since_prev_order,
        funnelTooltip: `${nextItem?.median_days_since_prev_order} dias entre o ${item.order_number}º e ${item.order_number + 1}º pedido`,
        barTooltip: `${item.total_customers} clientes fizeram ${item.order_number} ou mais compras`,
        onClick: () => {
          navigate(
            `${appPaths.customers.index()}?minTotalOrders=${item.order_number}`,
          );
        },
      };
    },
  );

  return (
    <SectionWrapper
      title="Frequência de compra"
      description="Entenda com que frequência os clientes estão comprando e o tempo entre cada pedido."
      isLoading={isFetchingCustomerPurchaseFrequencySummary}
    >
      <SectionedFunnelChart
        data={data}
        maxSections={7}
        barColor={'purple.700'}
        funnelColor={'purple.100'}
        width={1200}
        height={500}
        funnelValueFormatter={(value) => `${value} dias entre pedidos`}
        barValueFormatter={(value) => `${value} clientes`}
      />
    </SectionWrapper>
  );
};

export default SectionCustomerPurchaseFrequencySummary;
