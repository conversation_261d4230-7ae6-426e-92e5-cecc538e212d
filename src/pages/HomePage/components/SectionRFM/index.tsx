import {
  Button,
  FormControl,
  FormLabel,
  Select,
  Tooltip,
} from '@chakra-ui/react';
import RFMChart from '../../../../components/RFMChart';
import { maxRfmRecencyOptions } from '../../../CustomersPage/components/FilterSidebar/SectionRFM';
import SectionWrapper from '../SectionWrapper';
import { useState } from 'react';
import { StatisticsService } from '../../../../services/statistics.service';
import { PiArrowsClockwiseFill } from 'react-icons/pi';

interface SectionRFMProps {
  maxRecency: number;
  onChangeMaxRecency: (maxRecency: number) => void;
}

const SectionRFM = ({ maxRecency, onChangeMaxRecency }: SectionRFMProps) => {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = async () => {
    await StatisticsService.clearRFMCache(maxRecency);
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <SectionWrapper
      title="Sua matriz RFM"
      description="Entenda como seus clientes estão se comportando com base na matriz RFM."
      rightComponent={
        <FormControl display="flex" width="fit-content" alignItems="center">
          <FormLabel>Período máximo</FormLabel>
          <Select
            value={maxRecency}
            width="200px"
            onChange={(e) => onChangeMaxRecency(Number(e.target.value))}
          >
            {maxRfmRecencyOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.name}
              </option>
            ))}
          </Select>
          <Tooltip
            placement="left"
            label="Recalcular dados: obtenha os dados mais recentes da matriz RFM"
          >
            <Button ml={2} onClick={handleRefresh}>
              <PiArrowsClockwiseFill />
            </Button>
          </Tooltip>
        </FormControl>
      }
    >
      <RFMChart maxRecency={maxRecency} refreshKey={refreshKey} />
    </SectionWrapper>
  );
};

export default SectionRFM;
