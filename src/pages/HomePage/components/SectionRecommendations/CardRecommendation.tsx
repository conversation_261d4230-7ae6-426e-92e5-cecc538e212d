import React, { ReactElement, ReactNode } from 'react';
import {
  FaChartLine,
  FaClock,
  FaCrown,
  FaExclamationTriangle,
  FaGhost,
  FaHandshake,
  FaHeart,
  FaShieldAlt,
  FaSnowflake,
  FaStarHalfAlt,
  FaUserPlus,
} from 'react-icons/fa';
import { MdOutlineCampaign } from 'react-icons/md';
import { RiNodeTree } from 'react-icons/ri';
import CardInfo from '../../../../components/CardInfo';
import { colors, rfmColors } from '../../../../constants/colors';
import {
  Recommendation,
  RecommendationType,
} from '../../../../types/Recommendation';

const recommendationIcons: Record<RecommendationType, ReactNode> = {
  create_automation: <RiNodeTree size="24px" />,
  create_campaign: <MdOutlineCampaign size="24px" />,
};

const ctaLabels: Record<RecommendationType, string> = {
  create_automation: 'Criar Automação',
  create_campaign: '<PERSON><PERSON><PERSON>',
};

const recommendationColors: Record<RecommendationType, string> = {
  create_automation: colors.green,
  create_campaign: colors.primary,
};

const rfmIcons: Record<string, ReactElement> = {
  champions: <FaCrown />,
  loyal_customers: <FaHeart />,
  potential_loyalists: <FaStarHalfAlt />,
  recent_customers: <FaUserPlus />,
  promising: <FaChartLine />,
  need_attention: <FaExclamationTriangle />,
  about_to_sleep: <FaClock />,
  cannot_lose_them: <FaHandshake />,
  at_risk: <FaShieldAlt />,
  hibernating: <FaSnowflake />,
  lost_customers: <FaGhost />,
};

const randomIcons = [
  FaCrown,
  FaHeart,
  FaStarHalfAlt,
  FaChartLine,
  FaClock,
  FaGhost,
  FaHandshake,
  FaShieldAlt,
  FaSnowflake,
  FaExclamationTriangle,
];
const randomColors = Object.values(rfmColors);

const getRandomElement = <T,>(array: T[]): T =>
  array[Math.floor(Math.random() * array.length)];

interface CardRecommendationProps {
  recommendation: Recommendation;
  onClickRecommendation: (recommendation: Recommendation) => void;
}

const CardRecommendation: React.FC<CardRecommendationProps> = ({
  recommendation,
  onClickRecommendation,
}) => {
  const { rfmGroup } = recommendation.data;
  const IconComponent =
    rfmIcons[rfmGroup] || React.createElement(getRandomElement(randomIcons));
  const colorScheme =
    rfmColors[rfmGroup as keyof typeof rfmColors] ||
    getRandomElement(randomColors);

  return (
    <CardInfo
      bgTitle={
        recommendation.action === 'create_campaign' ? 'Campanha' : 'Automação'
      }
      title={recommendation.title}
      description={recommendation.description}
      info={recommendation.info}
      icon={React.cloneElement(IconComponent, { color: colorScheme.primary })}
      iconBgColor={colorScheme.secondary}
      onClick={() => onClickRecommendation(recommendation)}
    />
  );
};

export default CardRecommendation;
