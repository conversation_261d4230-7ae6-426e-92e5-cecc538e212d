import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Center,
  Heading,
  Spinner,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import { RecommendationsService } from '../../../../services/recommendations.service';
import { setCampaignRecommendation } from '../../../../state/campaignCreationSlice';
import {
  CampaignRecommendation,
  Recommendation,
} from '../../../../types/Recommendation';
import CardRecommendation from './CardRecommendation';
import { useAppModuleAccessGuard } from '../../../../hooks/useAppModuleAccessGuard';
import { useMemo } from 'react';
import SectionWrapper from '../SectionWrapper';

const SectionRecommendations = () => {
  const { checkUserHasPathAccess } = useAppModuleAccessGuard();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { data: recommendations = [], isFetching: isFetchingRecommendations } =
    useQuery(apiRoutes.listCompanyRecommendations(), async () => {
      const { data } =
        await RecommendationsService.listCompanyRecommendations();
      return data;
    });

  function handleClickRecommendation(recommendation: Recommendation) {
    if (recommendation.action === 'create_campaign') {
      dispatch(
        setCampaignRecommendation(recommendation as CampaignRecommendation),
      );
      navigate(appPaths.campaigns.create());
    } else if (recommendation.action === 'create_automation') {
      navigate(appPaths.automations.backgroundAutomations.createAutomation());
    }
  }

  const isUserAllowedToCreateCampaign = checkUserHasPathAccess(
    appPaths.campaigns.create(),
  );
  const isUserAllowedToCreateAutomation = checkUserHasPathAccess(
    appPaths.automations.backgroundAutomations.createAutomation(),
  );

  const recommendationsAllowedForUser = useMemo(
    () =>
      recommendations.filter((recommendation) => {
        if (recommendation.action === 'create_campaign') {
          return isUserAllowedToCreateCampaign;
        } else if (recommendation.action === 'create_automation') {
          return isUserAllowedToCreateAutomation;
        }
        return false;
      }),
    [
      recommendations,
      isUserAllowedToCreateCampaign,
      isUserAllowedToCreateAutomation,
    ],
  );

  if (!isUserAllowedToCreateCampaign && !isUserAllowedToCreateAutomation) {
    return null;
  }

  return (
    <SectionWrapper
      title="Nossas recomendações"
      description="Recomendações personalizadas baseadas nos seus dados."
      isLoading={isFetchingRecommendations}
      padding="0px"
    >
      <Box display="flex" height="100%" gap={3} overflowX="scroll">
        {recommendationsAllowedForUser.length === 0 ? (
          <Box>
            Não há nenhuma recomendação :(. Nossa{' '}
            <strong>Inteligência Artificial</strong> ainda está aprendendo sobre
            os seus dados
          </Box>
        ) : (
          recommendationsAllowedForUser.map((recommendation, index) => (
            <CardRecommendation
              recommendation={recommendation}
              onClickRecommendation={handleClickRecommendation}
            />
          ))
        )}
      </Box>
    </SectionWrapper>
  );
};

export default SectionRecommendations;
