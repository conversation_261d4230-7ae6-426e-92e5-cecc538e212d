import {
  Center,
  VStack,
  Icon,
  Heading,
  Text,
  Button,
  Grid,
  GridItem,
  Flex,
} from '@chakra-ui/react';
import { MdIntegrationInstructions, MdErrorOutline } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import LoadingScreen from '../../../../components/LoadingScreen';
import { appPaths } from '../../../../constants/app-paths';

interface IntegrationStatusScreenProps {
  isLoading: boolean;
  error: boolean;
  activeIntegrations: any[];
  children: React.ReactNode;
}

const IntegrationStatusScreen = ({
  isLoading,
  error,
  activeIntegrations,
  children,
}: IntegrationStatusScreenProps) => {
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <LoadingScreen isLoading={true}>
        <div />
      </LoadingScreen>
    );
  }

  const showNoIntegrations = !error && activeIntegrations.length === 0;
  const showError = error;

  if (showNoIntegrations || showError) {
    return (
      <Grid
        templateColumns="1fr"
        templateRows="auto 1fr"
        height="100vh"
        width="100%"
      >
        <GridItem>
          <Flex padding={4} justifyContent="center" alignItems="center">
            <Heading size="md" color="gray.600">
              Produtos
            </Heading>
          </Flex>
        </GridItem>
        <GridItem>
          <Center height="100%" width="100%">
            <VStack spacing={6} textAlign="center" maxW="md">
              <Icon
                as={showError ? MdErrorOutline : MdIntegrationInstructions}
                boxSize={16}
                color="gray.400"
              />
              <VStack spacing={2}>
                <Heading size="lg" color="gray.600">
                  {showError
                    ? 'Erro ao carregar integrações'
                    : 'Nenhuma integração ativa'}
                </Heading>
                <Text color="gray.500" fontSize="md">
                  {showError
                    ? 'Ocorreu um erro ao carregar as integrações. Tente recarregar a página.'
                    : 'É necessário ativar uma integração para sincronizar e listar seus produtos.'}
                </Text>
              </VStack>
              <Button
                colorScheme="blue"
                size="lg"
                onClick={() =>
                  showError
                    ? window.location.reload()
                    : navigate(appPaths.settings.integrationSettings.index())
                }
              >
                {showError ? 'Recarregar Página' : 'Configurar Integrações'}
              </Button>
            </VStack>
          </Center>
        </GridItem>
      </Grid>
    );
  }

  return <>{children}</>;
};

export default IntegrationStatusScreen;
