import {
  Badge,
  Flex,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  Image,
  Box,
  Skeleton,
  HStack,
  Icon,
} from '@chakra-ui/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useQuery } from 'react-query';
import { useSearchParams } from 'react-router-dom';
import {
  MdOutlineRemoveRedEye,
  MdWarning,
  MdCheckCircle,
} from 'react-icons/md';
import { useState, useCallback } from 'react';
import { PaginatedResponse } from '../../../../types/PaginatedResponse';
import { colors } from '../../../../constants/colors';
import LoadingScreen from '../../../../components/LoadingScreen';
import ButtonIcon from '../../../../components/ButtonIcon';
import Pagination from '../../../../components/Pagination';
import { ProductsService } from '../../../../services/products.service';
import ProductDetailsModal from '../ModalProductDetails';
import { IntegrationUtils } from '../../../../utils/integration.utils';
import { useProductSearchParams } from '../../../../hooks/useProductSearchParams';
import { ProductSql } from '../../../../types/ProductPaginatedSql';
import { MoneyUtils } from '../../../../utils/money.utils';
import { scrollbarStyles } from '../../../../styles/scrollbar.styles';

const TableProductsPaginated = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [imageLoadingStates, setImageLoadingStates] = useState<
    Record<string, boolean>
  >({});

  const {
    page,
    perPage,
    searchQuery,
    status,
    source,
    minPrice,
    maxPrice,
    minStock,
    maxStock,
    sortBy,
  } = useProductSearchParams();

  const selectedIntegrationName = source
    ? IntegrationUtils.getIntegrationDisplayName(source)
    : '';

  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const currentPage = Math.max(1, Number(page) || 1);
  const currentPerPage = Math.max(1, Number(perPage) || 10);

  const { data: products, isLoading: isLoadingProducts } = useQuery<
    PaginatedResponse<ProductSql>
  >(
    [
      'products',
      source,
      currentPage,
      currentPerPage,
      searchQuery,
      status,
      minPrice,
      maxPrice,
      minStock,
      maxStock,
      sortBy,
    ],
    async () => {
      const { data: paginatedData } = await ProductsService.listProducts({
        source,
        page: currentPage,
        perPage: currentPerPage,
        searchQuery: searchQuery || undefined,
        status: status || undefined,
        minPrice: minPrice,
        maxPrice: maxPrice,
        minStock: minStock,
        maxStock: maxStock,
        sortBy: sortBy || undefined,
      });

      return paginatedData;
    },
    {
      enabled: !!source,
      refetchInterval: 30000,
      refetchOnWindowFocus: true,
      keepPreviousData: true,
    },
  );

  function getStatusColorScheme(status: string): string {
    const statusData: Record<string, string> = {
      ACTIVE: colors.status.completed || 'green',
      DRAFT: 'orange',
    };
    return statusData[status] ?? 'gray';
  }

  function getStockStatus(product: ProductSql) {
    if (!product.hasStock || product.totalStock === 0) {
      return { color: 'red.500', icon: MdWarning, label: 'Sem estoque' };
    }
    if (product.totalStock <= 5) {
      return { color: 'orange.500', icon: MdWarning, label: 'Estoque baixo' };
    }
    return { color: 'green.500', icon: MdCheckCircle, label: 'Em estoque' };
  }

  const handleViewProduct = (productId: string) => {
    setSelectedProductId(productId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedProductId('');
  };

  const handleProductNameClick = (productId: string) => {
    setSelectedProductId(productId);
    setIsModalOpen(true);
  };

  const handlePageChange = useCallback(
    (newPage: number) => {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set('page', String(newPage));
      setSearchParams(newParams, { replace: false });

      const tableContainer = document.querySelector('[data-table-container]');
      if (tableContainer) {
        tableContainer.scrollTop = 0;
      }
    },
    [searchParams, setSearchParams],
  );

  const handlePerPageChange = useCallback(
    (newLimit: number) => {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set('perPage', String(newLimit));
      newParams.set('page', '1');
      setSearchParams(newParams, { replace: false });
    },
    [searchParams, setSearchParams],
  );

  return (
    <LoadingScreen isLoading={isLoadingProducts}>
      <Box height="100vh" display="flex" flexDirection="column">
        <Flex justifyContent="space-between" alignItems="center" mb={4} px={4}>
          <Text fontSize="xl" fontWeight="semibold" color="gray.700">
            Produtos - {selectedIntegrationName}
          </Text>

          <Flex gap={4} alignItems="center">
            {products?.meta && (
              <HStack spacing={4}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  {products.meta.totalItems} produto(s) encontrado(s)
                </Text>
              </HStack>
            )}
          </Flex>
        </Flex>

        <Box flex="1" overflow="hidden" px={4}>
          <TableContainer
            data-table-container
            overflowY="auto"
            height="100%"
            borderRadius="md"
            border="1px solid"
            borderColor="gray.200"
            css={scrollbarStyles({ height: '4px' })}
          >
            <Table variant="simple" size="md">
              <Thead
                position="sticky"
                top={0}
                bg="white"
                boxShadow="sm"
                zIndex={1}
              >
                <Tr sx={{ '& th': { px: 6, py: 4 } }}>
                  <Th>Imagem</Th>
                  <Th>Nome do Produto</Th>
                  <Th>ID Integração</Th>
                  <Th isNumeric>Preço Médio</Th>
                  <Th isNumeric>Estoque</Th>
                  <Th isNumeric>Variantes</Th>
                  <Th>Status</Th>
                  <Th>Última Atualização</Th>
                  <Th textAlign="center">Ações</Th>
                </Tr>
              </Thead>
              <Tbody>
                {products?.data?.map((product: ProductSql) => {
                  const isImageLoading = imageLoadingStates[product.id] ?? true;
                  const stockStatus = getStockStatus(product);

                  return (
                    <Tr
                      key={product.id}
                      _hover={{ bg: 'gray.50' }}
                      opacity={!product.isActive ? 0.7 : 1}
                    >
                      <Td borderColor="gray.100">
                        {product.imageUrl ? (
                          <Box position="relative" boxSize="50px">
                            {isImageLoading && (
                              <Skeleton
                                position="absolute"
                                top={0}
                                left={0}
                                boxSize="50px"
                                borderRadius="md"
                                zIndex={1}
                              />
                            )}
                            <Image
                              src={product.imageUrl}
                              alt={product.name}
                              boxSize="50px"
                              objectFit="cover"
                              borderRadius="md"
                              border="1px solid"
                              borderColor="gray.200"
                              fallbackSrc="/placeholder-product.png"
                              onLoad={() =>
                                setImageLoadingStates((prev) => ({
                                  ...prev,
                                  [product.id]: false,
                                }))
                              }
                            />
                          </Box>
                        ) : (
                          <Flex
                            boxSize="50px"
                            bg="gray.50"
                            borderRadius="md"
                            align="center"
                            justify="center"
                            border="1px solid"
                            borderColor="gray.200"
                            overflow="hidden"
                            textAlign="center"
                          >
                            <Text
                              fontSize="xs"
                              color="gray.400"
                              textAlign="center"
                              wordBreak="break-word"
                              whiteSpace="normal"
                              lineHeight="short"
                            >
                              Sem{'\n'}Imagem
                            </Text>
                          </Flex>
                        )}
                      </Td>
                      <Td borderColor="gray.100">
                        <Tooltip label={product.name} placement="top">
                          <Text
                            noOfLines={2}
                            maxW={'250px'}
                            display="block"
                            cursor="pointer"
                            color="black"
                            fontWeight="semibold"
                            _hover={{
                              textDecoration: 'underline',
                              color: 'gray.700',
                            }}
                            onClick={() => handleProductNameClick(product.id)}
                            lineHeight="1.3"
                          >
                            {product.name}
                          </Text>
                        </Tooltip>
                      </Td>
                      <Td borderColor="gray.100">
                        <Text
                          noOfLines={1}
                          maxW={'120px'}
                          display="block"
                          fontFamily="mono"
                          fontSize="sm"
                          color="gray.600"
                        >
                          {product.sourceId}
                        </Text>
                      </Td>
                      <Td borderColor="gray.100" isNumeric>
                        <Text
                          fontWeight="semibold"
                          color="green.600"
                          fontSize="sm"
                        >
                          {MoneyUtils.formatCurrency(product.averagePrice)}
                        </Text>
                      </Td>
                      <Td borderColor="gray.100" isNumeric>
                        <HStack justify="flex-end" spacing={1}>
                          <Icon
                            as={stockStatus.icon}
                            color={stockStatus.color}
                            boxSize={4}
                          />
                          <Text
                            color={stockStatus.color}
                            fontWeight="medium"
                            fontSize="sm"
                          >
                            {product.totalStock}
                          </Text>
                        </HStack>
                      </Td>
                      <Td borderColor="gray.100" isNumeric>
                        <Text
                          color={
                            product.numberOfVariants <= 1
                              ? 'orange.500'
                              : 'gray.700'
                          }
                          fontWeight={
                            product.numberOfVariants <= 1 ? 'bold' : 'normal'
                          }
                          fontSize="sm"
                        >
                          {product.numberOfVariants}
                        </Text>
                      </Td>
                      <Td borderColor="gray.100">
                        <HStack spacing={2}>
                          <Badge
                            colorScheme={getStatusColorScheme(product.status)}
                            variant="subtle"
                            borderRadius="full"
                            px={2}
                            py={1}
                            fontSize="xs"
                            fontWeight="medium"
                          >
                            {product.status}
                          </Badge>
                        </HStack>
                      </Td>
                      <Td borderColor="gray.100">
                        <Text fontSize="xs" color="gray.500">
                          {format(
                            new Date(product.updatedAt),
                            "dd/MM/yyyy 'às' HH:mm",
                            { locale: ptBR },
                          )}
                        </Text>
                      </Td>

                      <Td borderColor="gray.100" textAlign="center">
                        <ButtonIcon
                          tooltipLabel="Visualizar detalhes do produto"
                          icon={
                            <MdOutlineRemoveRedEye
                              color={colors.darkGrey}
                              size={18}
                            />
                          }
                          onClick={() => handleViewProduct(product.id)}
                        />
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
            </Table>
          </TableContainer>

          {!isLoadingProducts &&
            products?.data &&
            products.data.length === 0 && (
              <Flex
                justifyContent="center"
                alignItems="center"
                py={12}
                flexDirection="column"
                gap={2}
              >
                <Text color="gray.500" fontSize="lg" fontWeight="medium">
                  Nenhum produto encontrado
                </Text>
                <Text color="gray.400" fontSize="sm">
                  Tente ajustar os filtros de busca para encontrar produtos.
                </Text>
              </Flex>
            )}
        </Box>

        {products?.data && products.data.length > 0 && (
          <Box
            mt={4}
            px={4}
            py={3}
            bg="white"
            borderTop="1px solid"
            borderColor="gray.200"
            borderRadius="0 0 md md"
          >
            <Pagination
              initialPage={currentPage}
              rowsPerPage={currentPerPage}
              onChangePage={handlePageChange}
              onChangeRowsPerPage={handlePerPageChange}
              totalRows={products?.meta?.totalItems || 0}
              itemsLabel="produtos"
            />
          </Box>
        )}

        {isModalOpen && (
          <ProductDetailsModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            productId={selectedProductId}
          />
        )}
      </Box>
    </LoadingScreen>
  );
};

export default TableProductsPaginated;
