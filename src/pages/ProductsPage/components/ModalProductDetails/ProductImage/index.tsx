import { Box, Center, Skeleton, Text, Image } from '@chakra-ui/react';
import { useState } from 'react';

const ProductImage = ({ src, alt = 'Imagem do produto', ...props }: any) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const borderColor = 'gray.200';
  const bgColor = 'gray.50';
  const iconColor = 'gray.400';

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  if (!src || imageError) {
    return (
      <Center
        {...props}
        bg={bgColor}
        border="2px"
        borderColor={borderColor}
        borderRadius="xl"
        overflow="hidden"
        flexDirection="column"
        gap={2}
      >
        <Text fontSize="xs" color={iconColor} textAlign="center" px={2}>
          Sem Imagem
        </Text>
      </Center>
    );
  }

  return (
    <Box
      position="relative"
      {...props}
      border="2px"
      borderColor={borderColor}
      borderRadius="xl"
      overflow="hidden"
    >
      {isLoading && (
        <Skeleton
          width="100%"
          height="100%"
          position="absolute"
          top={0}
          left={0}
          zIndex={1}
        />
      )}
      <Image
        src={src}
        alt={alt}
        objectFit="cover"
        width="100%"
        height="100%"
        onError={handleImageError}
        onLoad={handleImageLoad}
        transition="opacity 0.3s"
        opacity={isLoading ? 0 : 1}
        zIndex={2}
      />
    </Box>
  );
};

export default ProductImage;
