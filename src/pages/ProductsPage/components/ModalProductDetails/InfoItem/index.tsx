import { Box, HStack, Text } from '@chakra-ui/react';
import NAValue from '../FallbackValue';
import { VariantItemProps } from '../VariantItem';

interface InfoItemProps {
  label: string;
  value: any;
  icon?: any;
  isCode?: boolean;
  theme: VariantItemProps['theme'];
}

const InfoItem = ({
  label,
  value,
  icon,
  isCode = false,
  theme,
  ...props
}: InfoItemProps) => (
  <Box {...props}>
    <HStack spacing={2} mb={2}>
      {icon && <Box as={icon} size="14px" color={theme.mutedColor} />}
      <Text fontSize="sm" fontWeight="600" color={theme.mutedColor}>
        {label}
      </Text>
    </HStack>
    <Box
      fontSize="sm"
      color={theme.textColor}
      fontFamily={isCode ? 'mono' : 'inherit'}
      bg={isCode ? theme.sectionBg : 'transparent'}
      px={isCode ? 3 : 0}
      py={isCode ? 2 : 0}
      borderRadius={isCode ? 'md' : 'none'}
      border={isCode ? '1px' : 'none'}
      borderColor={isCode ? theme.borderColor : 'transparent'}
      wordBreak="break-all"
    >
      {value || <NAValue tooltipText={`${label} não informado`} />}
    </Box>
  </Box>
);

export default InfoItem;
