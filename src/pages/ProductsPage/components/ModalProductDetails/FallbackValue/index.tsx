import { Tooltip, HStack, Text } from '@chakra-ui/react';
import { FiHelpCircle } from 'react-icons/fi';

const NAValue = ({
  tooltipText = 'Informação não disponível',
  color = 'gray.400',
  fontSize = 'sm',
  fontStyle = 'italic',
}: {
  tooltipText?: string;
  color?: string;
  fontSize?: string;
  fontStyle?: string;
}) => (
  <Tooltip
    label={tooltipText}
    placement="top"
    hasArrow
    bg="gray.700"
    color="white"
    fontSize="xs"
    px={3}
    py={2}
    borderRadius="md"
  >
    <HStack spacing={1} cursor="help">
      <Text color={color} fontSize={fontSize} fontStyle={fontStyle}>
        N/A
      </Text>
      <FiHelpCircle size="12px" color="var(--chakra-colors-gray-400)" />
    </HStack>
  </Tooltip>
);

export default NAValue;
