import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel as ChakraFormLabel,
  Input,
  Select,
  useToast,
} from '@chakra-ui/react';
import { Controller, useForm } from 'react-hook-form';
import { useCallback, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { scrollbarStyles } from '../../../../styles/scrollbar.styles';
import FormLabel from '../../../../components/FormLabel';
import InputSelect, { SelectOption } from '../../../../components/InputSelect';
import useSelectOptionsQuery from '../../../../hooks/useSelectOptionsQuery';
import { apiRoutes } from '../../../../constants/api-routes';
import { ProductsService } from '../../../../services/products.service';
import { useProductSearchParams } from '../../../../hooks/useProductSearchParams';
import { ProductFiltersEnum } from '../../../../types/ProdutFiltersEnum';
import { ProcessedIntegration } from '../../../../utils/integration.utils';

interface FilterSidebarProps {
  isOpen: boolean;
  activeIntegrations: ProcessedIntegration[];
}

interface FormData {
  source: string;
  status: SelectOption[];
  price: { minValue: string; maxValue: string };
  stock: { minValue: string; maxValue: string };
  sortBy: string;
}

export function FilterSidebar({
  isOpen,
  activeIntegrations,
}: FilterSidebarProps) {
  const [searchParams, setSearchParams] = useSearchParams();
  const toast = useToast();

  const { source, status, minPrice, maxPrice, minStock, maxStock, sortBy } =
    useProductSearchParams();

  const { register, handleSubmit, reset, control, setValue, watch } =
    useForm<FormData>({
      defaultValues: {
        source: source || '',
        status: [],
        price: {
          minValue: minPrice ? (minPrice / 100).toString() : '',
          maxValue: maxPrice ? (maxPrice / 100).toString() : '',
        },
        stock: {
          minValue: minStock?.toString() || '',
          maxValue: maxStock?.toString() || '',
        },
        sortBy: sortBy || '',
      },
    });

  const currentSource = watch('source');

  const activeSource = currentSource || source;

  const statusOptions = useSelectOptionsQuery(
    apiRoutes.listProductVariantsFieldValues('status', activeSource),
    () =>
      ProductsService.listProductVariantsFieldValues('status', activeSource),
    'status',
  );

  useEffect(() => {
    const currentFormData: FormData = {
      source: source || '',
      status: [],
      price: {
        minValue: minPrice ? (minPrice / 100).toString() : '',
        maxValue: maxPrice ? (maxPrice / 100).toString() : '',
      },
      stock: {
        minValue: minStock?.toString() || '',
        maxValue: maxStock?.toString() || '',
      },
      sortBy: sortBy || '',
    };

    reset(currentFormData);
  }, [source, minPrice, maxPrice, minStock, maxStock, sortBy, reset]);

  useEffect(() => {
    if (status && statusOptions && statusOptions.length > 0) {
      const statusIds = status.split(',');
      const selectedStatusOptions = statusOptions.filter((option) =>
        statusIds.includes(option.value),
      );
      setValue('status', selectedStatusOptions);
    }
  }, [status, statusOptions, setValue]);

  const handleSourceChange = useCallback(
    (newSource: string) => {
      setValue('status', []);

      const newParams = new URLSearchParams(searchParams);
      if (newSource) {
        newParams.set('source', newSource);
      } else {
        newParams.delete('source');
      }
      newParams.delete('status');
      newParams.set('page', '1');
      newParams.set('perPage', '10');

      setSearchParams(newParams);
    },
    [searchParams, setSearchParams, setValue],
  );

  if (!isOpen) {
    return null;
  }

  function onSubmit(data: FormData) {
    const { source: newSource, status, price, stock, sortBy } = data;

    const queryValues: Record<string, string> = {};

    if (newSource) {
      queryValues[ProductFiltersEnum.SOURCE] = newSource;
    }

    if (status && status.length > 0) {
      queryValues[ProductFiltersEnum.STATUS] = status
        .map((option) => option.value)
        .join(',');
    }

    if (price?.minValue) {
      const minPriceInCents = Math.round(parseFloat(price.minValue) * 100);
      queryValues[ProductFiltersEnum.MIN_PRICE] = minPriceInCents.toString();
    }
    if (price?.maxValue) {
      const maxPriceInCents = Math.round(parseFloat(price.maxValue) * 100);
      queryValues[ProductFiltersEnum.MAX_PRICE] = maxPriceInCents.toString();
    }

    if (stock?.minValue) {
      queryValues[ProductFiltersEnum.MIN_STOCK] = stock.minValue;
    }
    if (stock?.maxValue) {
      queryValues[ProductFiltersEnum.MAX_STOCK] = stock.maxValue;
    }

    if (sortBy) {
      const validSortValues = [
        'nameAsc',
        'nameDesc',
        'sourceCreatedAtAsc',
        'sourceCreatedAtDesc',
      ];
      if (validSortValues.includes(sortBy)) {
        queryValues[ProductFiltersEnum.SORT_BY] = sortBy;
      }
    }

    const newParams = new URLSearchParams();
    newParams.set('page', '1');
    newParams.set('perPage', '10');

    const searchQuery = searchParams.get('searchQuery');
    if (searchQuery) {
      newParams.set('searchQuery', searchQuery);
    }

    Object.entries(queryValues).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        newParams.set(key, value);
      }
    });

    setSearchParams(newParams);

    toast({
      title: 'Filtros aplicados com sucesso',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  }

  function handleClearFilters() {
    const currentIntegration = source || activeIntegrations[0]?.key || '';

    const newParams = new URLSearchParams();
    if (currentIntegration) {
      newParams.set('source', currentIntegration);
    }
    newParams.set('page', '1');
    newParams.set('perPage', '10');

    setSearchParams(newParams);

    reset({
      source: currentIntegration,
      status: [],
      price: { minValue: '', maxValue: '' },
      stock: { minValue: '', maxValue: '' },
      sortBy: '',
    });

    toast({
      title: 'Filtros limpos',
      status: 'info',
      duration: 2000,
      isClosable: true,
    });
  }

  const totalAppliedFilters = [
    status,
    Number(minPrice) || Number(maxPrice),
    Number(minStock) || Number(maxStock),
    sortBy,
  ].filter((filter) => !!filter).length;

  return (
    <Box height="100%" overflow="hidden">
      <form onSubmit={handleSubmit(onSubmit)} style={{ height: '100%' }}>
        <Flex flexDir="column" height="100%">
          <Box
            overflowY="auto"
            flex="1"
            css={scrollbarStyles({ width: '4px' })}
            p={2}
            gap={2}
          >
            <Flex
              overflowY="scroll"
              flexDir="column"
              maxHeight="69vh"
              flexGrow={1}
              css={scrollbarStyles({ width: '4px' })}
              paddingRight="5px"
              gap={3}
            >
              <FormControl>
                <ChakraFormLabel>Integração</ChakraFormLabel>
                <Select
                  size="md"
                  bg="white"
                  placeholder="Selecione uma integração"
                  {...register('source', {
                    onChange: (e) => handleSourceChange(e.target.value),
                  })}
                >
                  {activeIntegrations.map((integration) => (
                    <option key={integration.key} value={integration.key}>
                      {integration.name}
                    </option>
                  ))}
                </Select>
              </FormControl>

              <FormControl>
                <ChakraFormLabel>Ordenar por</ChakraFormLabel>
                <Select
                  size="md"
                  bg="white"
                  placeholder="Selecione"
                  {...register('sortBy')}
                >
                  <option value="nameAsc">Nome - A-Z</option>
                  <option value="nameDesc">Nome - Z-A</option>
                  <option value="sourceCreatedAtAsc">Mais Antigos</option>
                  <option value="sourceCreatedAtDesc">Mais Recentes</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel
                  size="sm"
                  tooltip="Mostrar produtos com base em seu status"
                >
                  Status
                </FormLabel>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <InputSelect
                      key={`status-${activeSource}`}
                      options={statusOptions || []}
                      isMulti
                      value={field.value || []}
                      onChange={(newValue) => {
                        field.onChange(newValue || []);
                      }}
                    />
                  )}
                />
              </FormControl>

              <FormControl>
                <ChakraFormLabel>Faixa de preço (R$)</ChakraFormLabel>
                <Flex gap={3}>
                  <Input
                    placeholder="Min"
                    type="number"
                    size="md"
                    bg="white"
                    step="0.01"
                    min="0"
                    {...register('price.minValue')}
                  />
                  <Input
                    placeholder="Max"
                    type="number"
                    size="md"
                    bg="white"
                    step="0.01"
                    min="0"
                    {...register('price.maxValue')}
                  />
                </Flex>
              </FormControl>

              <FormControl>
                <ChakraFormLabel>Estoque</ChakraFormLabel>
                <Flex gap={3}>
                  <Input
                    placeholder="Min"
                    type="number"
                    size="md"
                    bg="white"
                    min="0"
                    {...register('stock.minValue')}
                  />
                  <Input
                    placeholder="Max"
                    type="number"
                    size="md"
                    bg="white"
                    min="0"
                    {...register('stock.maxValue')}
                  />
                </Flex>
              </FormControl>
            </Flex>
          </Box>

          <Box bg="white" gap={4} p={4} pb={4}>
            {totalAppliedFilters > 0 ? (
              <Button
                flexGrow={1}
                width="100%"
                variant="outline"
                onClick={handleClearFilters}
              >
                Limpar ({totalAppliedFilters})
              </Button>
            ) : (
              <Button type="submit" variant="primary" width="100%">
                Filtrar
              </Button>
            )}

            {totalAppliedFilters > 0 && (
              <Flex width="100%" gap={3} mt={2}>
                <Button type="submit" flex="1" variant="primary" p={5}>
                  Filtrar
                </Button>
              </Flex>
            )}
          </Box>
        </Flex>
      </form>
    </Box>
  );
}

export default FilterSidebar;
