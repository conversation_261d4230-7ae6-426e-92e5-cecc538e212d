import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  Heading,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  Tooltip,
  useToast,
} from '@chakra-ui/react';
import { useEffect, useState, useMemo, useCallback } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { BsSearch } from 'react-icons/bs';
import { MdSync } from 'react-icons/md';
import { useSearchParams } from 'react-router-dom';
import { useQuery, useQueryClient, useMutation } from 'react-query';
import FilterSidebar from './components/FilterSidebar';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { scrollbarStyles } from '../../styles/scrollbar.styles';
import TableProductsPaginated from './components/TableProductsPaginated';
import { apiRoutes } from '../../constants/api-routes';
import { IntegrationsService } from '../../services/integrations.service';
import { ProductsService } from '../../services/products.service';
import {
  IntegrationUtils,
  ProcessedIntegration,
} from '../../utils/integration.utils';
import { SourceIntegration } from '../../types/Prisma';
import LoadingScreen from '../../components/LoadingScreen';
import IntegrationStatusScreen from './components/IntegrationStatusScreen';
import { useProductSearchParams } from '../../hooks/useProductSearchParams';

interface FormData {
  searchValue: string;
  selectedIntegration: string;
  isFilterOpen: boolean;
}

const ProductsPage = () => {
  const { source, searchQuery } = useProductSearchParams();
  const toast = useToast();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();

  const [searchInputValue, setSearchInputValue] = useState(searchQuery || '');

  const { control, setValue } = useForm<FormData>({
    defaultValues: {
      searchValue: searchQuery || '',
      selectedIntegration: source || '',
      isFilterOpen: true,
    },
  });

  const isFilterOpen = useWatch({ control, name: 'isFilterOpen' });
  const selectedIntegration = useWatch({
    control,
    name: 'selectedIntegration',
  });

  const toggleFilterSidebar = () => {
    setValue('isFilterOpen', !isFilterOpen);
  };

  const syncProductsMutation = useMutation(
    (source: SourceIntegration) => ProductsService.syncProducts(source),
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries(apiRoutes.listProducts());

        toast({
          title: 'Sincronização concluída',
          description: 'Os produtos foram sincronizados com sucesso!',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      },
    },
  );

  useEffect(() => {
    const urlSearchQuery = searchParams.get('searchQuery') || '';
    setSearchInputValue(urlSearchQuery);
    setValue('searchValue', urlSearchQuery);
  }, [searchParams, setValue]);

  useEffect(() => {
    if (searchParams.toString()) {
      const paramsCount = searchParams.toString().split('&').length;
      toast({
        title: 'Filtros aplicados',
        description: `${paramsCount} filtros aplicados`,
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    }
  }, []);

  const {
    data: integrationStatus,
    isLoading: integrationsLoading,
    error: integrationsError,
  } = useQuery(
    apiRoutes.getIntegrationStatusSummary(),
    async () => {
      const { data } = await IntegrationsService.getIntegrationStatusSummary();
      return data;
    },
    {
      onSuccess: (data) => {
        const activeIntegrations = IntegrationUtils.getActiveIntegrations(data);

        if (activeIntegrations.length > 0 && !searchParams.get('source')) {
          const firstActiveIntegration = activeIntegrations[0];
          setValue('selectedIntegration', firstActiveIntegration.key);

          const newParams = new URLSearchParams(searchParams);
          newParams.set('source', firstActiveIntegration.key);
          newParams.set('page', '1');
          setSearchParams(newParams);
        } else if (searchParams.get('source')) {
          setValue('selectedIntegration', searchParams.get('source') || '');
        }
      },
    },
  );

  const activeIntegrations: ProcessedIntegration[] = integrationStatus
    ? IntegrationUtils.getActiveIntegrations(integrationStatus)
    : [];

  const selectedIntegrationName = useMemo(() => {
    return source ? IntegrationUtils.getIntegrationDisplayName(source) : '';
  }, [source]);

  const handleSearchInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchInputValue(e.target.value);
    },
    [],
  );

  const executeSearch = useCallback(() => {
    const newParams = new URLSearchParams(searchParams);

    if (searchInputValue.trim()) {
      newParams.set('searchQuery', searchInputValue.trim());
    } else {
      newParams.delete('searchQuery');
    }

    newParams.set('page', '1');

    setSearchParams(newParams);
  }, [searchInputValue, searchParams, setSearchParams]);

  const handleSearchSubmit = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        executeSearch();
      }
    },
    [executeSearch],
  );

  const handleSyncProducts = () => {
    if (!selectedIntegration) {
      toast({
        title: 'Integração não selecionada',
        description: 'Selecione uma integração para sincronizar os produtos',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    syncProductsMutation.mutate(selectedIntegration as SourceIntegration);
  };

  return (
    <IntegrationStatusScreen
      isLoading={integrationsLoading}
      error={!!integrationsError}
      activeIntegrations={activeIntegrations}
    >
      <LoadingScreen
        isLoading={syncProductsMutation.isLoading}
        message="Sincronizando Produtos"
      >
        <Grid
          templateColumns={isFilterOpen ? '290px 1fr' : 'auto 1fr'}
          templateRows="auto auto 1fr"
          height="100vh"
          width="100%"
        >
          <GridItem
            rowSpan={3}
            colSpan={1}
            boxShadow="lg"
            position="relative"
            width={isFilterOpen ? '290px' : '60px'}
            overflow="hidden"
            bg="white"
            transition="all 0.3s ease"
          >
            <Flex
              align="center"
              justify="space-between"
              p={isFilterOpen ? '5' : '3'}
              pt={8}
              margin="auto"
              borderBottom="1px solid"
              borderColor="gray.200"
              transition="all 0.3s ease"
            >
              {isFilterOpen && (
                <Box>
                  <Heading size="md" whiteSpace="nowrap">
                    Produtos
                  </Heading>
                </Box>
              )}

              <Tooltip
                label={isFilterOpen ? 'Ocultar' : 'Expandir'}
                aria-label="Botão para expandir ou ocultar sidebar"
              >
                <IconButton
                  aria-label={isFilterOpen ? 'Fechar filtros' : 'Abrir filtros'}
                  icon={isFilterOpen ? <FiChevronLeft /> : <FiChevronRight />}
                  size="24"
                  width="36px"
                  height="36px"
                  borderRadius="full"
                  boxShadow="lg"
                  onClick={toggleFilterSidebar}
                  variant="ghost"
                  color="blue.600"
                  _hover={{ bg: 'transparent' }}
                  _active={{ bg: 'transparent' }}
                />
              </Tooltip>
            </Flex>

            <Box transition="all 0.3s ease" px="2">
              <FilterSidebar
                isOpen={isFilterOpen}
                activeIntegrations={activeIntegrations}
              />
            </Box>
          </GridItem>

          <GridItem colSpan={1}>
            <Flex
              padding={4}
              justifyContent="space-between"
              alignItems="center"
            >
              <Flex alignItems="center">
                <InputGroup minWidth="450px" mr={4}>
                  <InputLeftElement pointerEvents="none">
                    <BsSearch color="gray.300" />
                  </InputLeftElement>
                  <Input
                    placeholder="Nome do produto, SKU, código da variante..."
                    value={searchInputValue}
                    onChange={handleSearchInputChange}
                    onKeyPress={handleSearchSubmit}
                    onBlur={executeSearch}
                  />
                </InputGroup>
              </Flex>

              <Flex gap={2}>
                <Button
                  variant="primary"
                  leftIcon={<MdSync size="24px" />}
                  onClick={handleSyncProducts}
                  isDisabled={!selectedIntegration}
                  isLoading={syncProductsMutation.isLoading}
                  loadingText="Sincronizando..."
                >
                  Sincronizar Produtos
                  {selectedIntegrationName && ` - ${selectedIntegrationName}`}
                </Button>
              </Flex>
            </Flex>
          </GridItem>

          <GridItem
            colSpan={1}
            css={scrollbarStyles({ width: '4px' })}
            mr={1}
            overflow="auto"
          >
            <TableProductsPaginated />
          </GridItem>
        </Grid>
      </LoadingScreen>
    </IntegrationStatusScreen>
  );
};

export default ProductsPage;
