import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Text,
  Textarea,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import * as yup from 'yup';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import { DebugService } from '../../../services/debug.service';
import { RootState } from '../../../state/store';
import { useSelector } from 'react-redux';
import InputSelect, { SelectOption } from '../../../components/InputSelect';
import {
  AppModule,
  appModules,
  AppModulesEnum,
} from '../../../types/AppModule';

const schema = yup
  .object({
    appModulePermissionsOptions: yup.array().of(
      yup.object({
        value: yup.string().required(),
        label: yup.string().required(),
      }),
    ),
  })
  .required();

type SchemaType = yup.InferType<typeof schema>;

const appModulePermissionsOptionsToSelectOptions = (
  appModulePermissionsOptions: AppModule[],
): SelectOption[] => {
  return appModulePermissionsOptions.map((module) => ({
    value: module,
    label: module,
  }));
};

const MyAppModulesPermissions = () => {
  const { currentUser } = useSelector((state: RootState) => state.auth);

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
    defaultValues: {
      appModulePermissionsOptions: appModulePermissionsOptionsToSelectOptions(
        currentUser?.appModulePermissions || [],
      ),
    },
  });
  const toast = useToast();

  const {
    mutateAsync: debugSetMyAppModulesPermissionsMutationAsync,
    isLoading: debugSetMyAppModulesPermissionsMutationIsLoading,
  } = useMutation(
    apiRoutes.debugSetMyAppModulesPermissions(),
    DebugService.debugSetMyAppModulesPermissions,
  );

  async function onSubmit(data: SchemaType) {
    const response = await debugSetMyAppModulesPermissionsMutationAsync({
      appModulePermissions: (data.appModulePermissionsOptions?.map(
        (option) => option.value,
      ) || []) as AppModule[],
    });

    if (!!response.data) {
      toast({
        title: 'Sucesso ao alterar permissões.',
        duration: 5000,
        isClosable: true,
        status: 'success',
      });
    } else {
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao alterar permissões.',
        duration: 5000,
        isClosable: true,
        status: 'error',
      });
    }
  }

  return (
    <Box padding="20px">
      <Container>
        <Heading size="md" mb={5}>
          Minhas Permissões de App
        </Heading>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <FormControl>
              <FormLabel>
                Alterando permissões da <i>role</i>
              </FormLabel>
              <Input
                placeholder="Alterando permissões da role"
                value={currentUser?.roleId || ''}
                disabled
              />
            </FormControl>
            <Divider />
            <FormControl>
              <FormLabel>Módulos permitidos</FormLabel>
              <Controller
                name="appModulePermissionsOptions"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    placeholder="Adicionar permissões aos módulos"
                    options={appModules.map((module) => ({
                      value: module,
                      label: module,
                    }))}
                    isMulti
                    value={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
            </FormControl>
            <FormControl>
              <Flex justify="flex-end">
                <Button
                  colorScheme="blue"
                  type="submit"
                  isLoading={debugSetMyAppModulesPermissionsMutationIsLoading}
                >
                  Alterar permissões
                </Button>
              </Flex>
            </FormControl>
          </Stack>
        </form>
      </Container>
    </Box>
  );
};

export default MyAppModulesPermissions;
