import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Text,
  Textarea,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import * as yup from 'yup';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import { CompaniesService } from '../../../services/companies.service';
import { CustomersService } from '../../../services/customers.service';
import { DebugService } from '../../../services/debug.service';
import { UsersService } from '../../../services/users.service';
import { useSelector } from 'react-redux';
import { RootState } from '../../../state/store';
import { User } from '../../../types/Prisma';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../constants/app-paths';

const schema = yup
  .object({
    impersonateUserId: yup.string().required('Escolha um usuário'),
  })
  .required();

type SchemaType = yup.InferType<typeof schema>;

const ImpersonateDebugPage = () => {
  const { currentUser } = useSelector((state: RootState) => state.auth);
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
    watch,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
    defaultValues: {
      impersonateUserId: currentUser?.sub || '',
    },
  });
  const impersonateUserIdWatch = watch('impersonateUserId');

  const toast = useToast();

  const { data: usersQueryData } = useQuery(
    apiRoutes.debugListAvailableUsersForImpersonate(),
    async () => {
      const { data } =
        await DebugService.debugListAvailableUsersForImpersonate();
      return data;
    },
  );

  const {
    mutateAsync: debugImpersonateMutationAsync,
    isLoading: debugImpersonateMutationIsLoading,
  } = useMutation(apiRoutes.debugImpersonate(), DebugService.debugImpersonate);

  async function onSubmit(data: SchemaType) {
    const response = await debugImpersonateMutationAsync({
      userId: data.impersonateUserId,
    });

    if (!!response.data.accessToken) {
      localStorage.setItem('access_token', response.data.accessToken);
      toast({
        title: `Sucesso ao impersonar usuário com id ${data.impersonateUserId}. Recarregue a página se necessário.`,
        duration: 5000,
        isClosable: true,
        status: 'success',
      });
    } else {
      toast({
        title: 'Erro',
        description: `Ocorreu um erro ao impersonar usuário com id ${data.impersonateUserId}.`,
        duration: 5000,
        isClosable: true,
        status: 'error',
      });
    }
  }

  const isSelectedUserCurrentUser = impersonateUserIdWatch === currentUser?.sub;

  return (
    <Box padding="20px">
      <Container>
        <Heading size="md" mb={5}>
          Assumir identidade de usuário (Impersonate)
        </Heading>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <FormControl>
              <FormLabel>Impersonar usuário</FormLabel>
              <Select
                placeholder="Selecione um usuário"
                {...register('impersonateUserId')}
              >
                {usersQueryData?.map((user) => (
                  <option
                    key={user.id}
                    value={user.id}
                    selected={user.id === impersonateUserIdWatch}
                  >
                    {user.name} ({user.email})
                  </option>
                ))}
              </Select>
              <Text color={colors.danger} fontSize="xs">
                {errors.impersonateUserId?.message}
              </Text>
              {isSelectedUserCurrentUser && (
                <Text color={colors.darkGrey} fontSize="xs" mt={1}>
                  Usuário selecionado já é o usuário logado. Impersonar fará com
                  que o payload JWT seja atualizado.
                </Text>
              )}
            </FormControl>
            <FormControl>
              <Flex justify="flex-end">
                <Button
                  colorScheme="blue"
                  type="submit"
                  isLoading={debugImpersonateMutationIsLoading}
                  isDisabled={impersonateUserIdWatch === ''}
                >
                  Impersonar
                </Button>
              </Flex>
            </FormControl>
          </Stack>
        </form>
      </Container>
    </Box>
  );
};

export default ImpersonateDebugPage;
