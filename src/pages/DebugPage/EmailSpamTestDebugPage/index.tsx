import {
  Box,
  Button,
  Checkbox,
  CheckboxGroup,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  RadioGroup,
  Select,
  Stack,
  Text,
  Textarea,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import * as yup from 'yup';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import { CompaniesService } from '../../../services/companies.service';
import { CustomersService } from '../../../services/customers.service';
import { DebugService } from '../../../services/debug.service';
import { UsersService } from '../../../services/users.service';
import { useSelector } from 'react-redux';
import { RootState } from '../../../state/store';
import { EmailTemplate, User } from '../../../types/Prisma';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../constants/app-paths';
import { EmailTemplatesService } from '../../../services/email-templates.service';
import {
  EmailService,
  RecipientSeparator,
  SendSpamTestDto,
} from '../../../services/email.service';

const RECIPIENTS_SEPARATORS = [
  { label: 'Vírgula', value: ',' },
  { label: 'Ponto e vírgula', value: ';' },
  { label: 'Espaço em branco', value: ' ' },
  { label: 'Barra', value: '|' },
];

const schema = yup
  .object({
    separator: yup.string().required('Escolha um separador'),
    recipients: yup.string().required('Cole aqui a lista de emails'),
    emailTemplateId: yup.string().required('Selecione um template de email'),
  })
  .required();

type SchemaType = yup.InferType<typeof schema>;

const EmailSpamTestDebugPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
    defaultValues: {
      separator: ',',
      recipients: '',
      emailTemplateId: '',
    },
  });

  const toast = useToast();

  const { data: emailTemplates } = useQuery(
    apiRoutes.listEmailTemplates(),
    async () => {
      const { data } = await EmailTemplatesService.listEmailTemplates();
      return data;
    },
    {},
  );

  const sendSpamTest = useMutation(
    (sendSpamTestDto: SendSpamTestDto) =>
      EmailService.sendSpamTest(sendSpamTestDto),
    {
      onSuccess: async () => {
        toast({
          title: 'Emails enviados com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        reset();
      },
      onError: (error: any) => {
        toast({
          title: 'Erro ao enviar emails',
          description: error?.response?.data?.message || 'Erro desconhecido',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: SchemaType) {
    const sendSpamTestDto: SendSpamTestDto = {
      emailTemplateId: data.emailTemplateId,
      separator: data.separator as RecipientSeparator,
      recipients: data.recipients,
    };

    await sendSpamTest.mutateAsync(sendSpamTestDto);
  }

  return (
    <Box padding="20px">
      <Container>
        <Heading size="md" mb={5}>
          Enviar emails para teste de spam
        </Heading>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <FormControl>
              <FormLabel>Template de email</FormLabel>
              <Controller
                control={control}
                name="emailTemplateId"
                render={({ field }) => (
                  <Select
                    placeholder="Selecione um template de email"
                    {...field}
                    {...register('emailTemplateId')}
                    isInvalid={!!errors.emailTemplateId?.message}
                  >
                    {emailTemplates?.map((template: EmailTemplate) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </Select>
                )}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.emailTemplateId?.message}
              </Text>
            </FormControl>
            <FormControl>
              <FormLabel>Lista de emails</FormLabel>
              <Textarea
                placeholder="Cole aqui a lista de emails"
                {...register('recipients')}
                isInvalid={!!errors.recipients?.message}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.recipients?.message}
              </Text>
            </FormControl>
            <FormControl>
              <FormLabel>Separador</FormLabel>
              <Controller
                control={control}
                name="separator"
                render={({ field }) => (
                  <Select {...register('separator')}>
                    {RECIPIENTS_SEPARATORS.map(({ label, value }) => (
                      <option key={value} value={value}>
                        {label} ("{value}")
                      </option>
                    ))}
                  </Select>
                )}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.separator?.message}
              </Text>
            </FormControl>
            <FormControl>
              <Flex justify="flex-end">
                <Button
                  colorScheme="blue"
                  type="submit"
                  // isLoading={debugSimulateMessageReceiveMutationIsLoading}
                >
                  Enviar teste
                </Button>
              </Flex>
            </FormControl>
          </Stack>
        </form>
      </Container>
    </Box>
  );
};

export default EmailSpamTestDebugPage;
