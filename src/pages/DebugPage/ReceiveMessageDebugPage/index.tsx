import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Text,
  Textarea,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import * as yup from 'yup';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import { CompaniesService } from '../../../services/companies.service';
import { CustomersService } from '../../../services/customers.service';
import { DebugService } from '../../../services/debug.service';

const CUSTOMERS_TAKE = 10;

const schema = yup
  .object({
    receiveAsCompanyId: yup.string().optional(),
    sendAsCustomerId: yup.string().required('Escolha um cliente'),
    message: yup.string().required('Digite uma mensagem'),
  })
  .required();

type SchemaType = yup.InferType<typeof schema>;

const ReceiveMessageDebugPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<SchemaType>({
    resolver: yupResolver(schema),
  });
  const toast = useToast();

  const { data: companyQueryData, isLoading: isLoadingCompanyQuery } = useQuery(
    apiRoutes.getCompanyDetails(),
    async () => {
      const { data } = await CompaniesService.getCompanyDetails();
      return data;
    },
  );

  const { data: customersQueryData } = useQuery(
    apiRoutes.listCustomers({ perPage: CUSTOMERS_TAKE }),
    async () => {
      const { data: paginatedData } = await CustomersService.listCustomers({
        perPage: CUSTOMERS_TAKE,
      });
      return paginatedData.data;
    },
  );

  const {
    mutateAsync: debugSimulateMessageReceiveMutationAsync,
    isLoading: debugSimulateMessageReceiveMutationIsLoading,
  } = useMutation(
    apiRoutes.debugSimulateMessageReceive(),
    DebugService.debugSimulateMessageReceive,
  );

  useEffect(() => {
    if (isLoadingCompanyQuery || !companyQueryData) return;
    setValue('receiveAsCompanyId', companyQueryData.id);
  }, [isLoadingCompanyQuery, companyQueryData]);

  async function onSubmit(data: SchemaType) {
    const response = await debugSimulateMessageReceiveMutationAsync({
      sendAsCustomerId: data.sendAsCustomerId,
      message: data.message,
    });

    if (!!response.data.id) {
      toast({
        title: 'Recebimento de mensagem simulado com sucesso.',
        duration: 5000,
        isClosable: true,
        status: 'success',
      });
    } else {
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao simular o envio de mensagem.',
        duration: 5000,
        isClosable: true,
        status: 'error',
      });
    }
  }

  return (
    <Box padding="20px">
      <Container>
        <Heading size="md" mb={5}>
          Simulação de recebimento de mensagens
        </Heading>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={5}>
            <FormControl>
              <FormLabel>Recebendo como empresa</FormLabel>
              <Input
                placeholder="Rebecendo como empresa"
                {...register('receiveAsCompanyId')}
                isInvalid={!!errors.receiveAsCompanyId?.message}
                disabled
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.receiveAsCompanyId?.message}
              </Text>
            </FormControl>
            <Divider />
            <FormControl>
              <FormLabel>Enviar como cliente</FormLabel>
              <Select
                placeholder="Selecione um cliente"
                {...register('sendAsCustomerId')}
              >
                {customersQueryData?.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} ({customer.phoneNumberId})
                  </option>
                ))}
              </Select>
              <Text color={colors.danger} fontSize="xs">
                {errors.sendAsCustomerId?.message}
              </Text>
            </FormControl>
            <FormControl>
              <FormLabel>Mensagem</FormLabel>
              <Textarea
                placeholder="Digite aqui sua mensagem"
                {...register('message')}
                isInvalid={!!errors.message?.message}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.message?.message}
              </Text>
            </FormControl>
            <FormControl>
              <Flex justify="flex-end">
                <Button
                  colorScheme="blue"
                  type="submit"
                  isLoading={debugSimulateMessageReceiveMutationIsLoading}
                >
                  Enviar mensagem
                </Button>
              </Flex>
            </FormControl>
          </Stack>
        </form>
      </Container>
    </Box>
  );
};

export default ReceiveMessageDebugPage;
