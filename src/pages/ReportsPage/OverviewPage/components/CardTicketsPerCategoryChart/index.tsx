import { Card, CardBody, CardHeader, Center } from '@chakra-ui/react';
import CustomECharts from '../../../../../components/CustomECharts';
import { colors } from '../../../../../constants/colors';

interface CardTicketsPerCategoryChartProps {
  categories: string[];
  values: number[];
}

const colorEntries = Object.entries(colors.chartColors);

const getColorForCategory = (category: string) => {
  if (category.length === 0) {
    if (!Array.isArray(colorEntries) || colorEntries.length === 0) {
      throw new Error('colorEntries deve ser um array não vazio.');
    }

    const hash = category
      .split('')
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const index = hash % colorEntries.length;
    return colorEntries[index][1].secondary;
  }
};

export default function CardTicketsPerCategoryChart({
  categories,
  values,
}: CardTicketsPerCategoryChartProps) {
  return (
    <Card variant={'outline'}>
      <CardHeader>
        <Center>
          <b>Atendimentos Por Categoria</b>
        </Center>
      </CardHeader>
      <CardBody>
        <CustomECharts
          chartWidth={'100%'}
          chartHeight="500px"
          marginTop={'0px'}
          option={{
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
              },
              formatter: function (params: any) {
                const data = params[0];
                return `
                  <div style="padding: 8px;">
                    <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
                    <div>Atendimentos: ${data.value}</div>
                  </div>
                `;
              },
            },
            xAxis: {
              type: 'category',
              data: categories,
              axisLabel: {
                rotate: 45,
                interval: 0,
                fontSize: 10,
                margin: 15,
                formatter: function (value: string) {
                  if (value.length > 15) {
                    return value.substring(0, 15) + '...';
                  }
                  return value;
                },
              },
              triggerEvent: true,
            },
            yAxis: {
              type: 'value',
            },
            grid: {
              bottom: 100,
              left: 60,
              right: 30,
              top: 30,
            },
            series: [
              {
                data: values.map((value, index) => ({
                  value,
                  itemStyle: {
                    color: getColorForCategory(categories[index]),
                  },
                })),
                type: 'bar',
              },
            ],
          }}
        />
      </CardBody>
    </Card>
  );
}
