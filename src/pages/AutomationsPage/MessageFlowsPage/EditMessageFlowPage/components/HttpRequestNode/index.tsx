import { useTranslation } from 'react-i18next';
import { NodeProps, Handle, Position } from 'reactflow';
import { HttpRequestNodeData } from '../../../../../../types/ReactFlowNode';
import NodeLayout from '../NodeLayout';
import { colors } from '../../../../../../constants/colors';
import { Box, Text, Flex, Badge } from '@chakra-ui/react';
import { TbArrowsTransferDown } from 'react-icons/tb';
import { FaCheck } from 'react-icons/fa';
import { CgClose } from 'react-icons/cg';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

const HttpRequestNode = (props: NodeProps<HttpRequestNodeData>) => {
  const { t } = useTranslation();
  const methodColors: Record<HttpMethod, string> = {
    GET: 'green',
    POST: 'blue',
    PUT: 'orange',
    DELETE: 'red',
    PATCH: 'purple',
  };

  const getMethodColor = (method: string | undefined): string => {
    if (!method) return 'gray';

    const upperMethod = method.toUpperCase() as HttpMethod;
    return methodColors[upperMethod] || 'gray';
  };

  return (
    <NodeLayout
      id={props.id}
      title={t(`enums.FlowNodeType.${props.type}`)}
      icon={<TbArrowsTransferDown />}
      color={colors.hotPink}
      outHandle={false}
    >
      <Box mt="4" width="100%">
        <Box
          borderRadius="lg"
          bgColor="gray.50"
          padding="4"
          boxShadow="sm"
          mb="4"
        >
          <Flex alignItems="center" mb="3">
            <Badge
              colorScheme={getMethodColor(props.data?.method)}
              fontSize="sm"
              px="3"
              py="1"
              borderRadius="full"
            >
              {props.data?.method || 'POST'}
            </Badge>
          </Flex>

          <Box
            p="3"
            bg="white"
            borderRadius="md"
            border="1px"
            borderColor="gray.200"
            overflow="hidden"
          >
            <Text fontSize="xs" color="gray.500" mb="1">
              URL
            </Text>
            <Text
              fontSize="sm"
              fontWeight="medium"
              wordBreak="break-word"
              whiteSpace="nowrap"
              overflow="hidden"
              textOverflow="ellipsis"
            >
              {props.data?.url ? `${props.data.url}` : ''}
            </Text>
          </Box>
        </Box>

        <Box
          borderRadius="lg"
          bgColor="green.50"
          padding="4"
          boxShadow="sm"
          border="1px"
          borderColor="green.200"
          position="relative"
          mb="4"
        >
          <Flex alignItems="center" justifyContent="start">
            <FaCheck color="green" />
            <Text fontSize="sm" color="green.600" fontWeight="medium" ml={2}>
              Sucesso na Requisição
            </Text>
          </Flex>
        </Box>
        <Handle
          id={props.id}
          type="source"
          position={Position.Right}
          style={{
            top: '68%',
            transform: 'translateY(-50%)',
            background: 'green',
            height: 10,
            width: 10,
          }}
        />

        <Box
          borderRadius="lg"
          bgColor="red.50"
          padding="4"
          boxShadow="sm"
          border="1px"
          borderColor="red.200"
          position="relative"
        >
          <Flex alignItems="center" justifyContent="start">
            <CgClose color="red" size="18px" />
            <Text fontSize="sm" color="red.600" fontWeight="medium" ml={2}>
              Erro na Requisição
            </Text>
          </Flex>
        </Box>
        <Handle
          id={`error-${props.id}`}
          type="source"
          position={Position.Right}
          style={{
            top: '87%',
            transform: 'translateY(-50%)',
            background: 'red',
            height: 10,
            width: 10,
          }}
        />
      </Box>
    </NodeLayout>
  );
};

export default HttpRequestNode;
