import { Box } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { NodeProps } from 'reactflow';
import { apiRoutes } from '../../../../../../constants/api-routes';
import { SendEmailTemplateNodeData } from '../../../../../../types/ReactFlowNode';
import NodeLayout from '../NodeLayout';
import { EmailTemplatesService } from '../../../../../../services/email-templates.service';
import { colors } from '../../../../../../constants/colors';
import { MdEmail } from 'react-icons/md';

const SendEmailTemplateNode = ({
  id,
  data,
  type,
}: NodeProps<SendEmailTemplateNodeData>) => {
  const { t } = useTranslation();

  const { data: emailTemplates } = useQuery(
    apiRoutes.listEmailTemplates(),
    async () => {
      const { data } = await EmailTemplatesService.listEmailTemplates();
      return data;
    },
    {
      staleTime: 1000 * 15,
    },
  );
  const emailTemplate = emailTemplates?.find(
    (email) => email.id === data.emailTemplateId,
  );

  return (
    <NodeLayout
      id={id}
      title={t(`enums.FlowNodeType.${type}`)}
      icon={<MdEmail />}
      color={colors.tulip}
    >
      <Box
        mt="20px"
        width="100%"
        borderRadius={'10px'}
        bgColor={'#F3F3F3'}
        padding="15px"
        height="fit-content"
      >
        {emailTemplate?.name || ''}
      </Box>
    </NodeLayout>
  );
};

export default SendEmailTemplateNode;
