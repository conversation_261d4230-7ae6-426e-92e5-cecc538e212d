import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../../constants/api-routes';
import NodeLayout from '../NodeLayout';
import { Box, Flex, Text } from '@chakra-ui/react';
import { FaTag } from 'react-icons/fa';
import { UsersService } from '../../../../../../services/users.service';
import { NodeProps } from 'reactflow';
import { AssignConversationTicketToAgentNodeData } from '../../../../../../types/ReactFlowNode';
import { HiOutlineUserAdd } from 'react-icons/hi';
import { AiOutlineInfoCircle } from 'react-icons/ai';

const AssignConversationTicketToAgentNode = (
  props: NodeProps<AssignConversationTicketToAgentNodeData>,
) => {
  const { t } = useTranslation();

  const { data: companyAgents } = useQuery(
    apiRoutes.listCompanyAgents(),
    async () => {
      const { data } = await UsersService.listCompanyAgents();
      return data;
    },
    {
      staleTime: 1000 * 15,
    },
  );
  const targetAgents = companyAgents?.filter((agent) =>
    props.data.agentIds.includes(agent.id),
  );

  return (
    <NodeLayout
      id={props.id}
      title={t(`enums.FlowNodeType.${props.type}`)}
      icon={<HiOutlineUserAdd />}
      color="#ffaa00"
    >
      <Box
        mt="20px"
        width="100%"
        borderRadius={'10px'}
        bgColor={'#F3F3F3'}
        padding="15px"
        height="fit-content"
      >
        {targetAgents?.map((agent) => agent.name).join(', ')}
      </Box>
    </NodeLayout>
  );
};

export default AssignConversationTicketToAgentNode;
