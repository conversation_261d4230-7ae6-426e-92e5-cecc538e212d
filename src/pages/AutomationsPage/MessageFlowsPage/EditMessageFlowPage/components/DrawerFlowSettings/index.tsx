import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>erHeader,
  DrawerOverlay,
  Flex,
  FormControl,
  FormLabel,
  Stack,
  Switch,
  Text,
} from '@chakra-ui/react';
import { Flow } from '../../../../../../types/Flow';
import { Controller, useForm } from 'react-hook-form';
import { useEffect, useId } from 'react';

export type FlowSettingsData = Pick<Flow, 'repeatOnInvalidInput'>;

interface DrawerFlowSettingsProps {
  flow: Flow;
  isOpen: boolean;
  onClickClose: () => void;
  onClickSave: (data: FlowSettingsData) => void;
}

const DrawerFlowSettings = ({
  flow,
  isOpen,
  onClickClose,
  onClickSave,
}: DrawerFlowSettingsProps) => {
  const formId = useId();
  const { handleSubmit, reset, control } = useForm({
    defaultValues: {
      repeatOnInvalidInput: flow.repeatOnInvalidInput,
    },
  });

  useEffect(() => {
    reset({
      repeatOnInvalidInput: flow.repeatOnInvalidInput,
    });
  }, [isOpen]);

  return (
    <Drawer size="sm" isOpen={isOpen} placement="right" onClose={onClickClose}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader>
          Configurações do fluxo
          <Text fontSize="sm" fontWeight="normal">
            Configure as preferências de comportamento do fluxo
          </Text>
        </DrawerHeader>
        <DrawerBody>
          <form id={formId} onSubmit={handleSubmit(onClickSave)}>
            <Box
              display="flex"
              flexDirection="column"
              minHeight="100vh"
              justifyContent="space-between"
            >
              <Stack spacing={5} flex="1" overflowY="auto">
                <FormControl>
                  <Controller
                    name="repeatOnInvalidInput"
                    control={control}
                    render={({ field }) => (
                      <Flex gap={8} alignItems="center">
                        <Flex flexDirection="column" gap={1}>
                          <FormLabel margin={0}>
                            Impedir que usuário saia do fluxo
                          </FormLabel>
                          <Text fontSize="sm" fontWeight="normal">
                            Quando ativado, uma resposta inválida fará com que o
                            ultimo nó seja repetido. Caso ao contrario, o fluxo
                            será encerrado.
                          </Text>
                        </Flex>
                        <Switch
                          isChecked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      </Flex>
                    )}
                  />
                </FormControl>
              </Stack>
            </Box>
          </form>
        </DrawerBody>
        <DrawerFooter>
          <Box
            padding="20px"
            bg="white"
            position="sticky"
            bottom="0"
            width="100%"
          >
            <Flex justify="space-between">
              <Button variant="outline" onClick={onClickClose} minWidth="120px">
                Cancelar
              </Button>
              <Button
                bg="black"
                color="white"
                form={formId}
                type="submit"
                minWidth="120px"
              >
                Salvar
              </Button>
            </Flex>
          </Box>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default DrawerFlowSettings;
