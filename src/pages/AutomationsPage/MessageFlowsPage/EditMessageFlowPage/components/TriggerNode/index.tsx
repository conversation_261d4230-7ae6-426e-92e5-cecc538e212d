import { Box, Flex, Text, Tooltip } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { IoRocketOutline } from 'react-icons/io5';
import { Handle, NodeProps, Position } from 'reactflow';
import { TriggerNodeData } from '../../../../../../types/ReactFlowNode';
import NodeLayout from '../NodeLayout';
import { FaShoppingCart } from 'react-icons/fa';
import { CgTemplate } from 'react-icons/cg';
import { AiOutlineMessage } from 'react-icons/ai';
import { FlowTrigger } from '../../../../../../types/FlowTrigger';
import { MessageTemplate } from '../../../../../../types/MessageTemplate';
import { TextUtils } from '../../../../../../utils/text';

const TriggerNode = (props: NodeProps<TriggerNodeData>) => {
  const { t } = useTranslation();
  
  return (
    <NodeLayout
      id={props.id}
      title="Triggers"
      icon={<IoRocketOutline />}
      color="#666666"
      targetHandle={false}
      width="350px"
    >
      <Flex flexDir="column" gap={'5px'} mt="20px">
        {props.data.flowTriggers?.map((flowTrigger, index) => {
          const templateName = flowTrigger.type === 'quick_reply_message_template'
            ? (
                flowTrigger as FlowTrigger & {
                  messageTemplateButton?: {
                    messageTemplate?: MessageTemplate;
                  };
                }
              ).messageTemplateButton?.messageTemplate?.name || ''
            : t(`enums.FlowTriggerType.${flowTrigger.type}`);
          
          const displayTemplateName = TextUtils.truncateText(templateName, 15);
          const displayTriggerText = TextUtils.truncateText(flowTrigger.text || '', 20);
          const fullText = `${templateName}${flowTrigger.text ? ' ' + flowTrigger.text : ''}`;
          
          return (
            <Box key={flowTrigger.id}>
              <Handle
                id={flowTrigger.id}
                type="source"
                position={Position.Right}
                style={{
                  top: 'auto',
                  bottom: 30 + (props.data.flowTriggers!.length - index - 1) * 45,
                  height: 10,
                  width: 10,
                }}
              />
              <Tooltip label={fullText} placement="top" hasArrow>
                <Flex
                  bgColor={flowTrigger.isDefault ? '#EAF4FF' : '#F3F3F3'}
                  width="100%"
                  borderRadius="5px"
                  height="40px"
                  justifyContent="center"
                  alignItems="center"
                  flexDirection="row"
                  px={2}
                  overflow="hidden"
                >
                  {flowTrigger.type === 'abandoned_cart' ? (
                    <FaShoppingCart size={16} style={{ marginRight: 8, flexShrink: 0 }} />
                  ) : flowTrigger.type === 'quick_reply_message_template' ? (
                    <CgTemplate size={16} style={{ marginRight: 8, flexShrink: 0 }} />
                  ) : (
                    <AiOutlineMessage size={16} style={{ marginRight: 8, flexShrink: 0 }} />
                  )}
                  
                  <Flex 
                    flexDirection="row" 
                    fontWeight="bold"
                    overflow="hidden"
                    whiteSpace="nowrap"
                    textOverflow="ellipsis"
                    maxWidth="calc(100% - 24px)"
                  >
                    {flowTrigger.type === 'abandoned_cart'
                      ? t('enums.FlowTriggerType.abandoned_cart')
                      : displayTemplateName}
                    {displayTriggerText && (
                      <Text 
                        fontWeight="normal" 
                        ml={2}
                        overflow="hidden"
                        whiteSpace="nowrap"
                        textOverflow="ellipsis"
                      >
                        {displayTriggerText}
                      </Text>
                    )}
                  </Flex>
                </Flex>
              </Tooltip>
            </Box>
          );
        })}
      </Flex>
    </NodeLayout>
  );
};

export default TriggerNode;