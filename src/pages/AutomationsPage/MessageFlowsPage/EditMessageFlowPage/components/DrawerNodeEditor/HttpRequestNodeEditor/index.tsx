import {
  DrawerBody,
  Flex,
  FormControl,
  FormLabel,
  Text,
  Input,
  useToast,
  Textarea,
  Button,
  Box,
  Badge,
  InputGroup,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tooltip,
  Code,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
} from '@chakra-ui/react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { AiOutlineInfoCircle, AiOutlineApi } from 'react-icons/ai';
import { useState } from 'react';
import { scrollbarStyles } from '../../../../../../../styles/scrollbar.styles';
import { KeyValueEditor, KeyValuePair } from './components/KeyValueEditor';
import HttpRequestNodeEditorJsonMappingPanel from './components/KeyValueEditor/HttpRequestNodeEditorJsonMappingPanel';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../../../constants/api-routes';
import { CompanyDefinedFieldTableEnum } from '../../../../../../../types/CompanyDefinedField';
import { CompaniesService } from '../../../../../../../services/companies.service';
import { CompanyDefinedField } from '../../../../../../../types/Prisma';
import HttpRequestNodeEditorMappingCustomerCustomFieldsPanel from './components/KeyValueEditor/HttpRequestNodeEditorMappingCustomerCustomFieldsPanel';
import { MappingFields } from '../../../../../../../utils/mapping.utils';

export const BASE_URL = 'http://';

const DEFAULT_BODY = `{
  "chave": "valor",
  "userName": "[nome da variavel]",
  "objeto": {
    "subchave": "valor",
    "userEmail": "[email]",
  }
}`;

const TEST_BODY = `{
  "chave": "valor de teste",
  "userName": "João Silva",
  "objeto": {
    "subchave": "valor de teste",
    "userEmail": "<EMAIL>"
  }
}`;

const schema = yup.object({
  url: yup
    .string()
    .required('URL é obrigatória')
    .matches(
      /^(https?:\/\/)[a-zA-Z0-9-._~:/?#\[\]@!$&'()*+,;=]+$/,
      'URL deve começar com http:// ou https:// e conter apenas caracteres válidos',
    ),
  method: yup
    .string()
    .oneOf<HttpMethod>(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
    .required('Método é obrigatório'),
  headers: yup.array().of(
    yup.object({
      key: yup.string().required('Nome do header é obrigatório'),
      value: yup.string().required('Valor do header é obrigatório'),
    }),
  ),
  body: yup.string().required('Body é obrigatório'),
  dynamicVariables: yup.array().of(
    yup.object({
      key: yup.string().required('Nome da variável é obrigatório'),
      value: yup.string().required('Valor da variável é obrigatório'),
    }),
  ),
  jsonMappings: yup.array().of(
    yup.object().shape({
      source: yup.string().required('Origem do dado é obrigatória'),
      destination: yup.string().required('Destino do dado é obrigatório'),
    }),
  ),
  customerCustomFieldMappings: yup.array().of(
    yup.object().shape({
      source: yup.string().required('Origem do dado é obrigatória'),
      destination: yup
        .string()
        .required('Campo customizado do cliente é obrigatório'),
    }),
  ),
});

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface HttpRequestNodeEditorFormData {
  url: string;
  method: HttpMethod;
  headers: KeyValuePair[];
  body: string;
  dynamicVariables?: KeyValuePair[];
  jsonMappings: MappingFields[];
  customerCustomFieldMappings: MappingFields[];
}

interface TestResponse {
  status: number;
  data: any;
  headers: Record<string, string>;
  duration: number;
}

export interface HttpRequestNodeEditorProps {
  data: HttpRequestNodeEditorFormData;
  formId: string;
  onSaveNode: (data: any) => void;
}

const HttpRequestNodeEditor = ({
  data,
  formId,
  onSaveNode,
}: HttpRequestNodeEditorProps) => {
  const toast = useToast();
  const [headers, setHeaders] = useState<KeyValuePair[]>(
    data.headers || [{ key: 'Authorization', value: '' }],
  );
  const [dynamicVariables, setDynamicVariables] = useState<KeyValuePair[]>(
    data.dynamicVariables || [],
  );

  const [companyDefinedFieldOptions, setCompanyDefinedFieldOptions] = useState<
    {
      value: string;
      label: string;
    }[]
  >([]);
  const [testBody, setTestBody] = useState(TEST_BODY);
  const [isLoading, setIsLoading] = useState(false);
  const [testResponse, setTestResponse] = useState<TestResponse | null>(null);
  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      url: data.url || '',
      body: data.body || '',
      method: 'POST',
      jsonMappings: data.jsonMappings || [],
      customerCustomFieldMappings: data.customerCustomFieldMappings || [],
    },
  });

  const {
    fields: customerCustomFieldMappings,
    append: appendCustomerCustomFieldMappingElement,
    remove: removeCustomerCustomFieldMappingElement,
    replace: replaceCustomerCustomFieldMappings,
  } = useFieldArray({
    control,
    name: 'customerCustomFieldMappings',
  });

  useQuery(
    apiRoutes.listCompanyDefinedFields(CompanyDefinedFieldTableEnum.CUSTOMERS),
    async () => {
      const { data } = await CompaniesService.listCompanyDefinedFields(
        CompanyDefinedFieldTableEnum.CUSTOMERS,
      );
      return data;
    },
    {
      onSuccess: (data) => {
        const companyDefinedFieldOptions = data.map(
          (companyDefinedField: CompanyDefinedField) => ({
            value: companyDefinedField.id,
            label: companyDefinedField.name,
          }),
        );
        setCompanyDefinedFieldOptions(companyDefinedFieldOptions);
      },
    },
  );
  const url = watch('url');

  const handleTest = async () => {
    setIsLoading(true);
    setTestResponse(null);

    try {
      const headersObj = headers.reduce(
        (acc: { [key: string]: string }, { key, value }) => {
          acc[key] = value;
          return acc;
        },
        {},
      );

      const startTime = performance.now();

      const response = await fetch(url, {
        method: 'POST',
        headers: headersObj,
        body: testBody,
      });

      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      let responseData = {};
      const responseText = await response.text();

      if (responseText.trim()) {
        try {
          responseData = JSON.parse(responseText);
        } catch {
          responseData = {};
        }
      }
      setTestResponse({
        status: response.status,
        data: responseData,
        headers: Object.fromEntries(response.headers.entries()),
        duration,
      });

      toast({
        title: 'Teste realizado com sucesso!',
        status: 'success',
        duration: 3000,
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast({
          title: 'Erro ao testar endpoint',
          description: error.message || 'Erro desconhecido',
          status: 'error',
          duration: 5000,
        });
      } else {
        toast({
          title: 'Erro ao testar endpoint',
          description: 'Erro desconhecido',
          status: 'error',
          duration: 5000,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSubmit = (formData: any) => {
    try {
      JSON.parse(formData.body);

      const newData: HttpRequestNodeEditorFormData = {
        url: formData.url,
        headers,
        body: formData.body,
        method: 'POST',
        dynamicVariables,
        jsonMappings: formData.jsonMappings,
        customerCustomFieldMappings: formData.customerCustomFieldMappings,
      };

      onSaveNode(newData);
    } catch (error) {
      toast({
        title: 'Erro ao salvar',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <DrawerBody css={scrollbarStyles({ width: '4px' })}>
      <Tabs>
        <TabList>
          <Tab>1. Configuração</Tab>
          <Tab>2. Teste</Tab>
          <Tab>3. Atualizar Cliente</Tab>
        </TabList>

        <TabPanels>
          <TabPanel>
            <form onSubmit={handleSubmit(handleFormSubmit)} id={formId}>
              <Flex direction="column" gap={4}>
                <FormControl isInvalid={!!errors.url}>
                  <FormLabel>Endpoint</FormLabel>
                  <InputGroup>
                    <Controller
                      name="url"
                      control={control}
                      render={({ field }) => (
                        <Input
                          placeholder="https://exemplo.com/api/v1/recurso"
                          {...field}
                        />
                      )}
                    />
                  </InputGroup>
                  {errors.url && (
                    <Text color="red" fontSize="xs">
                      {errors.url.message}
                    </Text>
                  )}
                  <Text fontSize="xs" color="gray.600" mt={1}>
                    Exemplo: https://exemplo.com/api/v1/recurso
                  </Text>
                </FormControl>

                <Box p={3} bg="blue.50" borderRadius="md">
                  <Flex align="center" gap={2}>
                    <Tooltip label="Durante a fase de testes, apenas requisições POST estão disponíveis">
                      <Badge colorScheme="blue">POST</Badge>
                    </Tooltip>
                  </Flex>
                </Box>

                <FormControl>
                  <FormLabel>Headers</FormLabel>
                  <KeyValueEditor
                    items={headers}
                    onChange={setHeaders}
                    keyPlaceholder="Nome do header"
                    valueComponent="input"
                    addButtonText="Adicionar Header"
                    customerCustomVariables={companyDefinedFieldOptions}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Variáveis do Usuário Disponíveis</FormLabel>
                  <KeyValueEditor
                    items={dynamicVariables}
                    onChange={setDynamicVariables}
                    keyPlaceholder="Nome da variável"
                    valueComponent="select"
                    addButtonText="Adicionar Variável"
                    customerCustomVariables={companyDefinedFieldOptions}
                  />
                </FormControl>

                <FormControl isInvalid={!!errors.body}>
                  <FormLabel>Body (JSON)</FormLabel>
                  <Controller
                    name="body"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        fontFamily="monospace"
                        height="300px"
                        placeholder={DEFAULT_BODY}
                        resize="none"
                        {...field}
                      />
                    )}
                  />
                  {errors.body && (
                    <Text color="red" fontSize="xs">
                      {errors.body.message}
                    </Text>
                  )}
                </FormControl>

                <Flex mb={4} alignItems="start">
                  <AiOutlineInfoCircle
                    size={'32px'}
                    style={{ marginTop: '4px' }}
                  />
                  <Flex direction="column">
                    <Text ml={2}>
                      Configure os parâmetros da requisição POST. Você pode usar
                      as variáveis do usuário listadas acima no body.
                    </Text>
                    <Text mt={4}>
                      Adicione o nome da variável e seu valor no body, usando o
                      formato {'[nome da variável]'}.
                    </Text>
                  </Flex>
                </Flex>
                <Alert
                  status="info"
                  variant="left-accent"
                  mb={4}
                  flexDirection="column"
                  alignItems="flex-start"
                >
                  <AlertTitle mb={1} display="flex" alignItems="center">
                    <AlertIcon />
                    Informações Importantes
                  </AlertTitle>
                  <AlertDescription>
                    <Text>
                      Se seu endpoint possui whitelist de IPs, entre em contato
                      com nosso suporte para obter maiores informações.
                    </Text>
                  </AlertDescription>
                </Alert>
              </Flex>
            </form>
          </TabPanel>

          <TabPanel>
            <Flex direction="column" gap={4}>
              <Alert status="info" variant="left-accent">
                <AlertIcon />
                <Box>
                  <AlertTitle>Ambiente de Testes</AlertTitle>
                  <AlertDescription>
                    Use esta área para testar sua configuração antes de salvar.
                    Os dados inseridos aqui são apenas para teste e não serão
                    salvos.
                  </AlertDescription>
                </Box>
              </Alert>

              <Box p={4} bg="gray.50" borderRadius="md" maxW="100%">
                <Text fontWeight="bold" mb={2}>
                  Endpoint de Teste:
                </Text>
                <Code
                  p={2}
                  borderRadius="md"
                  display="flex"
                  alignItems="center"
                >
                  <Badge colorScheme="blue">POST</Badge>
                  <Text ml={2}>{url}</Text>
                </Code>
              </Box>

              <Box p={4} bg="gray.50" borderRadius="md" boxShadow="md">
                <Text fontWeight="bold" mb={4} fontSize="lg" color="gray.700">
                  Headers da Requisição:
                </Text>
                <Box bg="white" p={4} borderRadius="md" boxShadow="sm">
                  {headers.map((header, index) => (
                    <Box
                      key={index}
                      mb={4}
                      display="flex"
                      flexDirection="column"
                    >
                      <Box mb={2} display="flex" justifyContent="space-between">
                        <Text
                          fontSize="sm"
                          fontFamily="monospace"
                          color="teal.600"
                          fontWeight="bold"
                          width="100%"
                          maxW="50%"
                          minW="50%"
                        >
                          {header.key}:
                        </Text>
                        <Text
                          fontSize="sm"
                          fontFamily="monospace"
                          color="gray.500"
                          maxW="90%"
                          minW="50%"
                          ml={2}
                        >
                          {header.value}
                        </Text>
                      </Box>
                      <Box borderBottom="1px" borderColor="gray.200" />
                    </Box>
                  ))}
                </Box>
              </Box>

              <FormControl>
                <FormLabel>Body de Teste (JSON)</FormLabel>
                <Textarea
                  fontFamily="monospace"
                  height="200px"
                  value={testBody}
                  onChange={(e) => setTestBody(e.target.value)}
                  resize="none"
                />
              </FormControl>

              <Button
                leftIcon={<AiOutlineApi />}
                colorScheme="blue"
                onClick={handleTest}
                isLoading={isLoading}
                loadingText="Testando..."
              >
                Testar Requisição
              </Button>

              {testResponse && (
                <Box mt={4} p={6} bg="gray.50" borderRadius="lg" boxShadow="sm">
                  <Flex justify="space-between" align="center" mb={6}>
                    <Badge
                      colorScheme={testResponse.status < 400 ? 'green' : 'red'}
                      fontSize="md"
                      px={4}
                      py={2}
                      borderRadius="md"
                    >
                      Status: {testResponse.status}
                    </Badge>
                    <Text fontSize="sm" color="gray.600">
                      Duração: {testResponse.duration}ms
                    </Text>
                  </Flex>

                  <Box mb={6}>
                    <Text
                      fontWeight="bold"
                      fontSize="lg"
                      mb={2}
                      color="gray.700"
                    >
                      Headers da Resposta:
                    </Text>
                    <Box bg="white" p={4} borderRadius="md" boxShadow="sm">
                      {Object.entries(testResponse.headers).map(
                        ([key, value]) => (
                          <Flex key={key} justify="space-between" mb={2}>
                            <Text
                              fontSize="sm"
                              fontFamily="monospace"
                              color="teal.600"
                            >
                              {key}:
                            </Text>
                            <Text
                              fontSize="sm"
                              fontFamily="monospace"
                              color="gray.500"
                            >
                              {value}
                            </Text>
                          </Flex>
                        ),
                      )}
                    </Box>
                  </Box>

                  <Box>
                    <Text
                      fontWeight="bold"
                      fontSize="lg"
                      mb={2}
                      color="gray.700"
                    >
                      Resposta:
                    </Text>
                    <Box
                      bg="white"
                      p={4}
                      borderRadius="md"
                      boxShadow="sm"
                      maxHeight="300px"
                      overflowY="auto"
                    >
                      <pre
                        style={{
                          margin: 0,
                          fontFamily: 'monospace',
                          whiteSpace: 'pre-wrap',
                        }}
                      >
                        {JSON.stringify(testResponse.data, null, 2)}
                      </pre>
                    </Box>
                  </Box>
                </Box>
              )}
            </Flex>
          </TabPanel>
          <HttpRequestNodeEditorMappingCustomerCustomFieldsPanel
            mappingFields={customerCustomFieldMappings}
            control={control}
            appendMappingElement={appendCustomerCustomFieldMappingElement}
            removeMappingElement={removeCustomerCustomFieldMappingElement}
            replaceMappings={replaceCustomerCustomFieldMappings}
            testData={testResponse?.data}
            companyDefinedFieldOptions={companyDefinedFieldOptions}
            errors={errors}
          />
        </TabPanels>
      </Tabs>
    </DrawerBody>
  );
};

export default HttpRequestNodeEditor;
