import { Flex, Input, Select, <PERSON><PERSON><PERSON><PERSON>on, Button } from '@chakra-ui/react';
import { AiOutlineDelete, AiOutlinePlusCircle } from 'react-icons/ai';
import { UserVariablesEnum } from '../../../../../../../../../types/UserVariablesEnum';
import { useQuery } from 'react-query';

export interface KeyValuePair {
  key: string;
  value: UserVariablesEnum | string;
}

interface KeyValueEditorProps {
  items: KeyValuePair[];
  onChange: (items: KeyValuePair[]) => void;
  keyPlaceholder: string;
  valueComponent: 'input' | 'select';
  addButtonText: string;
  customerCustomVariables: { label: string; value: string }[];
}

const USER_VARIABLES = [
  { label: 'Nome do Usuário', value: UserVariablesEnum.CUSTOMER_NAME },
  { label: 'Email do Usuário', value: UserVariablesEnum.CUSTOMER_EMAIL },
  { label: 'ID do Usuário', value: UserVariablesEnum.CUSTOMER_ID },
];

export const KeyValueEditor = ({
  items,
  onChange,
  keyPlaceholder,
  valueComponent,
  addButtonText,
  customerCustomVariables,
}: KeyValueEditorProps) => {
  const { data: variableOptions } = useQuery(
    ['customerCustomVariables', customerCustomVariables],
    () => {
      const customVariables = customerCustomVariables.map((variable) => ({
        label: `Campo Customizado de Cliente: ${variable.label}`,
        value: variable.value,
      }));

      return [...USER_VARIABLES, ...customVariables];
    },
    {},
  );

  const handleAdd = () => {
    onChange([...items, { key: '', value: '' }]);
  };

  const handleRemove = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    onChange(newItems);
  };

  const handleChange = (
    index: number,
    field: 'key' | 'value',
    value: string,
  ) => {
    const newItems = [...items];
    if (field === 'value') {
      newItems[index][field] = value as UserVariablesEnum;
    } else {
      newItems[index][field] = value;
    }
    onChange(newItems);
  };

  return (
    <Flex direction="column" gap={2}>
      {items.map((item, index) => (
        <Flex key={index} gap={2}>
          <Input
            placeholder={keyPlaceholder}
            value={item.key}
            onChange={(e) => handleChange(index, 'key', e.target.value)}
          />
          {valueComponent === 'input' ? (
            <Input
              placeholder="Valor"
              value={item.value}
              onChange={(e) => handleChange(index, 'value', e.target.value)}
            />
          ) : (
            <Select
              value={item.value}
              onChange={(e) => handleChange(index, 'value', e.target.value)}
            >
              <option value="">Selecione uma variável</option>
              {variableOptions?.map((variable) => (
                <option key={variable.value} value={variable.value}>
                  {variable.label}
                </option>
              ))}
            </Select>
          )}
          <IconButton
            aria-label="Remover item"
            icon={<AiOutlineDelete />}
            onClick={() => handleRemove(index)}
            size="sm"
          />
        </Flex>
      ))}
      <Button
        leftIcon={<AiOutlinePlusCircle />}
        onClick={handleAdd}
        size="sm"
        variant="ghost"
      >
        {addButtonText}
      </Button>
    </Flex>
  );
};
