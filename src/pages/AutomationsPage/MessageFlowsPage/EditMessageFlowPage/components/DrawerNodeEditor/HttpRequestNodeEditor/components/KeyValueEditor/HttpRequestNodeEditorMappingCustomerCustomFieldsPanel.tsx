import React from 'react';
import {
  Box,
  Button,
  Input,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Select,
  Text,
} from '@chakra-ui/react';
import {
  Controller,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFieldArrayReplace,
  FieldArrayWithId,
  FieldError,
} from 'react-hook-form';
import * as yup from 'yup';
import ButtonIcon from '../../../../../../../../../components/ButtonIcon';
import { FaTrash } from 'react-icons/fa';
import { colors } from '../../../../../../../../../constants/colors';
import { HttpRequestNodeEditorFormData } from '../..';
import MappingUtils, {
  MappingFields,
} from '../../../../../../../../../utils/mapping.utils';

interface HttpRequestNodeEditorMappingCustomerCustomFieldsPanelProps {
  mappingFields: FieldArrayWithId<
    {
      url: string;
      body: string;
      method: string;
      jsonMappings: MappingFields[];
      customerCustomFieldMappings: MappingFields[];
    },
    'customerCustomFieldMappings',
    'id'
  >[];
  appendMappingElement: UseFieldArrayAppend<MappingFields>;
  removeMappingElement: UseFieldArrayRemove;
  replaceMappings: UseFieldArrayReplace<
    HttpRequestNodeEditorFormData,
    'customerCustomFieldMappings'
  >;
  control: any;
  testData: any;
  companyDefinedFieldOptions: {
    value: string;
    label: string;
  }[];
  errors: {
    url?: FieldError | undefined;
    body?: FieldError | undefined;
    method?: FieldError | undefined;
    jsonMappings?:
      | {
          source?: FieldError | undefined;
          destination?: FieldError | undefined;
        }[]
      | undefined;
    customerCustomFieldMappings?:
      | {
          source?: FieldError | undefined;
          destination?: FieldError | undefined;
        }[]
      | undefined;
  };
}

const HttpRequestNodeEditorMappingCustomerCustomFieldsPanel = ({
  testData,
  mappingFields,
  appendMappingElement,
  removeMappingElement,
  replaceMappings,
  control,
  companyDefinedFieldOptions,
  errors,
}: HttpRequestNodeEditorMappingCustomerCustomFieldsPanelProps) => {
  const handleExtractFromTestResult = () => {
    const customerCustomFieldMappings = MappingUtils.mapJsonStructure({
      input: testData,
      withEmptyDestination: true,
    });
    replaceMappings(customerCustomFieldMappings);
  };

  return (
    <TabPanel>
      <Button ml={4} onClick={() => handleExtractFromTestResult()}>
        Extrair Automaticamente
      </Button>
      <form>
        <Table variant="simple" mt={4}>
          <Thead>
            <Tr>
              <Th>Origem</Th>
              <Th>Destino</Th>
              <Th></Th>
            </Tr>
          </Thead>
          <Tbody>
            {mappingFields.map((item, index) => (
              <Tr key={item.id}>
                <Td width="45%">
                  <Controller
                    name={`customerCustomFieldMappings.${index}.source`}
                    control={control}
                    render={({ field }) => (
                      <>
                        <Input
                          {...field}
                          isInvalid={
                            !!errors?.customerCustomFieldMappings?.[index]
                              ?.source
                          }
                        />
                        <Text color={colors.danger} fontSize="xs">
                          {
                            errors?.customerCustomFieldMappings?.[index]?.source
                              ?.message
                          }
                        </Text>
                      </>
                    )}
                  />
                </Td>
                <Td width="55%">
                  <Controller
                    name={`customerCustomFieldMappings.${index}.destination`}
                    control={control}
                    render={({ field }) => (
                      <>
                        <Select
                          {...field}
                          placeholder="Selecione uma opção"
                          isInvalid={
                            !!errors?.customerCustomFieldMappings?.[index]
                              ?.destination
                          }
                        >
                          {companyDefinedFieldOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </Select>
                        <Text color={colors.danger} fontSize="xs">
                          {
                            errors?.customerCustomFieldMappings?.[index]
                              ?.destination?.message
                          }
                        </Text>
                      </>
                    )}
                  />
                </Td>
                <Td>
                  <ButtonIcon
                    icon={<FaTrash fontSize="20px" color={colors.red} />}
                    onClick={() => removeMappingElement(index)}
                  />
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
        <Button
          colorScheme="blue"
          onClick={() => appendMappingElement({ source: '', destination: '' })}
        >
          Adicionar Novo
        </Button>
      </form>
    </TabPanel>
  );
};

export default HttpRequestNodeEditorMappingCustomerCustomFieldsPanel;
