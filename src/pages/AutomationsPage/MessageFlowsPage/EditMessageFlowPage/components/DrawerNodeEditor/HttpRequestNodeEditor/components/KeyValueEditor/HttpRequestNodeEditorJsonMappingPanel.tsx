import React from 'react';
import {
  Box,
  Button,
  Input,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
} from '@chakra-ui/react';
import {
  Controller,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFieldArrayReplace,
  FieldArrayWithId,
} from 'react-hook-form';
import * as yup from 'yup';
import ButtonIcon from '../../../../../../../../../components/ButtonIcon';
import { FaTrash } from 'react-icons/fa';
import { colors } from '../../../../../../../../../constants/colors';
import { HttpRequestNodeEditorFormData } from '../..';
import MappingUtils, {
  MappingFields,
} from '../../../../../../../../../utils/mapping.utils';

interface HttpRequestNodeEditorJsonMappingPanelProps {
  mappingFields: FieldArrayWithId<
    {
      url: string;
      body: string;
      method: string;
      jsonMappings: MappingFields[];
      customerCustomFieldMappings: MappingFields[];
    },
    'jsonMappings',
    'id'
  >[];
  appendMappingElement: UseFieldArrayAppend<MappingFields>;
  removeMappingElement: UseFieldArrayRemove;
  replaceMappings: UseFieldArrayReplace<
    HttpRequestNodeEditorFormData,
    'jsonMappings'
  >;
  control: any;
  testData: any;
}

const schema = yup.object().shape({
  jsonMappings: yup.array().of(
    yup.object().shape({
      source: yup.string().required('Origem do dado é obrigatória'),
      destination: yup.string().required('Destino do dado é obrigatório'),
    }),
  ),
});

const HttpRequestNodeEditorJsonMappingPanel = ({
  testData,
  mappingFields,
  appendMappingElement,
  removeMappingElement,
  replaceMappings,
  control,
}: HttpRequestNodeEditorJsonMappingPanelProps) => {
  const handleExtractFromTestResult = () => {
    const jsonMappings = MappingUtils.mapJsonStructure({ input: testData });
    replaceMappings(jsonMappings);
  };

  return (
    <TabPanel>
      <Box justifySelf="end">
        <Button
          colorScheme="blue"
          onClick={() => appendMappingElement({ source: '', destination: '' })}
        >
          Adicionar Novo
        </Button>
        <Button ml={4} onClick={() => handleExtractFromTestResult()}>
          Extrair Automaticamente
        </Button>
      </Box>
      <form>
        <Table variant="simple" mt={4}>
          <Thead>
            <Tr>
              <Th>Origem</Th>
              <Th>Destino</Th>
              <Th></Th>
            </Tr>
          </Thead>
          <Tbody>
            {mappingFields.map((item, index) => (
              <Tr key={item.id}>
                <Td>
                  <Controller
                    name={`jsonMappings.${index}.source`}
                    control={control}
                    render={({ field }) => <Input {...field} />}
                  />
                </Td>
                <Td>
                  <Controller
                    name={`jsonMappings.${index}.destination`}
                    control={control}
                    render={({ field }) => <Input {...field} />}
                  />
                </Td>
                <Td>
                  <ButtonIcon
                    icon={<FaTrash fontSize="20px" color={colors.red} />}
                    onClick={() => removeMappingElement(index)}
                  />
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </form>
    </TabPanel>
  );
};

export default HttpRequestNodeEditorJsonMappingPanel;
