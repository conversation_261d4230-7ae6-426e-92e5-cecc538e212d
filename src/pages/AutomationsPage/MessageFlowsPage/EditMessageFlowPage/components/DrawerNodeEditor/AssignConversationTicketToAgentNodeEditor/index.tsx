import {
  Checkbox,
  DrawerBody,
  Flex,
  FormControl,
  FormLabel,
  Select,
  Text,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { useQuery } from 'react-query';
import * as yup from 'yup';
import { apiRoutes } from '../../../../../../../constants/api-routes';
import { colors } from '../../../../../../../constants/colors';
import { AssignConversationTicketToAgentNodeData } from '../../../../../../../types/ReactFlowNode';
import { UsersService } from '../../../../../../../services/users.service';
import { AiOutlineInfoCircle } from 'react-icons/ai';
import InputSelect, {
  SelectOption,
} from '../../../../../../../components/InputSelect';

const schema = yup
  .object({
    agentIds: yup
      .array()
      .of(yup.string().min(1, 'Selecione pelo menos um atendente')),
    overwriteAssignedAgent: yup.boolean().default(false),
  })
  .required();

export interface AssignConversationTicketToAgentEditorProps {
  data: AssignConversationTicketToAgentNodeData;
  formId: string;
  onSaveNode: (data: any) => void;
}
const AssignConversationTicketToAgentEditor = ({
  data,
  formId,
  onSaveNode,
}: AssignConversationTicketToAgentEditorProps) => {
  const { agentIds = [], overwriteAssignedAgent } = data;

  const {
    handleSubmit,
    setValue,
    register,
    watch,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      agentIds,
      overwriteAssignedAgent,
    },
  });
  const watchAgentIds = watch('agentIds');

  const { data: companyAgents = [] } = useQuery(
    apiRoutes.listCompanyAgents(),
    async () => {
      const { data } = await UsersService.listCompanyAgents();
      return data;
    },
    {
      select: (data) => {
        return data.filter((agent) => agent.isActive && agent.isAgent);
      },
    },
  );

  function handleChangeSelectedAgents(
    options: { value: string; label: string }[],
  ) {
    const selectedAgentIds = options.map((option) => option.value);
    setValue('agentIds', selectedAgentIds);
  }

  const agentOptions: SelectOption[] = companyAgents.map((agent) => ({
    value: agent.id,
    label: agent.name,
  }));

  const selectedAgents =
    (watchAgentIds
      ?.map((id) => agentOptions.find((option) => option.value === id))
      .filter(Boolean) as SelectOption[]) || [];

  return (
    <DrawerBody>
      <form onSubmit={handleSubmit(onSaveNode)} id={formId}>
        <Flex flexDir="column" gap={3}>
          <FormControl>
            <FormLabel>Atribuir atendimento à atendente</FormLabel>
            <InputSelect
              placeholder="Selecione uma opção"
              isMulti
              options={agentOptions}
              value={selectedAgents}
              onChange={handleChangeSelectedAgents}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.agentIds?.map((error: any) => error.message).join(', ')}
            </Text>
            {selectedAgents.length > 1 && (
              <Text color={colors.fontlightGrey} fontSize="xs">
                Caso seja selecionado mais de um atendente, o atendimento será
                atribuído aleatoriamente a qualquer um dos atendentes
                selecionados.
              </Text>
            )}
          </FormControl>
          <FormControl display="flex" alignItems="center">
            <Controller
              control={control}
              name="overwriteAssignedAgent"
              render={({ field: { onChange, onBlur, value } }) => {
                console.log({ value });
                return (
                  <Checkbox
                    isChecked={value}
                    onChange={(e) => {
                      onChange(e.target.checked);
                    }}
                    onBlur={onBlur}
                  >
                    Substituir atendente já associado (se houver)
                  </Checkbox>
                );
              }}
            />
          </FormControl>
          <Flex mb={4} alignItems="start">
            <AiOutlineInfoCircle size={'22px'} style={{ marginTop: '4px' }} />
            <Text ml={2}>
              Se o atendente for removido, essa etapa será pulada
              automaticamente no fluxo.
            </Text>
          </Flex>
        </Flex>
      </form>
    </DrawerBody>
  );
};

export default AssignConversationTicketToAgentEditor;
