import { useEffect, useMemo } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  DrawerBody,
  Flex,
  FormControl,
  Input,
  Select,
  Text,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useFieldArray, useForm } from 'react-hook-form';
import { FaTrash } from 'react-icons/fa';
import { v4 as uuidv4 } from 'uuid';
import * as yup from 'yup';
import ButtonIcon from '../../../../../../../components/ButtonIcon';
import FormLabel from '../../../../../../../components/FormLabel';
import InputSwitchControlled from '../../../../../../../components/InputSwitchControlled';
import { colors } from '../../../../../../../constants/colors';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../../../constants/api-routes';
import { MessageTemplatesService } from '../../../../../../../services/message-templates.service';
import { TriggerNodeData } from '../../../../../../../types/ReactFlowNode';
import { MESSAGE_TYPES } from './constants';
import { FiPlus } from 'react-icons/fi';

const flowTriggerSchema = yup
  .object({
    type: yup
      .string()
      .required('Tipo é obrigatório')
      .oneOf(['exact_match', 'keyword_match', 'quick_reply_message_template']),
    text: yup.string().required('Texto é obrigatório'),
    messageTemplateButtonId: yup.string().nullable(),
    isDefault: yup.boolean().default(false),
  })
  .required();

const schema = yup
  .object({
    flowTriggers: yup.array().of(flowTriggerSchema),
  })
  .required();
export interface TriggerNodeEditorProps {
  data: TriggerNodeData;
  formId: string;
  onSaveNode: (data: any) => void;
}

interface GroupedTrigger {
  field: any;
  index: number;
}

interface GroupedTriggers {
  [templateName: string]: GroupedTrigger[];
}

const TriggerNodeEditor = ({
  data,
  formId,
  onSaveNode,
}: TriggerNodeEditorProps) => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      flowTriggers: data.flowTriggers || [],
    },
  } as any);

  const {
    fields: flowTriggerFields,
    append: appendflowTrigger,
    remove: removeflowTrigger,
    update: updateTrigger,
  } = useFieldArray({
    control,
    name: 'flowTriggers',
  });

  const { data: templates } = useQuery(
    apiRoutes.listQuickReplyMessageTemplates(),
    async () => {
      const { data } =
        await MessageTemplatesService.listQuickReplyMessageTemplates();
      return data;
    },
    {
      refetchOnWindowFocus: true,
    },
  );

  const watchedFields = watch();

  const groupedTriggers = useMemo(() => {
    const groups: GroupedTriggers = {};
    flowTriggerFields.forEach((field, index) => {
      const templateName =
        watchedFields.flowTriggers?.[index]?.messageTemplateButton
          ?.messageTemplate?.name;
      if (
        templateName &&
        watchedFields.flowTriggers?.[index]?.type ===
          'quick_reply_message_template'
      ) {
        if (!groups[templateName]) {
          groups[templateName] = [];
        }
        groups[templateName].push({ field, index });
      }
    });
    return groups;
  }, [flowTriggerFields, watchedFields.flowTriggers]);

  function handleTemplateChange(templateId: string, index: number) {
    const selectedTemplate = templates?.find(
      (template) => template.id === templateId,
    );

    if (selectedTemplate) {
      const quickReplyButtons = selectedTemplate.messageTemplateButtons.filter(
        (button) => button.type === 'QUICK_REPLY',
      );

      setValue(`flowTriggers.${index}.type`, 'quick_reply_message_template');
      setValue(
        `flowTriggers.${index}.messageTemplateButton.messageTemplate.name`,
        selectedTemplate.name,
      );

      updateTrigger(index, {
        ...flowTriggerFields[index],
        type: 'quick_reply_message_template',
        messageTemplateButton: {
          messageTemplate: {
            name: selectedTemplate.name,
          },
        },
        text: quickReplyButtons[0]?.text || '',
        messageTemplateButtonId: quickReplyButtons[0]?.id || '',
      });

      const templateName = selectedTemplate.name;
      const existingTemplateTriggersIndexes = flowTriggerFields
        .map((field, i) =>
          i > index &&
          watchedFields.flowTriggers[i]?.messageTemplateButton?.messageTemplate
            ?.name === templateName
            ? i
            : null,
        )
        .filter((i) => i !== null)
        .reverse();

      existingTemplateTriggersIndexes.forEach((i) => {
        if (i !== null) removeflowTrigger(i);
      });

      quickReplyButtons.slice(1).forEach((button) => {
        appendflowTrigger({
          id: uuidv4(),
          type: 'quick_reply_message_template',
          messageTemplateButton: {
            messageTemplate: {
              name: selectedTemplate.name,
            },
          },
          text: button.text,
          messageTemplateButtonId: button.id,
          isDefault: false,
        });
      });
    }
  }

  function handleSaveNode(data: any) {
    const texts = new Set();
    for (const trigger of data.flowTriggers) {
      if (texts.has(trigger.text)) {
        toast({
          title: 'Validação',
          description: 'Cada trigger deve ter um valor único',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      texts.add(trigger.text);
    }
    onSaveNode(data);
  }

  function handleChangeDefault(index: number) {
    flowTriggerFields.forEach((_, i) => {
      if (i !== index) {
        setValue(`flowTriggers.${i}.isDefault`, false);
      }
    });
  }

  const renderTriggerForm = (field: any, index: number) => (
    <Card>
      <CardHeader paddingBottom={0}>Trigger {index + 1}</CardHeader>
      <CardBody flexDir="column" display="flex" gap={2}>
        <FormControl w="100%" display="flex" alignItems="center" gap={2}>
          <FormLabel width="100px">Tipo</FormLabel>
          <Select
            width="100%"
            placeholder="Selecione um tipo"
            {...register(`flowTriggers.${index}.type`)}
          >
            {MESSAGE_TYPES.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
          <Text color={colors.danger} fontSize="xs">
            {errors.flowTriggers?.[index]?.type?.message}
          </Text>
        </FormControl>

        {watchedFields.flowTriggers?.[index]?.type ===
          'quick_reply_message_template' && (
          <FormControl w="100%" display="flex" alignItems="center" gap={2}>
            <FormLabel width="100px">Template</FormLabel>
            <Select
              width="100%"
              placeholder="Selecione um template"
              {...register(
                `flowTriggers.${index}.messageTemplateButton.messageTemplate.name`,
              )}
              onChange={(e) => {
                const templateId = templates?.find(
                  (t) => t.name === e.target.value,
                )?.id;
                if (templateId) {
                  handleTemplateChange(templateId, index);
                }
              }}
            >
              {templates?.map((template) => (
                <option key={template.id} value={template.name}>
                  {template.name}
                </option>
              ))}
            </Select>
          </FormControl>
        )}

        <FormControl w="100%" display="flex" alignItems="center" gap={2}>
          <FormLabel width="100px">Valor</FormLabel>
          <Input
            width="100%"
            {...register(`flowTriggers.${index}.text`)}
            disabled={
              watchedFields.flowTriggers?.[index]?.type ===
              'quick_reply_message_template'
            }
          />
          <Text color={colors.danger} fontSize="xs">
            {errors.flowTriggers?.[index]?.text?.message}
          </Text>
        </FormControl>

        <FormControl w="100%" display="flex" alignItems="center" gap={2}>
          <FormLabel
            width="100px"
            tooltip="Acionado ao conectar um fluxo externo"
          >
            Default
          </FormLabel>
          <InputSwitchControlled
            control={control}
            onChange={() => handleChangeDefault(index)}
            name={`flowTriggers.${index}.isDefault`}
          />
        </FormControl>
      </CardBody>
    </Card>
  );

  return (
    <DrawerBody>
      {data.flowTriggers?.[0]?.type === 'abandoned_cart' ? (
        <Box>
          Esse fluxo é do tipo carrinho abandonado, não é possível editar os
          triggers
        </Box>
      ) : data.flowTriggers?.[0]?.type === 'csat' ? (
        <Box>
          Esse fluxo é do tipo ticket finalizado com CSAT, não é possível editar
          os triggers
        </Box>
      ) : data.flowTriggers?.[0]?.type === 'initial_contact' ? (
        <Box>
          Esse fluxo é do tipo contato inicial, não é possível editar os
          triggers
        </Box>
      ) : (
        <form onSubmit={handleSubmit(handleSaveNode)} id={formId}>
          <Flex flexDir="column" gap={3}>
            {flowTriggerFields.map((field, index) => {
              const templateName =
                watchedFields.flowTriggers?.[index]?.messageTemplateButton
                  ?.messageTemplate?.name;
              const isQuickReplyMessageTemplate =
                watchedFields.flowTriggers?.[index]?.type ===
                'quick_reply_message_template';

              if (
                isQuickReplyMessageTemplate &&
                templateName &&
                groupedTriggers[templateName]
              ) {
                if (groupedTriggers[templateName][0].index === index) {
                  return (
                    <Box
                      key={field.id}
                      p={4}
                      border="2px"
                      borderColor="blue.200"
                      borderRadius="lg"
                      bg="blue.50"
                    >
                      <Text mb={2} fontWeight="bold" color="blue.600">
                        Template: {templateName}
                      </Text>
                      {groupedTriggers[templateName].map(
                        ({ field: groupField, index: groupIndex }) => (
                          <Flex key={groupField.id} mb={2} alignItems="center">
                            {renderTriggerForm(groupField, groupIndex)}
                            <ButtonIcon
                              icon={
                                <FaTrash
                                  fontSize="20px"
                                  color={colors.danger}
                                />
                              }
                              onClick={() => removeflowTrigger(groupIndex)}
                            />
                          </Flex>
                        ),
                      )}
                    </Box>
                  );
                }
                return null;
              }

              return (
                <Flex key={field.id} alignItems="center">
                  {renderTriggerForm(field, index)}
                  {flowTriggerFields.length >= 1 && (
                    <ButtonIcon
                      icon={<FaTrash fontSize="20px" color={colors.danger} />}
                      onClick={() => removeflowTrigger(index)}
                    />
                  )}
                </Flex>
              );
            })}
            <Button
              onClick={() => {
                appendflowTrigger({
                  id: uuidv4(),
                  type: null,
                  text: '',
                  messageTemplateButtonId: null,
                  messageTemplateButton: {
                    messageTemplate: {
                      name: null,
                    },
                  },
                });
              }}
              className="flex items-center gap-2"
              width="100%"
            >
              <FiPlus size={16} />
              Adicionar trigger
            </Button>
          </Flex>
        </form>
      )}
    </DrawerBody>
  );
};

export default TriggerNodeEditor;
