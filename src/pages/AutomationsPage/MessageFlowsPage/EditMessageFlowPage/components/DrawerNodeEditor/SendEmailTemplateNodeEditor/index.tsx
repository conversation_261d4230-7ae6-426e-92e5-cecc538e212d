import {
  DrawerBody,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Text,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useQuery } from 'react-query';
import * as yup from 'yup';
import { SendEmailTemplateNodeData } from '../../../../../../../types/ReactFlowNode';
import { apiRoutes } from '../../../../../../../constants/api-routes';
import { colors } from '../../../../../../../constants/colors';
import InputSelect from '../../../../../../../components/InputSelect';
import { useMemo, useState } from 'react';
import { EmailTemplatesService } from '../../../../../../../services/email-templates.service';
import { EmailTemplate } from '../../../../../../../types/Prisma';
import EmailTemplatePreview from '../../../../../../../components/EmailTemplatePreview';
import { EmailTemplateUtils } from '../../../../../../../utils/email-template.utils';
import { Node } from 'reactflow';

const schema = yup
  .object({
    emailTemplate: yup.object().shape({
      value: yup.string().required(),
      label: yup.string().required(),
    }),
  })
  .required();

export interface SendEmailTemplateNodeEditorProps {
  data: SendEmailTemplateNodeData;
  formId: string;
  onSaveNode: (data: Node) => void;
}
const SendEmailTemplateNodeEditor = ({
  data,
  formId,
  onSaveNode,
}: SendEmailTemplateNodeEditorProps) => {
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);

  const [templateArgs, setTemplateArgs] = useState<{ [key: string]: string }>(
    data.templateArgs || {},
  );

  function handleChangeTemplateParameter(parameter: string, value: string) {
    setTemplateArgs({
      ...templateArgs,
      [parameter]: value,
    });
  }

  const { emailTemplateId } = data;
  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
    watch,
  } = useForm({
    resolver: yupResolver(schema),
  });

  useQuery(
    apiRoutes.listEmailTemplates(),
    async () => {
      const { data } = await EmailTemplatesService.listEmailTemplates();
      return data;
    },
    {
      onSuccess: (data) => {
        const currentEmailTemplate: EmailTemplate | undefined = data.find(
          (emailTemplate: EmailTemplate) =>
            emailTemplate.id === emailTemplateId,
        );
        if (currentEmailTemplate) {
          setValue('emailTemplate', {
            value: currentEmailTemplate.id,
            label: currentEmailTemplate.name,
          });
        }

        setEmailTemplates(data);
      },
    },
  );

  const selectedEmailTemplate = useMemo(
    () =>
      emailTemplates.find(
        (emailTemplate) => emailTemplate.id === watch('emailTemplate')?.value,
      ),
    [emailTemplates, watch('emailTemplate')?.value],
  );

  const customParameters = EmailTemplateUtils.getCustomParametersInText(
    selectedEmailTemplate?.html || '',
  );

  const handleFormSubmit = (data: FieldValues) => {
    const filteredTemplateArgs = Object.keys(templateArgs).reduce(
      (acc, key) => {
        if (customParameters.includes(key)) {
          acc[key] = templateArgs[key];
        }
        return acc;
      },
      {} as { [key: string]: string },
    );

    const emailTemplateId = data.emailTemplate.value;
    const emailTemplateData = {
      emailTemplateId,
      templateArgs: filteredTemplateArgs,
    };
    onSaveNode({ ...data, ...emailTemplateData } as unknown as Node);
  };

  return (
    <DrawerBody>
      <form onSubmit={handleSubmit(handleFormSubmit)} id={formId}>
        <Flex flexDir={'column'} gap={3}>
          <FormControl>
            <FormLabel>Template</FormLabel>
            <Controller
              name="emailTemplate"
              control={control}
              render={({ field }) => (
                <InputSelect
                  placeholder="Selecionar template de email"
                  options={emailTemplates.map((emailTemplate) => ({
                    value: emailTemplate.id,
                    label: emailTemplate.name,
                  }))}
                  value={field.value}
                  onChange={field.onChange}
                />
              )}
            />
            {errors.emailTemplate?.message ? (
              <Text color={colors.danger} fontSize="xs">
                {errors.emailTemplate?.message}
              </Text>
            ) : (
              <Text color={colors.darkGrey} fontSize="xs">
                Caso o cliente não tenha um email cadastrado, o template de
                email não será enviado e o próximo nó será executado.
              </Text>
            )}
          </FormControl>
          {selectedEmailTemplate &&
            customParameters.map((param) => (
              <FormControl isRequired key={param}>
                <FormLabel fontSize="sm">
                  {param.replace(/[{}]/g, '')}
                </FormLabel>
                <Input
                  size="sm"
                  onChange={(e) =>
                    handleChangeTemplateParameter(param, e.target.value)
                  }
                  value={templateArgs[param] || ''}
                  required
                />
              </FormControl>
            ))}

          {selectedEmailTemplate && (
            <EmailTemplatePreview emailTemplate={selectedEmailTemplate} />
          )}
        </Flex>
      </form>
    </DrawerBody>
  );
};

export default SendEmailTemplateNodeEditor;
