import { CheckIcon, CloseIcon, EditIcon, SettingsIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  ButtonGroup,
  Editable,
  EditableInput,
  EditablePreview,
  Flex,
  IconButton,
  Switch,
  Text,
  Tooltip,
  useDisclosure,
  useEditableControls,
  useToast,
} from '@chakra-ui/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { AiOutlineMessage } from 'react-icons/ai';
import {
  FaAngleLeft,
  FaExchangeAlt,
  FaTag,
  FaDotCircle,
  FaQuestionCircle,
  FaTrash,
} from 'react-icons/fa';
import { MdEmail, MdOutlinePermMedia } from 'react-icons/md';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import ReactFlow, {
  addEdge,
  Background,
  Controls,
  EdgeMouseHandler,
  MiniMap,
  Node,
  NodeChange,
  useEdgesState,
  useNodesState,
  useReactFlow,
  useUpdateNodeInternals,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { v4 as uuidv4 } from 'uuid';
import AlertDialogBase from '../../../../components/AlertDialog';
import ButtonIcon from '../../../../components/ButtonIcon';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import useOutsideClickDetector from '../../../../hooks/useOutsideClickDetector';
import {
  FlowsService,
  ShowFlowResponse,
  UpdateFlowDto,
} from '../../../../services/flows.service';
import {
  FlowNodeConditionBlock,
  NodeData,
  NodeType,
} from '../../../../types/ReactFlowNode';
import { ReactFlowUtils } from '../../../../utils/react-flow.utils';
import DrawerNodeEditor from './components/DrawerNodeEditor';
import MoveConversationToCategoryNode from './components/MoveConversationToCategoryNode';
import SendWhatsappMediaNode from './components/SendWhatsappMediaNode';
import SendWhatsappMessageNode from './components/SendWhatsappMessageNode';
import TriggerNode from './components/TriggerNode';
import AddTagToCustomerNode from './components/AddTagToCustomerNode';
import EndWhatsappConversationNode from './components/EndWhatsappConversationNode';
import SaveCustomerResponseNode from './components/SaveCustomerResponseNode';
import { TbArrowsTransferDown, TbMessageCirclePlus } from 'react-icons/tb';
import ConditionsCheckNode from './components/ConditionsCheckNode';
import { GiChoice } from 'react-icons/gi';
import TimeDelayNode from './components/TimeDelayNode';
import { IoIosTimer } from 'react-icons/io';
import SendWhatsappMessageTemplateNode from './components/SendWhatsappMessageTemplateNode';
import { CgTemplate } from 'react-icons/cg';
import HttpRequestNode from './components/HttpRequestNode';
import { FlowTrigger } from '../../../../types/FlowTrigger';
import SendEmailTemplateNode from './components/SendEmailTemplateNode';
import DrawerFlowSettings, {
  FlowSettingsData,
} from './components/DrawerFlowSettings';
import AssignConversationTicketToAgentNode from './components/AssignConversationTicketToAgentNode';
import { HiOutlineUserAdd } from 'react-icons/hi';

const nodeTypes: Record<NodeType, any> = {
  send_whatsapp_message: SendWhatsappMessageNode,
  send_whatsapp_media: SendWhatsappMediaNode,
  trigger: TriggerNode,
  move_conversation_to_category: MoveConversationToCategoryNode,
  add_tag_to_customer: AddTagToCustomerNode,
  end_whatsapp_conversation: EndWhatsappConversationNode,
  save_customer_response: SaveCustomerResponseNode,
  conditions_check: ConditionsCheckNode,
  http_request: HttpRequestNode,
  time_delay: TimeDelayNode,
  send_whatsapp_message_template: SendWhatsappMessageTemplateNode,
  send_email_template: SendEmailTemplateNode,
  assign_conversation_ticket_to_agent: AssignConversationTicketToAgentNode,
};

const FlowMenuOptions: {
  label: string;
  icon: JSX.Element;
  action: NodeType;
}[] = [
  {
    label: 'Enviar whatsapp',
    icon: <AiOutlineMessage />,
    action: 'send_whatsapp_message',
  },
  {
    label: 'Salvar resposta do cliente',
    icon: <TbMessageCirclePlus />,
    action: 'save_customer_response',
  },
  {
    label: 'Enviar mídia',
    icon: <MdOutlinePermMedia />,
    action: 'send_whatsapp_media',
  },
  {
    label: 'Enviar template de whatsapp',
    icon: <CgTemplate />,
    action: 'send_whatsapp_message_template',
  },
  {
    label: 'Enviar template de email',
    icon: <MdEmail />,
    action: 'send_email_template',
  },
  {
    label: 'Mover conversa para categoria',
    icon: <FaExchangeAlt />,
    action: 'move_conversation_to_category',
  },
  {
    label: 'Adicionar Tag',
    icon: <FaTag />,
    action: 'add_tag_to_customer',
  },
  {
    label: 'Checar condições',
    icon: <GiChoice />,
    action: 'conditions_check',
  },
  {
    label: 'Requisição HTTP',
    icon: <TbArrowsTransferDown />,
    action: 'http_request',
  },
  {
    label: 'Time delay',
    icon: <IoIosTimer />,
    action: 'time_delay',
  },
  {
    label: 'Finalizar Conversa',
    icon: <FaDotCircle />,
    action: 'end_whatsapp_conversation',
  },
  {
    label: 'Atribuir atendimento à atendente',
    icon: <HiOutlineUserAdd />,
    action: 'assign_conversation_ticket_to_agent',
  },
];

const FlowEditorPage = () => {
  const navigate = useNavigate();
  const { flowId } = useParams();
  const [flow, setFlow] = useState<ShowFlowResponse | null>(null);
  const [isFlowSettingsOpen, setIsFlowSettingsOpen] = useState(false);
  const reactFlowWrapper = useRef<any>(null);
  const {
    isOpen: isAlertOpen,
    onClose: onCloseAlert,
    onOpen: onOpenAlert,
  } = useDisclosure();
  const toast = useToast();
  const isCopyMode = location.pathname.endsWith('/copiar');

  useQuery(
    apiRoutes.showFlow(flowId!),
    async () => {
      if (isCopyMode) {
        const { data } = await FlowsService.copyFlow(flowId!);
        return data;
      } else {
        const { data } = await FlowsService.showFlow(flowId!);
        return data;
      }
    },
    {
      onSuccess: (data) => {
        if (data.type === 'default') {
          setFlow(data);
          return;
        }

        if (data.type === 'abandoned_cart') {
          setFlow(
            setCustomTrigger(data, {
              type: 'abandoned_cart',
              text: 'Carrinho abandonado',
            }),
          );
          return;
        }

        if (data.type === 'csat') {
          setFlow(
            setCustomTrigger(data, {
              type: 'csat',
              text: 'Ticket finalizado com CSAT',
            }),
          );
          return;
        }

        if (data.type === 'initial_contact') {
          setFlow(
            setCustomTrigger(data, {
              type: 'initial_contact',
              text: 'Contato inicial',
            }),
          );
          return;
        }
      },
    },
  );

  const setCustomTrigger = (
    data: ShowFlowResponse,
    triggerData: Pick<FlowTrigger, 'type' | 'text'>,
  ): ShowFlowResponse => {
    if (data.flowTriggers.length === 0) {
      data.flowTriggers.push({
        id: uuidv4(),
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        companyId: data.companyId,
        flowId: data.id,
        targetFlowNodeId: '',
        invocationCount: 0,
        isDeleted: false,
        ...triggerData,
      });
    }
    return data;
  };

  const updateFlow = useMutation(
    async (updateFlowDto: UpdateFlowDto) => {
      if (!flow || !flow.id) {
        throw new Error('FlowId não está definido');
      }
      const { data } = await FlowsService.updateFlow(flow.id, updateFlowDto);
      return data;
    },
    {
      onSuccess: () => {
        toast({
          title: 'Fluxo atualizado com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.automations.messageFlows.index());
      },
    },
  );

  const updateNodeInternals = useUpdateNodeInternals();
  const { getNode, project } = useReactFlow();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdgeId, setSelectedEdgeId] = useState<string | null>(null);
  const [edgeClickedPosition, setEdgeClickedPostion] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const deleteButtonRef = useRef<HTMLDivElement>(null);
  const [menuPosition, setMenuPosition] = useState<{
    flowX: number;
    flowY: number;
    offsetX: number;
    offsetY: number;
  } | null>(null);
  const {
    isOpen: isNodeModalOpen,
    onOpen: onNodeModalOpen,
    onClose: onNodeModalClose,
  } = useDisclosure();
  const [flowTitle, setFlowTitle] = useState<string>('');
  const [isFlowActive, setIsFlowActive] = useState<boolean>(false);
  const connectionCreated = useRef(false);

  const menuRef = useRef<any>(false);
  useOutsideClickDetector(menuRef, () => setMenuPosition(null));

  const flowEditorPageRef = useRef<HTMLDivElement | null>(null);
  useOutsideClickDetector(
    flowEditorPageRef,
    onOpenAlert,
    (event: MouseEvent) => {
      const element = event.target as HTMLElement;

      return !!element.closest(
        '[data-prevent-unsaved-exit-FlowEditorPage="true"]',
      );
    },
  );

  useEffect(() => {
    if (flow) {
      setFlowTitle(flow.title);
      setIsFlowActive(flow.isActive);
      setNodes(ReactFlowUtils.extractNodesFromFlow(flow));
      setEdges(ReactFlowUtils.extractEdgesFromFlow(flow));
    }
  }, [flow, setNodes, setEdges]);

  const onConnectStart = useCallback(() => {
    connectionCreated.current = false;
  }, []);

  const onConnect = useCallback(
    (params: any) => {
      connectionCreated.current = true;
      setEdges((eds) => addEdge(params, eds));
    },
    [setEdges],
  );

  const getReactFlowPositionFromEvent = useCallback(
    (e: any) => {
      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      return project({
        x: e.clientX - reactFlowBounds.left,
        y: e.clientY - reactFlowBounds.top,
      });
    },
    [project],
  );

  const onContextMenu = useCallback(
    (e: any) => {
      e.preventDefault();
      const { clientX, clientY } = e;
      const flowPosition = getReactFlowPositionFromEvent(e);

      setMenuPosition({
        flowX: flowPosition.x,
        flowY: flowPosition.y,
        offsetX: clientX - 50, // sidebar width
        offsetY: clientY - 40, // header height
      });
    },
    [getReactFlowPositionFromEvent],
  );

  useEffect(() => {
    removeInvalidEdges();
  }, [JSON.stringify(nodes)]);

  function handleNodeChange(changes: NodeChange[]) {
    const nextChanges = changes.reduce((acc, change) => {
      if (change.type === 'remove') {
        const node = getNode(change.id);
        if (node && node.type === 'trigger') return acc;
      }

      if (change.type === 'position') {
        const node = getNode(change.id);
        if (node && node.type === 'trigger') return acc;
      }

      return [...acc, change];
    }, [] as NodeChange[]);

    onNodesChange(nextChanges);
  }

  function handleNodeClick(event: React.MouseEvent, node: Node) {
    setSelectedNode(node);
    onNodeModalOpen();
  }

  function filterNodes(nodes: Node[]) {
    return nodes.filter((node) => node.type !== 'trigger');
  }

  async function handleSaveFlow() {
    const TriggerNode = nodes.find((node) => node.type === 'trigger');
    const flowTriggers = TriggerNode?.data.flowTriggers || [];
    const filteredNodes = filterNodes(nodes);

    const flowType = flow?.type
      ? flow.type
      : flowTriggers[0].type === 'abandoned_cart'
        ? 'abandoned_cart'
        : flowTriggers[0].type === 'csat'
          ? 'csat'
          : flowTriggers[0].type === 'initial_contact'
            ? 'initial_contact'
            : 'default';

    await updateFlow.mutateAsync({
      title: flowTitle,
      type: flowType,
      isActive: isFlowActive,
      repeatOnInvalidInput: flow?.repeatOnInvalidInput || false,
      edges,
      nodes: filteredNodes,
      flowTriggers,
    });
  }

  function handleSaveNode(data: any) {
    setNodes((nds) => {
      return nds.map((node) => {
        if (node.id === selectedNode!.id) {
          return {
            ...node,
            data,
          };
        }
        return node;
      });
    });
    updateNodeInternals(selectedNode!.id);
    onNodeModalClose();
  }

  function deleteNode(nodeId: string) {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    onNodeModalClose();
    setSelectedNode(null);
  }

  function setDefaultConditionBlock(): FlowNodeConditionBlock {
    const blockId = uuidv4();

    const defaultConditionBlock = {
      id: String(blockId),
      flowNodeConditions: [
        {
          id: uuidv4(),
          type: 'default' as const,
          value: { value: '1' },
          comparisonOperator: 'EQUALS' as const,
          flowNodeConditionBlockId: String(blockId),
        },
      ],
      conditionalJoinType: 'AND' as const,
      priority: 999,
      targetFlowNodeId: '',
    };

    return defaultConditionBlock;
  }

  function removeInvalidEdges() {
    setEdges((eds) => {
      return eds.filter((edge) => {
        const isSourceValid = nodes.some((node) => {
          if (
            node.type === 'send_whatsapp_message' &&
            node.data.buttons &&
            node.data.buttons.length > 0 &&
            !node.data.buttons[0].url
          ) {
            return node.data.buttons.some(
              (button: any) => button.id === edge.sourceHandle,
            );
          }
          if (
            node.type === 'conditions_check' &&
            node.data.flowNodeConditionBlocks &&
            node.data.flowNodeConditionBlocks.length > 0
          ) {
            return node.data.flowNodeConditionBlocks.some(
              (conditionBlock: any) => conditionBlock.id === edge.sourceHandle,
            );
          }
          if (
            node.type === 'trigger' &&
            node.data.flowTriggers &&
            node.data.flowTriggers.length > 0
          ) {
            return node.data.flowTriggers.some(
              (trigger: any) => trigger.id === edge.sourceHandle,
            );
          }
          if (node.type === 'http_request') {
            return edge.source === node.id;
          }

          return node.id === edge.source && edge.source === edge.sourceHandle;
        });
        const isTargetValid = nodes.some(
          (node) => node.id === edge.target || node.id === edge.targetHandle,
        );

        return isSourceValid && isTargetValid;
      });
    });
  }

  function createNewNode(nodeType: NodeType) {
    const nodeData: Record<NodeType, NodeData> = {
      trigger: {
        flowTriggers: [],
      },
      move_conversation_to_category: {
        targetConversationCategoryId: '',
      },
      send_whatsapp_message: {
        text: '',
        buttons: [],
      },
      send_whatsapp_media: {
        file: undefined,
        fileId: '',
        fileKey: '',
        mediaType: undefined,
      },
      add_tag_to_customer: {
        tagId: '',
      },
      end_whatsapp_conversation: {
        text: '',
      },
      save_customer_response: {
        companyDefinedFieldId: '',
      },
      conditions_check: {
        flowNodeConditionBlocks: [setDefaultConditionBlock()],
      },
      http_request: {
        url: '',
        method: '',
        headers: [],
        body: '',
        dynamicVariables: [],
      },
      send_whatsapp_message_template: {
        messageTemplateId: '',
        templateArgs: {},
      },
      send_email_template: {
        emailTemplateId: '',
        templateArgs: {},
      },
      assign_conversation_ticket_to_agent: {
        agentIds: [],
        overwriteAssignedAgent: false,
      },
      time_delay: {},
    };
    const newNode = {
      id: uuidv4(),
      type: nodeType,
      position: { x: menuPosition!.flowX, y: menuPosition!.flowY },
      data: nodeData[nodeType],
    };

    setNodes((nds) => [newNode, ...nds]);
    setSelectedNode(newNode);
    onNodeModalOpen();
    setMenuPosition(null);
  }

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const DELETE_KEYS = ['Delete', 'Backspace'];
      if (DELETE_KEYS.includes(event.key)) {
        handleEdgeDeletion();
        setSelectedEdgeId('');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedEdgeId, setEdges]);

  const handleEdgeClick: EdgeMouseHandler = (event, edge) => {
    setEdgeClickedPostion({ x: event.clientX, y: event.clientY });
    setSelectedEdgeId(edge.id);
  };

  const handleEdgeDeletion = () => {
    if (selectedEdgeId) {
      setEdges((edges) => edges.filter((e) => e.id !== selectedEdgeId));
      setSelectedEdgeId(null);
      setEdgeClickedPostion(null);
    }
  };

  useOutsideClickDetector(deleteButtonRef, () => {
    setEdgeClickedPostion(null);
  });

  const handlePaneMove = useCallback(() => {
    setEdgeClickedPostion(null);
  }, []);

  const handleSaveFlowSettings = (data: FlowSettingsData) => {
    setIsFlowSettingsOpen(false);
    if (!flow) return;
    setFlow({
      ...flow,
      ...data,
    });
  };

  return (
    <Flex ref={flowEditorPageRef} flexDir="column" width="100%" height="100%">
      <Flex justifyContent={'space-between'} alignItems="center" padding={2}>
        <Flex alignItems={'center'} gap={2}>
          <ButtonIcon icon={<FaAngleLeft />} onClick={() => onOpenAlert()} />
          <Editable
            value={flowTitle}
            onChange={setFlowTitle}
            fontWeight="bold"
            fontSize="lg"
            gap={2}
            display="flex"
            isPreviewFocusable={false}
          >
            <EditableClickablePreview />
            <EditableInput />
            <EditableControls />
          </Editable>

          <IconButton
            size="sm"
            icon={<SettingsIcon />}
            aria-label="Configurações do fluxo"
            onClick={() => setIsFlowSettingsOpen(true)}
          />
        </Flex>
        <Flex>
          <Text fontWeight="bold" marginRight={2}>
            Ativo?
          </Text>
          <Switch
            isChecked={isFlowActive}
            onChange={(e) => setIsFlowActive(e.target.checked)}
          />
        </Flex>
        <Button isLoading={updateFlow.isLoading} onClick={handleSaveFlow}>
          Salvar mudanças
        </Button>
      </Flex>
      <Box ref={reactFlowWrapper} height="100%">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodeChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          onConnectStart={onConnectStart}
          onNodeClick={handleNodeClick}
          onEdgeClick={handleEdgeClick}
          onContextMenu={onContextMenu}
          onMove={handlePaneMove}
          minZoom={0.3}
          style={{ position: 'relative', flex: 1 }}
        >
          <Controls position="top-right" />
          <MiniMap position="bottom-left" />
          <Background gap={12} size={1} />
          {selectedEdgeId && edgeClickedPosition && (
            <Box
              ref={deleteButtonRef}
              position="fixed"
              left={`${edgeClickedPosition.x + 20}px`}
              top={`${edgeClickedPosition.y - 20}px`}
              zIndex={10}
              transform="translate(-50%, -50%)"
            >
              <Tooltip>
                <IconButton
                  icon={<FaTrash />}
                  aria-label="Deletar aresta"
                  colorScheme="red"
                  backgroundColor="red.400"
                  size="sm"
                  onClick={handleEdgeDeletion}
                />
              </Tooltip>
            </Box>
          )}
          <Box
            display={menuPosition ? 'block' : 'none'}
            ref={menuRef}
            position="absolute"
            left={menuPosition?.offsetX}
            top={menuPosition?.offsetY}
            right="auto"
            bottom="auto"
            zIndex={10}
            padding="16px"
            boxShadow="sm"
            bg="#3f3f3f"
            color="white"
            rounded="md"
            gap={20}
          >
            <Flex direction={'column'} gap={1}>
              {FlowMenuOptions.map((option) => {
                const isBlocked =
                  flow?.type === 'abandoned_cart' &&
                  ['send_whatsapp_message', 'send_whatsapp_media'].includes(
                    option.action,
                  );

                return (
                  <Flex
                    padding={'4px 8px'}
                    borderRadius={5}
                    onClick={
                      !isBlocked
                        ? () => createNewNode(option.action)
                        : undefined
                    }
                    alignItems="center"
                    gap={3}
                    _hover={
                      !isBlocked
                        ? { cursor: 'pointer', bg: '#5b5b5b' }
                        : { cursor: 'not-allowed' }
                    }
                    opacity={isBlocked ? 0.5 : 1}
                  >
                    {option.icon}
                    <Text>{option.label}</Text>
                  </Flex>
                );
              })}
            </Flex>
          </Box>
          {nodes.length <= 1 && (
            <Box
              position="absolute"
              bgColor="#293845"
              color="white"
              borderRadius={'15px'}
              padding="10px"
              top="40px"
              left="50%"
              transform={'translate(-50%, 0)'}
            >
              <Text>
                Clique com o botão direito do mouse para adicionar um novo bloco
              </Text>
            </Box>
          )}
        </ReactFlow>
      </Box>
      {selectedNode && (
        <DrawerNodeEditor
          onDeleteNode={(nodeId) => deleteNode(nodeId)}
          isOpen={isNodeModalOpen}
          onClose={onNodeModalClose}
          selectedNode={selectedNode}
          onSaveNode={handleSaveNode}
          flowType={flow?.type || 'default'}
          nodes={nodes}
          edges={edges}
        />
      )}
      <AlertDialogBase
        isOpen={isAlertOpen}
        onClose={onCloseAlert}
        title="Salvar alterações?"
        buttonRejectText="Descartar"
        buttonConfirmText="Salvar"
        buttonConfirmColor="blue"
        onConfirm={async () => {
          onCloseAlert();
          await handleSaveFlow();
        }}
        onReject={() => {
          navigate(appPaths.automations.messageFlows.index());
        }}
      >
        Deseja salvar as alterações?
      </AlertDialogBase>
      {flow && (
        <DrawerFlowSettings
          isOpen={isFlowSettingsOpen}
          onClickClose={() => setIsFlowSettingsOpen(false)}
          onClickSave={handleSaveFlowSettings}
          flow={flow}
        />
      )}
    </Flex>
  );
};

function EditableControls() {
  const {
    isEditing,
    getSubmitButtonProps,
    getCancelButtonProps,
    getEditButtonProps,
  } = useEditableControls();

  return isEditing ? (
    <ButtonGroup justifyContent="center" size="sm">
      <IconButton icon={<CheckIcon />} {...(getSubmitButtonProps() as any)} />
      <IconButton icon={<CloseIcon />} {...(getCancelButtonProps() as any)} />
    </ButtonGroup>
  ) : (
    <Flex justifyContent="center">
      <IconButton
        size="sm"
        icon={<EditIcon />}
        {...(getEditButtonProps() as any)}
      />
    </Flex>
  );
}

function EditableClickablePreview() {
  const { getEditButtonProps } = useEditableControls();

  return <EditablePreview {...getEditButtonProps()} />;
}

export default FlowEditorPage;
