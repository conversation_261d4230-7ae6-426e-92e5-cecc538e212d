import {
  Box,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalOverlay,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { IoCartOutline, IoNewspaperOutline } from 'react-icons/io5';
import { RiNodeTree } from 'react-icons/ri';
import { useMutation } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../../constants/app-paths';
import { colors } from '../../../../../constants/colors';
import {
  FlowsService,
  ListFlowItem,
} from '../../../../../services/flows.service';
import { noop } from '@tanstack/react-table';
import { FlowType } from '../../../../../types/Prisma';
import { IoIosCall } from 'react-icons/io';

interface createFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  hasCsatFlow: boolean;
  initialContactFlow: ListFlowItem | null;
}

export const CreateFlowModal = ({
  isOpen,
  onClose,
  hasCsatFlow,
  initialContactFlow,
}: createFlowModalProps) => {
  const navigate = useNavigate();

  type FlowTypesBlocks = {
    id: number;
    title: string;
    description: string;
    onClick: () => void;
    icon: React.ReactNode;
    isDisabled?: boolean;
    disabledMessage?: string;
  };

  const flowTypes: FlowTypesBlocks[] = [
    {
      id: 1,
      title: 'Padrão',
      description: 'Personalize a jornada de atendimento do seu cliente',
      onClick: () => createFlowMutation.mutateAsync('default'),
      icon: <RiNodeTree size={48} color={colors.primaryMedium} />,
    },
    {
      id: 2,
      title: 'Atendimento',
      description:
        'Personalize a jornada do seu cliente no contato inicial com a empresa',
      onClick: () =>
        initialContactFlow
          ? navigate(
              appPaths.automations.messageFlows.editMessageFlow(
                initialContactFlow.id,
              ),
            )
          : createFlowMutation.mutateAsync('initial_contact'),

      icon: <IoIosCall size={48} color={colors.chartColors.yellow.primary} />,
    },
    {
      id: 3,
      title: 'Carrinho Abandonado',
      description: 'Personalize a jornada de carrinho abandonado',
      onClick: () => createFlowMutation.mutateAsync('abandoned_cart'),
      icon: (
        <IoCartOutline
          size={48}
          color={colors.chartColors.lightGreen.primary}
        />
      ),
    },
    {
      id: 4,
      title: 'Atendimento finalizado por CSAT',
      description:
        'Personalize a avaliação do seu cliente quando a conversa é finalizada',
      onClick: () => createFlowMutation.mutateAsync('csat'),
      isDisabled: hasCsatFlow,
      icon: (
        <IoNewspaperOutline
          size={48}
          color={colors.chartColors.purple.primary}
        />
      ),
      disabledMessage: 'Você já possui um fluxo de CSAT',
    },
  ];

  const createFlowMutation = useMutation(
    async (type: FlowType) => {
      const { data } = await FlowsService.createFlow({
        title: 'Sem título',
        type: type,
      });
      return data;
    },
    {
      onSuccess: (data) => {
        navigate(appPaths.automations.messageFlows.editMessageFlow(data.id));
      },
    },
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Criação de Fluxo</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          {flowTypes.map((block) => (
            <Tooltip
              key={block.id}
              placement="bottom"
              label={block?.disabledMessage}
              hidden={!block?.isDisabled}
            >
              <Box
                key={block.id}
                as="button"
                onClick={block.isDisabled ? noop : block.onClick}
                width="100%"
                minHeight={110}
                textAlign="left"
                p={4}
                mb={4}
                borderRadius="md"
                boxShadow="sm"
                _hover={block.isDisabled ? {} : { bg: 'gray.100' }}
                opacity={block.isDisabled ? 0.5 : 1}
                cursor={block.isDisabled ? 'not-allowed' : 'pointer'}
              >
                <Flex>
                  <Flex mr={4} alignItems="center">
                    {block.icon}
                  </Flex>
                  <Flex direction="column" justifyContent="center" flex="1">
                    <Text fontWeight="bold" fontSize="lg">
                      {block.title}
                    </Text>
                    <Text mt={2} fontSize="sm" color="gray.600">
                      {block.description}
                    </Text>
                  </Flex>
                </Flex>
              </Box>
            </Tooltip>
          ))}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
