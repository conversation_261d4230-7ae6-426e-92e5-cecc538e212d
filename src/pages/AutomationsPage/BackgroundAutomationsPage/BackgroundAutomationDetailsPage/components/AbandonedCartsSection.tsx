import {
  AbandonedCartsService,
  AbandonedCartStatus,
} from '../../../../../services/abandoned-carts.service';
import Pagination from '../../../../../components/Pagination';
import LoadingScreen from '../../../../../components/LoadingScreen';
import { FiFilter } from 'react-icons/fi';
import React, { useState } from 'react';
import './react-datepicker.css';
import {
  Box,
  Button,
  Divider,
  Flex,
  Heading,
  useDisclosure,
} from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import AbandonedCartsFilterPanel, {
  AbandonedCartsFilterFormValues,
} from './AbandonedCartsFilterPanel';
import { useForm } from 'react-hook-form';
import AbandonedCartsTable from './AbandonedCartsTable';

function AbandonedCartsSection() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(20);

  const [appliedFilters, setAppliedFilters] = useState({});

  const { isOpen, onOpen, onClose } = useDisclosure();

  const {
    data: abandonedCartsData,
    isLoading,
    refetch,
  } = useQuery(
    apiRoutes.listAbandonedCarts(appliedFilters),
    async () => {
      const response =
        await AbandonedCartsService.listAbandonedCarts(appliedFilters);
      return response;
    },
    {
      keepPreviousData: true,
    },
  );

  const handleOpenFilters = () => {
    onOpen();
  };

  function handleClearFilters() {
    reset();

    setAppliedFilters({});

    setCurrentPage(1);
    setRowsPerPage(20);

    refetch();
    onClose();
  }

  const { register, handleSubmit, reset, watch, setValue } =
    useForm<AbandonedCartsFilterFormValues>({
      defaultValues: {
        currentPage: 1,
        rowsPerPage: 20,
        searchQuery: '',
        selectedStatuses: [],
        dateRange: [null, null],
      },
    });

  return (
    <LoadingScreen isLoading={isLoading && !abandonedCartsData}>
      <Box gap={3} mt={5}>
        <Flex justifyContent="space-between" mt={14} alignItems="flex-start">
          <Heading mt="50px">Carrinhos abandonados</Heading>
          <Flex direction="column" alignItems="flex-end" gap={3} mt="50px">
            <Button
              onClick={handleOpenFilters}
              leftIcon={<FiFilter />}
              colorScheme="blue"
              variant="outline"
            >
              Filtros
            </Button>
          </Flex>
        </Flex>

        <AbandonedCartsFilterPanel
          isOpen={isOpen}
          setAppliedFilters={setAppliedFilters}
          refetch={refetch}
          onClose={onClose}
          currentPage={currentPage}
          rowsPerPage={rowsPerPage}
          handleSubmit={handleSubmit}
          register={register}
          watch={watch}
          setValue={setValue}
          handleClearFilters={handleClearFilters}
        />

        <Divider orientation="horizontal" mt={10} />

        <AbandonedCartsTable
          watch={watch}
          setValue={setValue}
          setAppliedFilters={setAppliedFilters}
          refetch={refetch}
          abandonedCartsData={abandonedCartsData!}
        />

        <Pagination
          initialPage={currentPage}
          onChangePage={(page) => setCurrentPage(page)}
          rowsPerPage={rowsPerPage}
          totalRows={abandonedCartsData?.meta.totalItems!}
          onChangeRowsPerPage={(r) => setRowsPerPage(r)}
          itemsLabel="clientes"
        />
      </Box>
    </LoadingScreen>
  );
}

export default AbandonedCartsSection;
