import {
  ArrowDownIcon,
  ArrowUpIcon,
  EmailIcon,
  RepeatIcon,
  TimeIcon,
  WarningIcon,
} from '@chakra-ui/icons';
import {
  Badge,
  IconButton,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { AbandonedCartResponse } from '../../../../../services/abandoned-carts.service';
import { AbandonedCartsFilterFormValues } from './AbandonedCartsFilterPanel';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';

function AbandonedCartsTable({
  watch,
  setValue,
  setAppliedFilters,
  refetch,
  abandonedCartsData,
}: {
  watch: UseFormWatch<AbandonedCartsFilterFormValues>;
  setValue: UseFormSetValue<AbandonedCartsFilterFormValues>;
  setAppliedFilters: (filters: object) => void;
  refetch: () => void;
  abandonedCartsData: AbandonedCartResponse;
}) {
  const { t } = useTranslation();

  const statusPresentationMap: Record<
    string,
    { colorScheme: string; icon: JSX.Element }
  > = {
    abandoned: { colorScheme: 'yellow', icon: <TimeIcon boxSize={4} mr={2} /> },
    whatsapp_sent: {
      colorScheme: 'green',
      icon: <EmailIcon boxSize={4} mr={2} />,
    },
    flow_triggered: {
      colorScheme: 'blue',
      icon: <RepeatIcon boxSize={4} mr={2} />,
    },
    failed: { colorScheme: 'red', icon: <WarningIcon boxSize={4} mr={2} /> },
  };

  return (
    <TableContainer overflowX="visible">
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>
              Criado em
              <IconButton
                size="xs"
                variant="ghost"
                aria-label="Ordenar por criado em"
                icon={
                  watch('createdAtOrder') === 'asc' ? (
                    <ArrowUpIcon />
                  ) : (
                    <ArrowDownIcon />
                  )
                }
                onClick={() => {
                  const orderBy =
                    watch('createdAtOrder') === 'asc' ? 'desc' : 'asc';

                  setValue('createdAtOrder', orderBy);

                  setAppliedFilters((prev: AbandonedCartsFilterFormValues) => ({
                    ...prev,
                    createdAtOrder: orderBy,
                  }));
                  refetch();
                }}
                ml={2}
              />
            </Th>
            <Th>Nome</Th>
            <Th>Telefone</Th>
            <Th>Email</Th>
            <Th>Status</Th>
            <Th>Mensagem de Erro</Th>
          </Tr>
        </Thead>
        <Tbody>
          {abandonedCartsData?.data?.map((cart) => {
            const { colorScheme, icon } = statusPresentationMap[
              cart.status
            ] || {
              colorScheme: 'gray',
              icon: null,
            };
            return (
              <Tr key={cart.id}>
                <Td>
                  {new Date(cart.sourceCreatedAt).toLocaleDateString('pt-BR')}
                </Td>
                <Td>{cart.customerName}</Td>
                <Td>{cart.phoneNumberId}</Td>
                <Td>{cart.customerEmail}</Td>
                <Td>
                  <Badge pb="0.5" colorScheme={colorScheme}>
                    {icon}
                    {t(`enums.AbandonedCartStatus.${cart.status}`)}
                  </Badge>
                </Td>
                <Td>{cart.errorMessage}</Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </TableContainer>
  );
}

export default AbandonedCartsTable;
