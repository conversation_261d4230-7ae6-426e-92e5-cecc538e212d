import { CloseIcon, SearchIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Checkbox,
  CheckboxGroup,
  Flex,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  Slide,
  useOutsideClick,
  VStack,
  Text,
} from '@chakra-ui/react';
import React from 'react';
import ReactDatePicker from 'react-datepicker';
import pt from 'date-fns/locale/pt';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';

export type AbandonedCartsFilterFormValues = {
  currentPage: number;
  rowsPerPage: number;
  searchQuery: string;
  selectedStatuses: string[];
  dateRange: (Date | null)[];
  createdAtOrder: 'asc' | 'desc' | null;
};

interface AbandonedCartsFilterPanelProps {
  isOpen: boolean;
  setAppliedFilters: (filters: object) => void;
  refetch: () => void;
  onClose: () => void;
  currentPage: number;
  rowsPerPage: number;
  handleSubmit: UseFormHandleSubmit<AbandonedCartsFilterFormValues>;
  register: UseFormRegister<AbandonedCartsFilterFormValues>;
  watch: UseFormWatch<AbandonedCartsFilterFormValues>;
  setValue: UseFormSetValue<AbandonedCartsFilterFormValues>;
  handleClearFilters: () => void;
}

function AbandonedCartsFilterPanel({
  isOpen,
  setAppliedFilters,
  refetch,
  onClose,
  currentPage,
  rowsPerPage,
  handleSubmit,
  register,
  watch,
  setValue,
  handleClearFilters,
}: AbandonedCartsFilterPanelProps) {
  const onSubmit = (formData: AbandonedCartsFilterFormValues) => {
    const [startDate, endDate] = formData.dateRange || [];

    const filters = {
      searchQuery: formData.searchQuery,
      status: formData.selectedStatuses.join(','),
      startDate,
      endDate,
      createdAtOrder: formData.createdAtOrder,
      perPage: rowsPerPage,
      page: currentPage,
    };

    setAppliedFilters(filters);

    refetch();
    onClose();
  };

  const panelRef = React.useRef<HTMLDivElement>(null);
  useOutsideClick({ ref: panelRef, handler: onClose });

  return (
    <Slide direction="right" in={isOpen} style={{ zIndex: 9999 }}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          ref={panelRef}
          position="fixed"
          top="50%"
          right="0"
          transform="translateY(-50%)"
          width="300px"
          bg="white"
          boxShadow="lg"
          borderRadius="md"
          marginRight={'20px'}
          p={4}
        >
          <IconButton
            aria-label="Fechar painel"
            icon={<CloseIcon />}
            size="sm"
            variant="ghost"
            onClick={onClose}
            mb={2}
          />
          <Text fontWeight="medium" fontSize="lg" mb={4}>
            Filtros
          </Text>

          <Text fontWeight="medium" fontSize="sm" mb={2}>
            Buscar
          </Text>
          <InputGroup mb={4}>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="gray.300" />
            </InputLeftElement>
            <Input
              {...register('searchQuery')}
              placeholder="Nome, e‑mail ou telefone..."
              value={watch('searchQuery')}
              onChange={(e) => setValue('searchQuery', e.target.value)}
            />
          </InputGroup>

          <Text fontWeight="medium" fontSize="sm" mb={2}>
            Status
          </Text>
          <CheckboxGroup
            colorScheme="blue"
            value={watch('selectedStatuses')}
            onChange={(values: string[]) =>
              setValue('selectedStatuses', values)
            }
          >
            <VStack align="stretch" spacing={1} mb={4}>
              <Checkbox
                {...register('selectedStatuses')}
                value="flow_triggered"
              >
                Fluxo Acionado
              </Checkbox>
              <Checkbox {...register('selectedStatuses')} value="abandoned">
                Abandonado
              </Checkbox>
              <Checkbox {...register('selectedStatuses')} value="whatsapp_sent">
                WhatsApp Enviado
              </Checkbox>
              <Checkbox {...register('selectedStatuses')} value="failed">
                Falhou
              </Checkbox>
            </VStack>
          </CheckboxGroup>

          <Text fontWeight="medium" fontSize="sm" mb={2}>
            Período
          </Text>
          <ReactDatePicker
            selected={watch('dateRange')[0]}
            startDate={watch('dateRange')[0]}
            endDate={watch('dateRange')[1]}
            onChange={(dates: Date[]) => setValue('dateRange', dates)}
            selectsRange
            inline
            locale={pt}
            calendarClassName="chakra-datepicker"
          />

          <Flex mt={4} justify="flex-end" gap={2}>
            <Button variant="ghost" size="sm" onClick={handleClearFilters}>
              Limpar filtros
            </Button>
            <Button size="sm" bg="black" color="white" type="submit">
              Aplicar Filtros
            </Button>
          </Flex>
        </Box>
      </form>
    </Slide>
  );
}

export default AbandonedCartsFilterPanel;
