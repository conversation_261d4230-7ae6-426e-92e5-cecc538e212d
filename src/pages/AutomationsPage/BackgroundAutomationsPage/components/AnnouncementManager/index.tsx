import { useMutation, useQuery } from 'react-query';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalCloseButton,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';

import { AnnouncementsService } from '../../../../../services/announcements.service';
import { useSelector } from 'react-redux';
import { isAuthenticated as isAuthenticatedSelector } from '../../../../../state/authSlice';

function AnnouncementManager() {
  const isAuthenticated = useSelector(isAuthenticatedSelector);
  const [closedIds, setClosedIds] = useState<string[]>([]);
  const [iframeHeights, setIframeHeights] = useState<{ [key: string]: number }>(
    {},
  );
  const [iframeWidths, setIframeWidths] = useState<{ [key: string]: number }>(
    {},
  );

  const {
    data: announcements,
    isLoading,
    isError,
  } = useQuery(
    ['announcements', { isAuthenticated }],
    async () => {
      const data = await AnnouncementsService.listAnnouncements({
        viewed: false,
      });
      return data.data;
    },
    {
      enabled: !!isAuthenticated,
    },
  );

  const updateUserAnnouncementView = useMutation({
    mutationFn: AnnouncementsService.updateUserAnnouncementView,
  });

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (!event.data.type || !event.data.id) return;

      if (event.data.type === 'iframeHeight') {
        setIframeHeights((prevHeights) => ({
          ...prevHeights,
          [event.data.id]: event.data.height,
        }));
      }

      if (event.data.type === 'iframeWidth') {
        setIframeWidths((prevWidths) => ({
          ...prevWidths,
          [event.data.id]: event.data.width,
        }));
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  if (isLoading || isError || !announcements || announcements.length === 0) {
    return null;
  }

  return (
    <>
      {announcements.map((announcement) => {
        const isOpen = !closedIds.includes(announcement.id);
        const iframeHeight = iframeHeights[announcement.id];
        const iframeWidth = iframeWidths[announcement.id];

        const adjustedWidth = Math.max(iframeWidth || 400, 600);

        const modifiedHtml = `
          <!DOCTYPE html>
          <html>
          <head>
          </head>
          <body>
            ${announcement.html}
            <script>
              window.onload = function() {
                const height = document.documentElement.scrollHeight;
                const width = Math.max(document.documentElement.scrollWidth, 600); // Ensure minimum width
                window.parent.postMessage({ type: 'iframeHeight', id: '${announcement.id}', height: height }, '*');
                window.parent.postMessage({ type: 'iframeWidth', id: '${announcement.id}', width: width }, '*');
              };
            </script>
          </body>
          </html>
        `;

        return (
          <Modal
            key={announcement.id}
            isOpen={isOpen}
            onClose={() => {
              setClosedIds((prev) => [...prev, announcement.id]);
              updateUserAnnouncementView.mutate(announcement.id);
            }}
            isCentered
            size="xl"
          >
            <ModalOverlay />
            <ModalContent
              padding="30px"
              shadow="none"
              background="transparent"
              position="relative"
              maxWidth="60vw"
              display="flex"
              justifyContent="center"
              alignItems="center"
              width="fit-content"
              margin="auto"
            >
              <ModalCloseButton
                top="15px"
                right="20px"
                zIndex="sticky"
                borderRadius="full"
                bg="red.500"
                color="white"
                _hover={{ bg: 'red.600' }}
                size="sm"
              />

              <iframe
                srcDoc={modifiedHtml}
                title="Announcement Content"
                sandbox="allow-same-origin allow-scripts"
                style={{
                  border: 'none',
                  height: `${iframeHeight}px`,
                  width: `${adjustedWidth}px`,
                  overflow: 'hidden',
                  backgroundColor: 'transparent',
                  borderRadius: '10px',
                  display: 'block',
                  margin: '0 auto',
                }}
                allowTransparency={true}
              />
            </ModalContent>
          </Modal>
        );
      })}
    </>
  );
}

export default AnnouncementManager;
