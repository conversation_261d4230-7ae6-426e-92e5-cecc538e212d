import { SourceIntegration } from '../../../../types/SourceIntegrationEnum';
import { CashbackPlatform } from '../types/CashbackPlataform';

export const platforms: CashbackPlatform[] = [
  {
    id: SourceIntegration.tray,
    name: 'TRAY',
    description: 'Cashback via criação de cupons diretamente na plataforma.',
    isSupported: true,
    supportsUsageRestrictions: true,
  },
  {
    id: SourceIntegration.varejo_online,
    name: 'Varejo Online',
    description: 'Cashback gerado por meio de vouchers exclusivos.',
    isSupported: true,
    supportsUsageRestrictions: false,
  },
  {
    id: SourceIntegration.shopify_ecommerce,
    name: 'Shopify',
    description: 'Cashback usando regras de preço e cupons do Shopify.',
    isSupported: true,
    supportsUsageRestrictions: true,
  },
];
