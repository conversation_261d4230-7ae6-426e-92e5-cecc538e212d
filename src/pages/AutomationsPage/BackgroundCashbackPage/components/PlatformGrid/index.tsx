import { SimpleGrid } from '@chakra-ui/react';
import { CashbackPlatform } from '../../types/CashbackPlataform';
import CardAction from '../../../../../components/CardAction';
import { SourceIntegrationLabelsRoutesAndImages } from '../../../../../types/source-integration-labels-routes-and-images';

interface PlatformGrid {
  platforms: CashbackPlatform[];
  onSelect: (platform: CashbackPlatform) => void;
}

const PlatformGrid = ({ platforms, onSelect }: PlatformGrid) => {
  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} mt={4}>
      {platforms.map((platform) => {
        const sourceIntegration =
          SourceIntegrationLabelsRoutesAndImages[platform.id ?? 'unknown'];

        return (
          <CardAction
            key={platform.id}
            title={platform.name}
            subtitle={platform.description}
            imgSource={`${process.env.PUBLIC_URL}/${sourceIntegration?.image}`}
            onPrimaryAction={() => onSelect(platform)}
            primaryActionName="Configurar Cashback"
          />
        );
      })}
    </SimpleGrid>
  );
};

export default PlatformGrid;
