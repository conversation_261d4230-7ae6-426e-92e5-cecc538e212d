import { CloseIcon, SearchIcon } from '@chakra-ui/icons';
import {
  useOutsideClick,
  Slide,
  Text,
  Box,
  Flex,
  IconButton,
  InputGroup,
  InputLeftElement,
  Input,
  CheckboxGroup,
  VStack,
  Checkbox,
  Button,
  Radio,
  RadioGroup,
  Stack,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CouponStep } from '../../../../../../types/CouponStepEnum';

interface FiltersPanelProps {
  isOpen: boolean;
  onClose: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedSteps: CouponStep[];
  setSelectedSteps: (steps: CouponStep[]) => void;
  isActive?: boolean;
  setIsActive: (active: boolean | undefined) => void;
  isUsed?: boolean;
  setIsUsed: (used: boolean | undefined) => void;
  onClearFilters: () => void;
  onApplyFilters: () => void;
}

const stepEnumValues = [
  CouponStep.pending,
  CouponStep.creation_whatsapp_sent,
  CouponStep.reminder_whatsapp_sent,
  CouponStep.last_day_whatsapp_sent,
];

const FiltersPanel = ({
  isOpen,
  onClose,
  searchTerm,
  setSearchTerm,
  selectedSteps,
  setSelectedSteps,
  isActive,
  setIsActive,
  isUsed,
  setIsUsed,
  onClearFilters,
  onApplyFilters,
}: FiltersPanelProps) => {
  const { t } = useTranslation();
  const [preSearchTerm, setPreSearchTerm] = useState(searchTerm);
  const [preSelectedSteps, setPreSelectedSteps] = useState(selectedSteps);
  const [preIsActive, setPreIsActive] = useState<string>(
    isActive === undefined ? '' : isActive ? 'true' : 'false',
  );
  const [preIsUsed, setPreIsUsed] = useState<string>(
    isUsed === undefined ? '' : isUsed ? 'true' : 'false',
  );

  const panelRef = React.useRef<HTMLDivElement>(null);
  useOutsideClick({ ref: panelRef, handler: onClose });

  const getStepOptions = () => {
    return stepEnumValues.map((value) => ({
      value,
      label: t(`enums.couponStep.${value}`, value),
    }));
  };

  const handleApplyFilters = () => {
    setSearchTerm(preSearchTerm);
    setSelectedSteps(preSelectedSteps);
    setIsActive(preIsActive === '' ? undefined : preIsActive === 'true');
    setIsUsed(preIsUsed === '' ? undefined : preIsUsed === 'true');
    onApplyFilters();
  };

  const handleClearFilters = () => {
    setPreSearchTerm('');
    setPreSelectedSteps([]);
    setPreIsActive('');
    setPreIsUsed('');
    onClearFilters();
  };

  React.useEffect(() => {
    if (isOpen) {
      setPreSearchTerm(searchTerm);
      setPreSelectedSteps(selectedSteps);
      setPreIsActive(isActive === undefined ? '' : isActive ? 'true' : 'false');
      setPreIsUsed(isUsed === undefined ? '' : isUsed ? 'true' : 'false');
    }
  }, [isOpen, searchTerm, selectedSteps, isActive, isUsed]);

  const stepOptions = getStepOptions();

  return (
    <Slide direction="right" in={isOpen} style={{ zIndex: 9999 }}>
      <Box
        ref={panelRef}
        position="fixed"
        top="50%"
        right="0"
        transform="translateY(-50%)"
        width="320px"
        bg="white"
        boxShadow="lg"
        borderRadius="md"
        marginRight="20px"
        p={4}
      >
        <Flex justifyContent="space-between" alignItems="center" mb={4}>
          <Text fontWeight="medium" fontSize="lg">
            Filtros da Tabela
          </Text>
          <IconButton
            aria-label="Fechar painel"
            icon={<CloseIcon />}
            size="sm"
            variant="ghost"
            onClick={onClose}
          />
        </Flex>

        <Text fontWeight="medium" fontSize="sm" mb={2}>
          Buscar
        </Text>
        <InputGroup mb={4}>
          <InputLeftElement pointerEvents="none">
            <SearchIcon color="gray.300" />
          </InputLeftElement>
          <Input
            placeholder="Nome, código ou telefone..."
            value={preSearchTerm}
            onChange={(e) => setPreSearchTerm(e.target.value)}
          />
        </InputGroup>

        <Text fontWeight="medium" fontSize="sm" mb={2}>
          Estágio do Cupom
        </Text>
        <CheckboxGroup
          colorScheme="blue"
          value={preSelectedSteps}
          onChange={(values: CouponStep[]) => setPreSelectedSteps(values)}
        >
          <VStack align="stretch" spacing={1} mb={4}>
            {stepOptions.map(({ value, label }) => (
              <Checkbox key={value} value={value}>
                {label}
              </Checkbox>
            ))}
          </VStack>
        </CheckboxGroup>

        <Text fontWeight="medium" fontSize="sm" mb={2}>
          Status Ativo
        </Text>
        <RadioGroup
          colorScheme="blue"
          value={preIsActive}
          onChange={setPreIsActive}
          mb={4}
        >
          <Stack spacing={2}>
            <Radio value="">Todos</Radio>
            <Radio value="true">Ativo</Radio>
            <Radio value="false">Inativo</Radio>
          </Stack>
        </RadioGroup>

        <Text fontWeight="medium" fontSize="sm" mb={2}>
          Status Usado
        </Text>
        <RadioGroup
          colorScheme="blue"
          value={preIsUsed}
          onChange={setPreIsUsed}
          mb={4}
        >
          <Stack spacing={2}>
            <Radio value="">Todos</Radio>
            <Radio value="true">Usado</Radio>
            <Radio value="false">Não usado</Radio>
          </Stack>
        </RadioGroup>

        <Flex mt={4} justify="flex-end" gap={2}>
          <Button variant="ghost" size="sm" onClick={handleClearFilters}>
            Limpar filtros
          </Button>
          <Button
            size="sm"
            bg="black"
            color="white"
            onClick={handleApplyFilters}
          >
            Aplicar Filtros
          </Button>
        </Flex>
      </Box>
    </Slide>
  );
};

export default FiltersPanel;
