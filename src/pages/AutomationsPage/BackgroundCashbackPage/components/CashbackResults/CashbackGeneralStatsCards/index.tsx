import { BsTicketPerforated, BsCheck2Circle } from 'react-icons/bs';
import { colors } from '../../../../../../constants/colors';
import { apiRoutes } from '../../../../../../constants/api-routes';
import { SourceIntegration } from '../../../../../../types/Prisma';
import { format } from 'date-fns';
import CardStatisticContainer from '../../../../../../components/CardStatisticContainer';
import { BiTransfer } from 'react-icons/bi';
import { FaMoneyBillWave } from 'react-icons/fa';
import { MoneyUtils } from '../../../../../../utils/money.utils';

interface CashbackGeneralStatsCardsDataProps {
  source: SourceIntegration;
  startDate: Date;
  endDate: Date;
}

export const CashbackGeneralStatsCardsData = ({
  source,
  startDate,
  endDate,
}: CashbackGeneralStatsCardsDataProps) => {
  const startDateStr = format(startDate, 'yyyy-MM-dd');
  const endDateStr = format(endDate, 'yyyy-MM-dd');

  const cardsData = [
    {
      icon: <BsTicketPerforated />,
      title: 'Cupons Gerados',
      requestRoute: apiRoutes.getTotalCouponsGeneratedByCashbackAutomation(
        source,
        startDateStr,
        endDateStr,
      ),
      bgIconColor: colors.primaryLight,
      tooltip: 'Total de cupons de cashback gerados no período selecionado',
    },
    {
      icon: <BsCheck2Circle />,
      title: 'Pedidos Convertidos',
      requestRoute: apiRoutes.getTotalOrdersConvertedByCashbackAutomation(
        source,
        startDateStr,
        endDateStr,
      ),
      bgIconColor: colors.green,
      tooltip:
        'Número de pedidos que foram convertidos através dos cupons de cashback',
    },
    {
      icon: <BiTransfer />,
      title: 'Conversão de Cupons',
      bgIconColor: colors.orange,
      tooltip: 'Taxa de conversão dos cupons de cashback em vendas efetivas',
      valueFormatter: (value: any) => {
        const numValue = Number(value);
        return isNaN(numValue) ? '0.00%' : `${numValue.toFixed(2)}%`;
      },
      value: (data: any) => {
        if (!data || typeof data !== 'object') return 0;

        const cuponsGerados = Number(data.cuponsGerados) || 0;
        const pedidosConvertidos = Number(data.pedidosConvertidos) || 0;

        if (cuponsGerados === 0) return 0;

        return (pedidosConvertidos / cuponsGerados) * 100;
      },
    },
    {
      icon: <FaMoneyBillWave />,
      title: 'Receita Total',
      requestRoute: apiRoutes.getTotalRevenueByCashbackAutomation(
        source,
        startDateStr,
        endDateStr,
      ),
      bgIconColor: colors.red,
      tooltip:
        'Valor total em vendas geradas através das campanhas de cashback',
      valueFormatter: (value: number) => MoneyUtils.formatCurrency(value),
    },
  ];

  return (
    <>
      {cardsData.map(
        ({
          icon,
          requestRoute,
          title,
          bgIconColor,
          valueFormatter,
          tooltip,
          value,
        }) => (
          <CardStatisticContainer
            key={`${title}-${startDate}-${endDate}`}
            icon={icon}
            title={title}
            requestRoute={requestRoute}
            valueFormatter={valueFormatter}
            bgIconColor={bgIconColor}
            tooltip={tooltip}
            value={value}
          />
        ),
      )}
    </>
  );
};
