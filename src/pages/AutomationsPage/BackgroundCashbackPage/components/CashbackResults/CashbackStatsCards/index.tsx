import { Flex } from '@chakra-ui/react';
import { BsPeopleFill, BsFillEyeFill } from 'react-icons/bs';
import { FaBox, FaMoneyBillWave } from 'react-icons/fa';
import { HiCursorClick } from 'react-icons/hi';
import { MoneyUtils } from '../../../../../../utils/money.utils';
import { colors } from '../../../../../../constants/colors';
import CardStatistic from '../../../../../../components/CardStatistic';

export interface CashbackSalesData {
  automationCost: number;
  startDate: string;
  endDate: string;
  received: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  read: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  engaged: {
    totalOrders: number;
    totalOrdersValue: number;
    roi: number;
    count: number;
  };
  totalMessagesSent: number;
  totalOrders: number;
}

interface CashbackStatsCardsProps {
  data: CashbackSalesData;
}

const CashbackStatsCards = ({ data }: CashbackStatsCardsProps) => {
  const statsConfig = [
    {
      icon: <BsPeopleFill />,
      title: 'Disparos',
      value: data.totalMessagesSent,
      bgIconColor: colors.secondary,
      tooltip: 'Quantidade total de mensagens enviadas',
    },
    {
      icon: <BsFillEyeFill />,
      title: 'Lidas',
      value: data.read.count,
      bgIconColor: colors.primaryLight,
      tooltip: 'Quantidade de mensagens lidas',
    },
    {
      icon: <HiCursorClick />,
      title: 'Responderam',
      value: data.engaged.count,
      bgIconColor: colors.green,
      tooltip: 'Quantidade de clientes que responderam',
    },
    {
      icon: <FaBox />,
      title: 'Pedidos',
      value: data.totalOrders,
      bgIconColor: colors.purple,
      tooltip: 'Quantidade de pedidos convertidos',
    },
    {
      icon: <FaMoneyBillWave />,
      title: 'Custo',
      value: MoneyUtils.formatCurrency(data.automationCost),
      bgIconColor: colors.red,
      tooltip: 'Custo total de envio dos cupons',
    },
  ];

  return (
    <Flex justifyContent="space-between" gap={3} mb={8}>
      {statsConfig.map(({ icon, title, value, bgIconColor, tooltip }) => (
        <CardStatistic
          value={value}
          key={title}
          icon={icon}
          title={title}
          bgIconColor={bgIconColor}
          tooltip={tooltip}
        />
      ))}
    </Flex>
  );
};

export default CashbackStatsCards;
