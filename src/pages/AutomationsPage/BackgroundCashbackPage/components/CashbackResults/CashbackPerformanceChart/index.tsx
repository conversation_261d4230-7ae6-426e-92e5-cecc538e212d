import { Box, Heading } from '@chakra-ui/react';
import CustomECharts from '../../../../../../components/CustomECharts';
import { colors } from '../../../../../../constants/colors';

interface CashbackPerformanceChartProps {
  data: Array<{
    datetime: string;
    messages_count: number;
    coupons_used: number;
  }>;
}

const CashbackPerformanceChart = ({ data }: CashbackPerformanceChartProps) => {
  return (
    <Box mb={8}>
      <Heading size="md" mb={4}>
        Conversões no período
      </Heading>
      <CustomECharts
        chartWidth="100%"
        chartHeight="450px"
        marginTop="20px"
        option={{
          legend: {},
          dataZoom: [
            {
              type: 'slider',
              start: 0,
              end: 50,
            },
          ],
          tooltip: {},
          color: [colors.primaryLight, colors.green],
          dataset: {
            dimensions: ['datetime', 'messages_count', 'orders_count'],
            source: data,
          },
          xAxis: {
            type: 'category',
          },
          yAxis: [
            {
              name: 'Quantidade',
              nameLocation: 'middle',
              splitLine: { show: false },
              nameGap: 55,
            },
          ],
          series: [
            { type: 'line', name: 'Mensagens enviadas', yAxisIndex: 0 },
            {
              type: 'line',
              name: 'Mensagens convertidas em uso do cupom',
              yAxisIndex: 0,
            },
          ],
        }}
      />
    </Box>
  );
};

export default CashbackPerformanceChart;
