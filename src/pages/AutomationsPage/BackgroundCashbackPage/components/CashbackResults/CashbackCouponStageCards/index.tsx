import React from 'react';
import {
  BsTicketPerforated,
  BsCheck2Circle,
  BsBell,
  BsExclamationTriangle,
} from 'react-icons/bs';
import { colors } from '../../../../../../constants/colors';
import { apiRoutes } from '../../../../../../constants/api-routes';
import { SourceIntegration } from '../../../../../../types/Prisma';
import { CouponStep } from '../../../../../../types/CouponStepEnum';
import { format } from 'date-fns';
import CardStatisticContainer from '../../../../../../components/CardStatisticContainer';

interface CashbackCouponStageCardsProps {
  source: SourceIntegration;
  startDate: Date;
  endDate: Date;
}

export const CashbackCouponStageCards = ({
  source,
  startDate,
  endDate,
}: CashbackCouponStageCardsProps) => {
  const startDateStr = format(startDate, 'yyyy-MM-dd');
  const endDateStr = format(endDate, 'yyyy-MM-dd');

  const cardsData = [
    {
      icon: <BsTicketPerforated />,
      title: 'Cupons Pendentes',
      requestRoute: apiRoutes.getCashbackCouponsCountByStage(
        source,
        startDateStr,
        endDateStr,
      ),
      dataKey: CouponStep.pending,
      bgIconColor: colors.blueTurquose,
      tooltip:
        'Cupons que foram gerados mas ainda não foram enviados aos clientes',
    },
    {
      icon: <BsCheck2Circle />,
      title: 'Cupons Criados',
      requestRoute: apiRoutes.getCashbackCouponsCountByStage(
        source,
        startDateStr,
        endDateStr,
      ),
      dataKey: CouponStep.creation_whatsapp_sent,
      bgIconColor: colors.green,
      tooltip:
        'Cupons que foram criados e estão disponíveis para uso pelos clientes',
    },
    {
      icon: <BsBell />,
      title: 'Lembretes Enviados',
      requestRoute: apiRoutes.getCashbackCouponsCountByStage(
        source,
        startDateStr,
        endDateStr,
      ),
      dataKey: CouponStep.reminder_whatsapp_sent,
      bgIconColor: colors.secondary,
      tooltip:
        'Cupons para os quais foram enviados lembretes de uso antes do vencimento',
    },
    {
      icon: <BsExclamationTriangle />,
      title: 'Último Dia',
      requestRoute: apiRoutes.getCashbackCouponsCountByStage(
        source,
        startDateStr,
        endDateStr,
      ),
      dataKey: CouponStep.last_day_whatsapp_sent,
      bgIconColor: colors.purple,
      tooltip: 'Cupons que estão no último dia antes do vencimento',
    },
  ];

  return (
    <>
      {cardsData.map(
        ({ icon, requestRoute, title, bgIconColor, tooltip, dataKey }) => (
          <CardStatisticContainer
            key={`${title}-${startDateStr}-${endDateStr}`}
            icon={icon}
            title={title}
            requestRoute={requestRoute}
            bgIconColor={bgIconColor}
            tooltip={tooltip}
            dataKey={dataKey}
          />
        ),
      )}
    </>
  );
};
