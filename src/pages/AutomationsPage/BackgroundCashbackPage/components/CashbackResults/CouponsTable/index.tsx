import { TimeIcon, WarningIcon } from '@chakra-ui/icons';
import {
  Badge,
  Text,
  Box,
  Link,
  Switch,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import { BsWhatsapp, BsTicketPerforated } from 'react-icons/bs';
import { MoneyUtils } from '../../../../../../utils/money.utils';
import { useCallback } from 'react';
import { useMutation } from 'react-query';
import { useTranslation } from 'react-i18next';
import { appPaths } from '../../../../../../constants/app-paths';
import {
  CreateConversationDto,
  ConversationsService,
} from '../../../../../../services/conversations.service';
import { ConversationWithIncludes } from '../../../../../../types/Conversation';
import { useNavigate } from 'react-router-dom';
import { CouponData } from '../../../../../../types/CouponData';
import { CouponStep } from '../../../../../../types/CouponStepEnum';

interface CouponsTableProps {
  coupons: CouponData[];
}

// Array de strings para tradução dos status dos cupons
const couponStepTranslationKeys = [
  'couponStep.pending',
  'couponStep.creation_whatsapp_sent',
  'couponStep.reminder_whatsapp_sent',
  'couponStep.last_day_whatsapp_sent',
] as const;

const CouponsTable = ({ coupons }: CouponsTableProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const couponStatusColorMap = {
    pending: { colorScheme: 'yellow', icon: <TimeIcon boxSize={3} /> },
    creation_whatsapp_sent: {
      colorScheme: 'blue',
      icon: <BsWhatsapp size={14} />,
    },
    reminder_whatsapp_sent: {
      colorScheme: 'green',
      icon: <BsTicketPerforated size={14} />,
    },
    last_day_whatsapp_sent: {
      colorScheme: 'red',
      icon: <WarningIcon boxSize={3} />,
    },
  } as const;

  const createConversation = useMutation(
    (createConversationDto: CreateConversationDto) =>
      ConversationsService.createConverstation(createConversationDto),
    {
      onSuccess: (res) => {
        const createdConversation: ConversationWithIncludes = res.data;
        navigate({
          pathname: appPaths.conversations(),
          search: `conversationId=${createdConversation.id}`,
        });
      },
    },
  );

  const handleClickRow = useCallback(
    async (coupon: CouponData) => {
      await createConversation.mutateAsync({
        recipientPhoneNumberId:
          coupon.customerCoupons[0].customer.phoneNumberId,
        recipientName: coupon.customerCoupons[0].customer.name,
      });
    },
    [createConversation],
  );

  const translateCouponStep = (step: CouponStep): string => {
    return t(`enums.couponStep.${step}`, step);
  };

  return (
    <Box>
      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Data de Criação</Th>
              <Th>Nome Cliente</Th>
              <Th>Telefone</Th>
              <Th>Código Cupom</Th>
              <Th>Valor Cupom</Th>
              <Th>Status</Th>
              <Th>Ativo</Th>
              <Th>Usado</Th>
            </Tr>
          </Thead>
          <Tbody>
            {coupons.map((coupon) => {
              const couponStep = coupon.customerCoupons[0]
                .couponStep as CouponStep;
              const { colorScheme, icon } = couponStatusColorMap[
                couponStep
              ] || {
                colorScheme: 'gray',
                icon: null,
              };

              return (
                <Tr key={coupon.id}>
                  <Td>
                    {new Date(coupon.createdAt).toLocaleDateString('pt-BR')}
                  </Td>
                  <Td fontWeight="medium">
                    <Link
                      onClick={() => handleClickRow(coupon)}
                      noOfLines={1}
                      isTruncated
                      title={coupon.customerCoupons[0].customer.name}
                      color="black"
                      fontWeight="semibold"
                    >
                      {coupon.customerCoupons[0].customer.name}
                    </Link>
                  </Td>
                  <Td>{coupon.customerCoupons[0].customer.phoneNumberId}</Td>
                  <Td>
                    <Text fontFamily="mono" fontWeight="bold">
                      {coupon.code}
                    </Text>
                  </Td>
                  <Td fontWeight="medium">
                    {coupon.discountType === 'percentage'
                      ? `${coupon.discountValue}%`
                      : MoneyUtils.formatCurrency(coupon.discountValue)}
                  </Td>
                  <Td>
                    <Badge
                      colorScheme={colorScheme}
                      display="flex"
                      alignItems="center"
                      w="fit-content"
                      gap={1}
                    >
                      {icon}
                      <Text>
                        {translateCouponStep(
                          coupon.customerCoupons[0].couponStep,
                        )}
                      </Text>
                    </Badge>
                  </Td>
                  <Td>
                    <Switch
                      isChecked={coupon.isActive}
                      colorScheme="green"
                      isDisabled
                    />
                  </Td>
                  <Td>
                    <Switch
                      isChecked={coupon.customerCoupons[0].isUsed}
                      colorScheme="green"
                      isDisabled
                    />
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default CouponsTable;
