import React, { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Button,
  Divider,
  useToast,
  SimpleGrid,
  useDisclosure,
} from '@chakra-ui/react';
import { FiFilter } from 'react-icons/fi';
import ReactDatePicker from 'react-datepicker';
import { format, startOfMonth, endOfMonth, addDays } from 'date-fns';
import { useQuery } from 'react-query';
import { CashbackPlatform } from '../../types/CashbackPlataform';
import Pagination from '../../../../../components/Pagination';
import LoadingScreen from '../../../../../components/LoadingScreen';
import CouponsTable from './CouponsTable';
import FiltersPanel from './FiltersPanel';
import CashbackStatsCards from './CashbackStatsCards';
import CashbackPerformanceChart from './CashbackPerformanceChart';
import { StatisticsService } from '../../../../../services/statistics.service';
import { CouponsService } from '../../../../../services/coupons.service';
import { CouponStep } from '../../../../../types/CouponStepEnum';
import { apiRoutes } from '../../../../../constants/api-routes';
import { CashbackCouponStageCards } from './CashbackCouponStageCards';
import { CashbackGeneralStatsCardsData } from './CashbackGeneralStatsCards';

interface CashbackResultsProps {
  integration: CashbackPlatform;
}

const CashbackResults: React.FC<CashbackResultsProps> = ({ integration }) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);

  const [startDate, setStartDate] = useState<Date>(startOfMonth(new Date()));
  const [endDate, setEndDate] = useState<Date>(endOfMonth(new Date()));

  const [tableSearchTerm, setTableSearchTerm] = useState<string>('');
  const [tableSelectedSteps, setTableSelectedSteps] = useState<CouponStep[]>(
    [],
  );
  const [tableIsActive, setTableIsActive] = useState<boolean | undefined>(
    undefined,
  );
  const [tableIsUsed, setTableIsUsed] = useState<boolean | undefined>(
    undefined,
  );

  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const {
    data: cashbackPerformanceData = [],
    isLoading: isLoadingCashbackPerformance,
  } = useQuery(
    apiRoutes.getChartCashbackPerformance(integration.id, startDate, endDate),
    async () => {
      const { data } = await StatisticsService.getChartCashbackPerformance(
        integration.id,
        startDate,
        endDate,
      );
      return data;
    },
    {
      keepPreviousData: true,
    },
  );

  const { data: statsData, isLoading: isLoadingStats } = useQuery(
    apiRoutes.getChartCashbackSales(integration.id, startDate, endDate),
    async () => {
      const { data } = await StatisticsService.getChartCashbackSales(
        integration.id,
        startDate,
        endDate,
      );
      return data;
    },
    {
      keepPreviousData: true,
    },
  );

  const { data: couponsData, isLoading: isLoadingCoupons } = useQuery(
    apiRoutes.listCashbackCoupons({
      page: currentPage,
      perPage: rowsPerPage,
      source: integration.id,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? addDays(new Date(endDate), 1) : undefined,
      couponSteps:
        tableSelectedSteps.length > 0
          ? tableSelectedSteps.join(',')
          : undefined,
      isActive: tableIsActive,
      isUsed: tableIsUsed,
      searchQuery: tableSearchTerm || undefined,
    }),
    async () => {
      const { data: paginatedData } = await CouponsService.listCashbackCoupons({
        page: currentPage,
        perPage: rowsPerPage,
        source: integration.id,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? addDays(new Date(endDate), 1) : undefined,
        couponSteps:
          tableSelectedSteps.length > 0 ? tableSelectedSteps : undefined,
        isActive: tableIsActive,
        isUsed: tableIsUsed,
        searchQuery: tableSearchTerm || undefined,
      });
      return paginatedData;
    },
    {
      keepPreviousData: true,
    },
  );

  const handleClearTableFilters = () => {
    setTableSearchTerm('');
    setTableSelectedSteps([]);
    setTableIsActive(undefined);
    setTableIsUsed(undefined);
    setCurrentPage(1);
    onClose();
  };

  const handleApplyTableFilters = () => {
    setCurrentPage(1);
    onClose();

    toast({
      title: 'Filtros aplicados',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  const handleDateChange = (start: Date, end: Date) => {
    setStartDate(start);
    setEndDate(end);
  };

  const isLoading =
    isLoadingCashbackPerformance || isLoadingStats || isLoadingCoupons;

  return (
    <Box p={5}>
      <LoadingScreen isLoading={isLoading}>
        <Flex justifyContent="space-between" alignItems="flex-start" mb={6}>
          <Box>
            <Heading size="lg">
              Resultados de Cashback - {integration.name}
            </Heading>
            <Text color="gray.600">
              Acompanhe o desempenho das campanhas de cashback
            </Text>
          </Box>

          <Flex alignItems={'flex-end'} flexDir="column" gap={2}>
            <Flex gap={10} alignItems="center">
              <Flex gap={2} alignItems="center">
                <Text>Início</Text>
                <ReactDatePicker
                  isClearable
                  selected={startDate}
                  onSelect={(date: Date) => handleDateChange(date, endDate)}
                  onChange={(date: Date) => handleDateChange(date, endDate)}
                  dateFormat="dd/MM/yyyy"
                />
              </Flex>
              <Flex gap={2} alignItems="center">
                <Text>Fim</Text>
                <ReactDatePicker
                  isClearable
                  selected={endDate}
                  onSelect={(date: Date) => handleDateChange(startDate, date)}
                  onChange={(date: Date) => handleDateChange(startDate, date)}
                  dateFormat="dd/MM/yyyy"
                />
              </Flex>
            </Flex>
          </Flex>
        </Flex>

        <Box mb={6}>
          <Heading size="md" mb={4}>
            Estatísticas Gerais
          </Heading>
          <Text fontSize="sm" color="gray.500" mb={3}>
            {format(startDate, 'dd/MM/yyyy')} à {format(endDate, 'dd/MM/yyyy')}
          </Text>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={10} mb={6}>
            <CashbackGeneralStatsCardsData
              source={integration.id}
              startDate={startDate}
              endDate={endDate}
            />
          </SimpleGrid>
        </Box>

        <Box mb={8}>
          <Heading size="md" mb={4}>
            Estágio dos Cupons
          </Heading>
          <Text fontSize="sm" color="gray.500" mb={3}>
            {format(startDate, 'dd/MM/yyyy')} à {format(endDate, 'dd/MM/yyyy')}
          </Text>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={10}>
            <CashbackCouponStageCards
              source={integration.id}
              startDate={startDate}
              endDate={endDate}
            />
          </SimpleGrid>
        </Box>

        <CashbackPerformanceChart data={cashbackPerformanceData} />

        {statsData && <CashbackStatsCards data={statsData} />}

        <Divider mb={6} />

        <Flex justifyContent="space-between" alignItems="center" mb={4}>
          <Heading size="md">Cupons Gerados</Heading>

          <Button
            onClick={onOpen}
            leftIcon={<FiFilter />}
            colorScheme="blue"
            variant="outline"
          >
            Filtros da Tabela
          </Button>
        </Flex>

        <CouponsTable coupons={couponsData?.data || []} />

        <Box mt={6}>
          <Pagination
            initialPage={currentPage}
            onChangePage={(page) => setCurrentPage(page)}
            rowsPerPage={rowsPerPage}
            totalRows={couponsData?.meta.totalItems || 0}
            onChangeRowsPerPage={(r) => setRowsPerPage(r)}
            itemsLabel="cupons"
          />
        </Box>

        <FiltersPanel
          isOpen={isOpen}
          onClose={onClose}
          searchTerm={tableSearchTerm}
          setSearchTerm={setTableSearchTerm}
          selectedSteps={tableSelectedSteps}
          setSelectedSteps={setTableSelectedSteps}
          isActive={tableIsActive}
          setIsActive={setTableIsActive}
          isUsed={tableIsUsed}
          setIsUsed={setTableIsUsed}
          onClearFilters={handleClearTableFilters}
          onApplyFilters={handleApplyTableFilters}
        />
      </LoadingScreen>
    </Box>
  );
};

export default CashbackResults;
