import {
  Box,
  <PERSON>ing,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  FormControl,
  Text,
  Flex,
  Badge,
  Grid,
  GridItem,
  Select,
  FormLabel,
  FormErrorMessage,
  NumberInputField,
  FormHelperText,
  NumberInput,
  Switch,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import WhatsappTemplatePreview from '../../../../../components/WhatsappTemplatePreview';
import { Controller } from 'react-hook-form';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import {
  ListMessageTemplateItem,
  MessageTemplatesService,
} from '../../../../../services/message-templates.service';
import { MessageTemplateTypeEnum } from '../../../../../types/MessageTemplateType';
import MessageTemplateStatusEnum2 from '../../../../../types/MessageTemplateStatusEnum';

interface CashbackTemplatesProps {
  control: any;
  errors: any;
  watch: any;
}

const CashbackTemplates = ({
  control,
  errors,
  watch,
}: CashbackTemplatesProps) => {
  const [activeTab, setActiveTab] = useState(0);
  const [templateArgs, setTemplateArgs] = useState<{
    [key: string]: string | undefined;
  }>({});
  const isReminderEnabled = watch('isReminderEnabled');
  const { data: templates } = useQuery(
    apiRoutes.listMessageTemplates({
      type: MessageTemplateTypeEnum.CASHBACK,
      status: MessageTemplateStatusEnum2.approved,
    }),
    async () => {
      const { data } = await MessageTemplatesService.listMessageTemplates({
        type: MessageTemplateTypeEnum.CASHBACK,
        status: MessageTemplateStatusEnum2.approved,
      });
      return data;
    },
    {
      select(data) {
        return data.filter(
          (template: ListMessageTemplateItem) => template.status === 'approved',
        );
      },
    },
  );

  const watchCreationTemplateId = watch('creationMessageTemplateId');
  const watchReminderTemplateId = watch('reminderMessageTemplateId');
  const watchLastDayTemplateId = watch('lastDayMessageTemplateId');

  const getSelectedTemplateId = () => {
    switch (activeTab) {
      case 0:
        return watchCreationTemplateId;
      case 1:
        return watchReminderTemplateId;
      case 2:
        return watchLastDayTemplateId;
      default:
        return null;
    }
  };

  const selectedTemplateId = getSelectedTemplateId();
  const selectedTemplate = selectedTemplateId
    ? templates?.find((template) => template.id === selectedTemplateId)
    : null;

  const handleTemplateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const templateId = e.target.value;
    switch (activeTab) {
      case 0:
        control.setValue('creationMessageTemplateId', templateId);
        break;
      case 1:
        control.setValue('reminderMessageTemplateId', templateId);
        break;
      case 2:
        control.setValue('lastDayMessageTemplateId', templateId);
        break;
    }

    setTemplateArgs({});
  };

  useEffect(() => {
    setTemplateArgs({});
  }, [activeTab]);

  return (
    <Grid templateColumns="2fr 1fr" justifyContent="space-between">
      <GridItem>
        <Box>
          <Box mb={5}>
            <Heading size="md">Templates de Notificação de Cashback</Heading>
            <Text color="gray.600">
              Configure os templates para cada etapa do cashback
            </Text>
          </Box>

          <Tabs
            variant="enclosed"
            onChange={(index) => setActiveTab(index)}
            index={activeTab}
          >
            <TabList>
              <Tab>Pós Compra</Tab>
              <Tab>Prestes a expirar</Tab>
              <Tab>Último dia</Tab>
            </TabList>

            <TabPanels>
              <TabPanel>
                <Box p={4} bg="gray.50" borderRadius="md" mb={4}>
                  <Flex>
                    <Badge colorScheme="green" mr={2}>
                      Pós Compra
                    </Badge>
                    <Text fontSize="sm">
                      Enviado quando um cupom é gerado após a entrega do pedido
                    </Text>
                  </Flex>
                </Box>

                <FormControl isInvalid={!!errors.creationMessageTemplateId}>
                  <FormLabel>Template para envio</FormLabel>
                  <Controller
                    name="creationMessageTemplateId"
                    control={control}
                    render={({ field }) => (
                      <Select
                        placeholder="Selecione um template"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          handleTemplateChange(e);
                        }}
                      >
                        {templates?.map((template) => (
                          <option key={template.id} value={template.id}>
                            {template.name}
                          </option>
                        ))}
                      </Select>
                    )}
                  />
                  <FormErrorMessage>
                    {errors.creationMessageTemplateId?.message}
                  </FormErrorMessage>
                </FormControl>
              </TabPanel>

              <TabPanel>
                <Box p={4} bg="gray.50" borderRadius="md" mb={4}>
                  <Flex>
                    <Badge colorScheme="yellow" mr={2}>
                      Prestes a expirar
                    </Badge>
                    <Text fontSize="sm">
                      Enviado antes do cupom expirar, se não foi utilizado
                    </Text>
                  </Flex>
                </Box>

                <FormControl isInvalid={!!errors.isReminderEnabled} mb={4}>
                  <Flex align="center">
                    <FormLabel mb="0">Ativar lembretes de Cashback</FormLabel>
                    <Controller
                      name="isReminderEnabled"
                      control={control}
                      render={({ field: { onChange, value, ref } }) => (
                        <Switch
                          isChecked={value}
                          onChange={(e) => onChange(e.target.checked)}
                          ref={ref}
                          colorScheme="green"
                          size="md"
                        />
                      )}
                    />
                  </Flex>
                  <FormHelperText>
                    Quando ativado, enviará lembretes aos clientes sobre seus
                    cupons de cashback
                  </FormHelperText>
                </FormControl>

                {isReminderEnabled && (
                  <>
                    <FormControl
                      flex="1"
                      width="50%"
                      mb={4}
                      isInvalid={!!errors.reminderDays}
                    >
                      <FormLabel>Dias antes da expiração</FormLabel>
                      <Controller
                        name="reminderDays"
                        control={control}
                        render={({ field }) => (
                          <NumberInput
                            {...field}
                            min={1}
                            max={30}
                            maxW="200px"
                            onChange={(valueString) =>
                              field.onChange(parseInt(valueString))
                            }
                          >
                            <NumberInputField />
                          </NumberInput>
                        )}
                      />
                      <FormHelperText>
                        Quantos dias antes da expiração enviar lembrete
                      </FormHelperText>
                      <FormErrorMessage>
                        {errors.reminderDays?.message}
                      </FormErrorMessage>
                    </FormControl>

                    <FormControl isInvalid={!!errors.reminderMessageTemplateId}>
                      <FormLabel>Template para envio</FormLabel>
                      <Controller
                        name="reminderMessageTemplateId"
                        control={control}
                        render={({ field }) => (
                          <Select
                            placeholder="Selecione um template"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              handleTemplateChange(e);
                            }}
                          >
                            {templates?.map((template) => (
                              <option key={template.id} value={template.id}>
                                {template.name}
                              </option>
                            ))}
                          </Select>
                        )}
                      />
                      <FormErrorMessage>
                        {errors.reminderMessageTemplateId?.message}
                      </FormErrorMessage>
                    </FormControl>
                  </>
                )}
              </TabPanel>

              <TabPanel>
                <Box p={4} bg="gray.50" borderRadius="md" mb={4}>
                  <Flex>
                    <Badge colorScheme="red" mr={2}>
                      Último dia
                    </Badge>
                    <Text fontSize="sm">
                      Enviado no último dia antes do cupom expirar, se não foi
                      utilizado
                    </Text>
                  </Flex>
                </Box>

                <FormControl isInvalid={!!errors.lastDayMessageTemplateId}>
                  <FormLabel>Template para envio</FormLabel>
                  <Controller
                    name="lastDayMessageTemplateId"
                    control={control}
                    render={({ field }) => (
                      <Select
                        placeholder="Selecione um template"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          handleTemplateChange(e);
                        }}
                      >
                        {templates?.map((template) => (
                          <option key={template.id} value={template.id}>
                            {template.name}
                          </option>
                        ))}
                      </Select>
                    )}
                  />
                  <FormErrorMessage>
                    {errors.lastDayMessageTemplateId?.message}
                  </FormErrorMessage>
                </FormControl>
              </TabPanel>
            </TabPanels>
          </Tabs>
          <FormControl
            flex="1"
            isInvalid={!!errors.dailyMessageLimitOnWhatsapp}
          >
            <FormLabel>Limite diário de envio</FormLabel>
            <Controller
              name="dailyMessageLimitOnWhatsapp"
              control={control}
              render={({ field }) => (
                <NumberInput
                  min={0}
                  maxW="200px"
                  onChange={(valueString) =>
                    field.onChange(
                      valueString === '' ? undefined : parseFloat(valueString),
                    )
                  }
                  value={field.value ?? undefined}
                >
                  <NumberInputField />
                </NumberInput>
              )}
            />

            <FormHelperText>Limite diário de envios dessa etapa</FormHelperText>
            <FormErrorMessage>
              {errors.dailyMessageLimitOnWhatsapp?.message}
            </FormErrorMessage>
          </FormControl>
        </Box>
      </GridItem>

      <GridItem ml={8} w="100%">
        <Box display="flex" justifyContent="center" alignItems="center">
          {selectedTemplate ? (
            <WhatsappTemplatePreview
              message={selectedTemplate.templateText}
              footer={selectedTemplate.footerText}
              buttons={selectedTemplate.messageTemplateButtons}
              fileUrl={selectedTemplate.mediaUrl}
            />
          ) : (
            <WhatsappTemplatePreview
              message="📩 Escolha o melhor template para essa etapa!  
Personalize sua mensagem e garanta uma comunicação eficaz. 🚀✨"
              footer="Um bom template melhora o engajamento e a conversão! 😉"
              buttons={[]}
              fileUrl={null}
            />
          )}
        </Box>
      </GridItem>
    </Grid>
  );
};

export default CashbackTemplates;
