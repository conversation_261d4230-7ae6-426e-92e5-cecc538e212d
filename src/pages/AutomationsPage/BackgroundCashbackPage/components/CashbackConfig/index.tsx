import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Box,
  VStack,
  FormControl,
  FormLabel,
  FormErrorMessage,
  NumberInput,
  NumberInputField,
  Button,
  HStack,
  FormHelperText,
  Heading,
  Divider,
  Radio,
  RadioGroup,
  Stack,
  Flex,
  Text,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Switch,
} from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  CashbackConfigsService,
  CreateCashbackConfigDto,
  UpdateCashbackConfigDto,
} from '../../../../../services/cashbackconfig.service';
import { apiRoutes } from '../../../../../constants/api-routes';
import useSelectOptionsQuery from '../../../../../hooks/useSelectOptionsQuery';
import { OrdersService } from '../../../../../services/orders.service';
import InputSelect from '../../../../../components/InputSelect';
import { CashbackPlatform } from '../../types/CashbackPlataform';
import CashbackTemplates from '../CashbackTemplates';
import { colors } from '../../../../../constants/colors';
import { MoneyUtils } from '../../../../../utils/money.utils';
import { SourceIntegration } from '../../../../../types/SourceIntegration';

const cashbackSchema = yup.object().shape({
  discountValue: yup
    .number()
    .positive('O valor deve ser positivo')
    .required('Valor do desconto é obrigatório')
    .test(
      'percentage-validation',
      'Porcentagem deve ser um número inteiro entre 1 e 100',
      function (value) {
        const discountType = this.parent.discountType;
        if (
          discountType === 'percentage' &&
          value !== undefined &&
          value !== null
        ) {
          return Number.isInteger(value) && value >= 1 && value <= 100;
        }
        return true;
      },
    )
    .test(
      'fixed-value-validation',
      'Valor fixo deve ser um número inteiro',
      function (value) {
        const discountType = this.parent.discountType;
        if (
          discountType === 'fixed_in_cents' &&
          value !== undefined &&
          value !== null
        ) {
          return Number.isInteger(value);
        }
        return true;
      },
    ),
  discountType: yup
    .string()
    .oneOf(['percentage', 'fixed_in_cents'], 'Tipo de desconto inválido')
    .required('Tipo de desconto é obrigatório'),
  cumulative: yup.boolean().required('Campo obrigatório'),
  minOrderValue: yup
    .number()
    .min(0, 'O valor mínimo não pode ser negativo')
    .required('Valor mínimo obrigatório'),
  maxOrderValue: yup
    .number()
    .min(0, 'O valor máximo não pode ser negativo')
    .transform((value) => (isNaN(value) ? undefined : value))
    .required('Valor maximo obrigatório')
    .test(
      'min-max',
      'Valor máximo deve ser maior que o valor mínimo',
      function (value) {
        return !value || value > this.parent.minOrderValue;
      },
    ),
  hasMinValueToUseCoupon: yup.boolean().default(false),
  minValueToUseCoupon: yup
    .number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .test(
      'min-value-coupon-validation',
      'Valor mínimo para usar cupom é obrigatório quando ativado',
      function (value) {
        const hasMinValueToUseCoupon = this.parent.hasMinValueToUseCoupon;
        if (hasMinValueToUseCoupon) {
          return value !== undefined && value !== null && value > 0;
        }
        return true;
      },
    ),
  integration: yup.string().required('Integração é obrigatória'),
  creationMessageTemplateId: yup
    .string()
    .nullable()
    .required('Template de Pós Compra necessário'),
  reminderMessageTemplateId: yup
    .string()
    .nullable()
    .test(
      'reminder-template-validation',
      'O template de Lembrete é obrigatório se a automação estiver ativa ou se o campo foi modificado',
      function (value) {
        const { isEditing, dirtyFields } = this.options.context || {};
        const isReminderEnabled = this.parent.isReminderEnabled;

        if (!isEditing) return !isReminderEnabled || !!value;

        const fieldIsDirty = dirtyFields?.reminderMessageTemplateId;
        const reminderWasCleared = fieldIsDirty && value === '';

        if ((isReminderEnabled || reminderWasCleared) && fieldIsDirty) {
          return !!value;
        }

        return true;
      },
    )
    .transform((value) => (value === '' ? undefined : value)),
  otherwise: yup.string().nullable(),
  lastDayMessageTemplateId: yup
    .string()
    .nullable()
    .required('Template de Último Dia necessário'),
  reminderDays: yup
    .number()
    .min(1, 'Deve ser pelo menos 1 dia')
    .max(30, 'Não pode ser mais que 30 dias')
    .required('Dias de lembrete são obrigatórios'),
  statusTrigger: yup.string().required('Status de gatilho é obrigatório'),
  isActive: yup.boolean().default(true),
  isReminderEnabled: yup.boolean().default(true),
});

interface CashbackConfigProps {
  integration: CashbackPlatform;
  setTabIndex: React.Dispatch<React.SetStateAction<number>>;
}

const CashbackConfig = ({ integration, setTabIndex }: CashbackConfigProps) => {
  const toast = useToast();
  const queryClient = useQueryClient();

  const defaultValues = {
    discountValue: 0,
    discountType: 'percentage',
    cumulative: false,
    minOrderValue: 0,
    maxOrderValue: 1,
    hasMinValueToUseCoupon: false,
    minValueToUseCoupon: 0,
    integration: integration.id,
    creationMessageTemplateId: '',
    reminderMessageTemplateId: '',
    lastDayMessageTemplateId: '',
    reminderDays: 7,
    statusTrigger: '',
    isActive: false,
    isReminderEnabled: true,
    dailyMessageLimitOnWhatsapp: 100,
  };

  const {
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors, isSubmitting, dirtyFields },
    watch,
  } = useForm({
    resolver: yupResolver(cashbackSchema),
    defaultValues,
  });

  const { data: existingConfig } = useQuery(
    apiRoutes.listCashbackConfig(integration.id),
    async () => {
      const { data } = await CashbackConfigsService.listCashbackConfig(
        integration.id,
      );
      return data;
    },
    {
      onSuccess: (data) => {
        if (data) {
          const formattedConfig = {
            discountValue:
              data.discountType === 'percentage'
                ? data.discountValue
                : MoneyUtils.fromCentsToInt(data.discountValue),
            discountType: data.discountType,
            cumulative: data.cumulative,
            minOrderValue: MoneyUtils.fromCentsToInt(data.minOrderValue),
            maxOrderValue: MoneyUtils.fromCentsToInt(data.maxOrderValue),
            hasMinValueToUseCoupon: data.minValueToUseCoupon !== null,
            minValueToUseCoupon: data.minValueToUseCoupon
              ? MoneyUtils.fromCentsToInt(data.minValueToUseCoupon)
              : 0,
            integration: data.integration,
            creationMessageTemplateId: data.creationMessageTemplateId || '',
            reminderMessageTemplateId: data.reminderMessageTemplateId || '',
            lastDayMessageTemplateId: data.lastDayMessageTemplateId || '',
            reminderDays: data.reminderDays,
            statusTrigger: data.statusTrigger || '',
            isActive: data.isActive,
            isReminderEnabled:
              data.isReminderEnabled !== undefined
                ? data.isReminderEnabled
                : true,
            dailyMessageLimitOnWhatsapp: data.dailyMessageLimitOnWhatsapp,
          };

          reset(formattedConfig as any);
        }
      },
    },
  );

  const createCashbackMutation = useMutation(
    (data: CreateCashbackConfigDto) =>
      CashbackConfigsService.createCashbackConfig(data),
    {
      onSuccess: () => {
        toast({
          title: 'Configuração criada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(
          apiRoutes.listCashbackConfig(integration.id),
        );
      },
    },
  );

  const updateCashbackMutation = useMutation(
    (data: { id: string; config: UpdateCashbackConfigDto }) =>
      CashbackConfigsService.updateCashbackConfig(data.id, data.config),
    {
      onSuccess: () => {
        toast({
          title: 'Configuração atualizada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(
          apiRoutes.listCashbackConfig(integration.id),
        );
      },
    },
  );

  const toggleCashbackMutation = useMutation(
    async (params: { id: string; isActive: boolean }) => {
      const { data } = await CashbackConfigsService.toggleCashback(params.id);
      return data;
    },
    {
      onSuccess: (data) => {
        toast({
          title: data.isActive ? 'Cashback ativado' : 'Cashback desativado',
          description: data.isActive
            ? 'A funcionalidade de cashback foi ativada com sucesso.'
            : 'A funcionalidade de cashback foi desativada. Os cupons não serão gerados, e lembretes não serão enviados',
          status: data.isActive ? 'success' : 'info',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries([`cashback-configs/${integration.id}`]);
        setValue('isActive', data.isActive);
      },
    },
  );

  const ordersStatusOptions = useSelectOptionsQuery(
    apiRoutes.listOrderFieldValues('status', integration.id),
    () => OrdersService.listOrderFieldValues('status', integration.id),
    'status',
  );

  const discountType = watch('discountType');
  const isActive = watch('isActive');
  const hasMinValueToUseCoupon = watch('hasMinValueToUseCoupon');

  const supportsUsageRestrictions =
    integration.supportsUsageRestrictions === true;

  const onSubmit = async (formData: any) => {
    const {
      discountType,
      discountValue,
      minOrderValue,
      maxOrderValue,
      hasMinValueToUseCoupon,
      minValueToUseCoupon,
      integration,
      ...restData
    } = formData;

    const processedDiscountValue =
      discountType === 'percentage'
        ? Math.floor(discountValue)
        : MoneyUtils.parseToCents(discountValue);

    const processedMinOrderValue = MoneyUtils.parseToCents(minOrderValue);
    const processedMaxOrderValue = MoneyUtils.parseToCents(maxOrderValue);

    const processedMinValueToUseCoupon =
      hasMinValueToUseCoupon && supportsUsageRestrictions
        ? MoneyUtils.parseToCents(minValueToUseCoupon)
        : null;

    const processedData = {
      ...restData,
      discountType,
      discountValue: processedDiscountValue,
      minOrderValue: processedMinOrderValue,
      maxOrderValue: processedMaxOrderValue,
      minValueToUseCoupon: processedMinValueToUseCoupon,
      integration,
    };

    if (existingConfig) {
      const changedValues = Object.keys(dirtyFields).reduce((acc, key) => {
        if (key === 'hasMinValueToUseCoupon') {
          // Quando o switch muda, sempre incluir o minValueToUseCoupon
          acc['minValueToUseCoupon'] = processedMinValueToUseCoupon;
        } else if (key === 'minValueToUseCoupon') {
          // Incluir o valor processado quando o campo foi alterado
          acc[key] = processedMinValueToUseCoupon;
        } else {
          acc[key] = processedData[key];
        }
        return acc;
      }, {} as any);

      changedValues.integration = processedData.integration;

      await updateCashbackMutation.mutate({
        id: existingConfig.id,
        config: changedValues,
      });
    } else {
      await createCashbackMutation.mutate(processedData);
    }
  };

  const handleToggle = () => {
    if (existingConfig) {
      toggleCashbackMutation.mutate({
        id: existingConfig.id,
        isActive: !isActive,
      });
    } else {
      toast({
        title: 'Configuração não encontrada',
        description: 'É necessário salvar a configuração antes de ativá-la.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleCancel = () => {
    if (existingConfig) {
      reset();
    } else {
      reset(defaultValues);
    }
    setTabIndex(0);
  };

  return (
    <Box p={5}>
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Box>
          <Heading size="lg">
            Configuração de Cashback - {integration.name}
          </Heading>
          <Text>Gerencie as configurações de cashback automático</Text>
        </Box>
        <Box textAlign="center">
          <Switch
            isChecked={isActive}
            isDisabled={!existingConfig}
            colorScheme="green"
            onChange={handleToggle}
          />
        </Box>
      </Flex>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Tabs variant="enclosed">
          <TabList>
            <Tab>Configuração de Desconto</Tab>
            <Tab>Restrições Gerais</Tab>
            <Tab>Templates de Notificação</Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <VStack spacing={6} align="flex-start" width="100%">
                <Alert
                  status="info"
                  variant="subtle"
                  flexDirection="column"
                  alignItems="flex-start"
                  borderRadius="md"
                  width="100%"
                  color={colors.fontDark}
                  colorScheme="blue"
                  p={5}
                >
                  <Flex align="center" mb={3}>
                    <AlertIcon boxSize={5} />
                    <AlertTitle fontWeight="semibold" fontSize="md" ml={2}>
                      Recomendações para Cashback Efetivo
                    </AlertTitle>
                  </Flex>

                  <AlertDescription width="100%">
                    <VStack align="flex-start" spacing={3}>
                      <Text fontSize="sm">
                        <strong>• Percentual ideal:</strong> 5% a 15% para
                        maximizar o retorno dos clientes.
                      </Text>
                      <Text fontSize="sm">
                        <strong>• Cuidado com excessos:</strong> Descontos acima
                        de 20% podem comprometer sua margem de lucro.
                      </Text>
                      <Text fontSize="sm">
                        <strong>• Dados de mercado:</strong> Cupons percentuais
                        têm conversão 25% superior aos de valor fixo.
                      </Text>
                    </VStack>
                  </AlertDescription>
                </Alert>

                <Flex width="100%" gap={4} flexDirection="row">
                  <FormControl
                    flex={1}
                    isRequired
                    isInvalid={!!errors.discountType}
                  >
                    <FormLabel>Tipo de Desconto</FormLabel>
                    <Controller
                      name="discountType"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup {...field}>
                          <Stack direction="row">
                            <Radio value="percentage">Percentual (%)</Radio>
                            <Radio value="fixed_in_cents">
                              Valor fixo (R$)
                            </Radio>
                          </Stack>
                        </RadioGroup>
                      )}
                    />
                    <FormErrorMessage>
                      {errors.discountType?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl
                    flex={1}
                    isRequired
                    isInvalid={!!errors.discountValue}
                  >
                    <FormLabel>
                      Valor do Desconto{' '}
                      {discountType === 'percentage' ? '(%)' : '(R$)'}
                    </FormLabel>
                    <Controller
                      name="discountValue"
                      control={control}
                      render={({ field }) => (
                        <NumberInput
                          {...field}
                          min={1}
                          step={1}
                          precision={0}
                          onChange={(valueString) => {
                            const value = parseFloat(valueString) || 0;
                            field.onChange(Math.floor(value));
                          }}
                        >
                          <NumberInputField />
                        </NumberInput>
                      )}
                    />
                    <FormHelperText>
                      {discountType === 'percentage'
                        ? 'Valor em porcentagem (apenas números inteiros de 1 a 100)'
                        : 'Valor fixo em reais (apenas números inteiros, ex: 10)'}
                    </FormHelperText>
                    <FormErrorMessage>
                      {errors.discountValue?.message}
                    </FormErrorMessage>
                  </FormControl>
                </Flex>
              </VStack>
            </TabPanel>

            <TabPanel>
              <VStack spacing={6} align="flex-start" width="100%">
                <Flex
                  width="100%"
                  gap={8}
                  justifyContent="space-between"
                  flexWrap="wrap"
                >
                  <FormControl flex="1" isInvalid={!!errors.minOrderValue}>
                    <FormLabel>Valor mínimo para gerar cupom (R$)</FormLabel>
                    <Controller
                      name="minOrderValue"
                      control={control}
                      render={({ field }) => (
                        <NumberInput
                          {...field}
                          min={0}
                          maxW="200px"
                          step={0.01}
                          precision={2}
                          onChange={(valueString) =>
                            field.onChange(parseFloat(valueString))
                          }
                        >
                          <NumberInputField />
                        </NumberInput>
                      )}
                    />
                    <FormHelperText>
                      Valor mínimo que o pedido deve ter para gerar um cupom de
                      cashback
                    </FormHelperText>
                    <FormErrorMessage>
                      {errors.minOrderValue?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl flex="1" isInvalid={!!errors.maxOrderValue}>
                    <FormLabel>Valor máximo para gerar cupom (R$)</FormLabel>
                    <Controller
                      name="maxOrderValue"
                      control={control}
                      render={({ field }) => (
                        <NumberInput
                          min={0}
                          maxW="200px"
                          step={0.01}
                          precision={2}
                          onChange={(valueString) =>
                            field.onChange(
                              valueString === ''
                                ? undefined
                                : parseFloat(valueString),
                            )
                          }
                          value={field.value ?? undefined}
                        >
                          <NumberInputField />
                        </NumberInput>
                      )}
                    />

                    <FormHelperText>
                      Valor máximo que o pedido deve ter para gerar um cupom de
                      cashback
                    </FormHelperText>
                    <FormErrorMessage>
                      {errors.maxOrderValue?.message}
                    </FormErrorMessage>
                  </FormControl>
                </Flex>

                {supportsUsageRestrictions && (
                  <Box width="100%" p={4} borderRadius="md" bg="gray.50">
                    <VStack spacing={4} align="flex-start">
                      <Flex alignItems="center" gap={3}>
                        <Controller
                          name="hasMinValueToUseCoupon"
                          control={control}
                          render={({ field }) => (
                            <Switch
                              isChecked={field.value}
                              onChange={field.onChange}
                              colorScheme="blue"
                            />
                          )}
                        />
                        <FormLabel mb={0} fontWeight="semibold">
                          Definir valor mínimo para usar o cupom
                        </FormLabel>
                      </Flex>

                      {hasMinValueToUseCoupon && (
                        <FormControl isInvalid={!!errors.minValueToUseCoupon}>
                          <FormLabel>
                            Valor mínimo para usar cupom (R$)
                          </FormLabel>
                          <Controller
                            name="minValueToUseCoupon"
                            control={control}
                            render={({ field }) => (
                              <NumberInput
                                {...field}
                                min={0}
                                maxW="200px"
                                step={0.01}
                                precision={2}
                                onChange={(valueString) =>
                                  field.onChange(parseFloat(valueString))
                                }
                              >
                                <NumberInputField />
                              </NumberInput>
                            )}
                          />
                          <FormHelperText>
                            Valor mínimo que o cliente deve gastar para poder
                            usar o cupom de cashback
                          </FormHelperText>
                          <FormErrorMessage>
                            {errors.minValueToUseCoupon?.message}
                          </FormErrorMessage>
                        </FormControl>
                      )}
                    </VStack>
                  </Box>
                )}

                <Flex
                  width="100%"
                  gap={8}
                  justifyContent="space-between"
                  flexWrap="wrap"
                >
                  <FormControl flex="1" isInvalid={!!errors.cumulative}>
                    <FormLabel>Acúmulo de Desconto</FormLabel>
                    <Controller
                      name="cumulative"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup
                          value={field.value ? '1' : '0'}
                          onChange={(value) => field.onChange(value === '1')}
                        >
                          <Stack direction="row" spacing={6}>
                            <Radio value="0">Não acumulável</Radio>
                            <Radio value="1">Acumulável</Radio>
                          </Stack>
                        </RadioGroup>
                      )}
                    />
                    <FormHelperText>
                      Para cashback, recomendamos não permitir acúmulo
                    </FormHelperText>
                    <FormErrorMessage>
                      {errors.cumulative?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl flex="1" isInvalid={!!errors.statusTrigger}>
                    <FormLabel>Status do pedido</FormLabel>
                    <Controller
                      name="statusTrigger"
                      control={control}
                      render={({ field }) => (
                        <InputSelect
                          options={ordersStatusOptions || []}
                          value={
                            field.value
                              ? [{ value: field.value, label: field.value }]
                              : undefined
                          }
                          onChange={(option) => field.onChange(option.value)}
                          placeholder="Selecione um status de pedido"
                        />
                      )}
                    />
                    <FormErrorMessage>
                      {errors.statusTrigger?.message}
                    </FormErrorMessage>
                  </FormControl>
                </Flex>
              </VStack>
            </TabPanel>

            <TabPanel>
              <CashbackTemplates
                control={control}
                errors={errors}
                watch={watch}
              />
            </TabPanel>
          </TabPanels>
        </Tabs>

        <Divider my={6} />

        <Flex justifyContent="space-between">
          <HStack spacing={4}>
            <Button
              variant="outline"
              colorScheme="gray"
              onClick={handleCancel}
              isDisabled={isSubmitting}
            >
              Cancelar
            </Button>
          </HStack>

          <HStack spacing={4}>
            <Button
              type="submit"
              variant="primary"
              isLoading={isSubmitting}
              loadingText="Salvando..."
            >
              {existingConfig
                ? 'Atualizar Configurações'
                : 'Criar Configurações'}
            </Button>
          </HStack>
        </Flex>
      </form>
    </Box>
  );
};

export default CashbackConfig;
