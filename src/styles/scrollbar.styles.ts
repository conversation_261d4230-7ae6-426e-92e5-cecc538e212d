import { colors } from '../constants/colors';

type ScrollbarStyleOptions = {
  width?: string;
  height?: string;
  background?: string;
};

export const scrollbarStyles = ({
  width = '0',
  height,
  background = colors.darkGrey,
}: ScrollbarStyleOptions = {}) => {
  background = background || colors.darkGrey;
  return {
    '&::-webkit-scrollbar': {
      width,
      ...(height && { height }),
    },
    '&::-webkit-scrollbar-track': {
      background: colors.white,
    },
    '&::-webkit-scrollbar-thumb': {
      background,
      borderRadius: '24px',
    },
  };
};
