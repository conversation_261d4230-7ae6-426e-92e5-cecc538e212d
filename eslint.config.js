const tseslint = require('@typescript-eslint/eslint-plugin');
const tsParser = require('@typescript-eslint/parser');
const reactPlugin = require('eslint-plugin-react');

module.exports = [
  {
    files: ['**/*.{ts,tsx}'], // Lint all TypeScript files
    languageOptions: {
      ecmaVersion: 'latest', // Modern JS
      sourceType: 'module', // Enable ES modules
      parser: tsParser, // TypeScript parser
      parserOptions: {
        project: './tsconfig.json', // Point to tsconfig.json
      },
    },
    plugins: {
      '@typescript-eslint': tseslint,
      react: reactPlugin,
    },
    rules: {
      // React Rules
      'react/jsx-uses-react': 'off',
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',

      // TypeScript Rules
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn',

      // General Rules
      quotes: ['error', 'single'],
      'no-console': 'warn',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
];
